"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.raw(`
    -- Extension for UUID generation
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    -- Extension for vector operations (commented out due to environment issues)
    -- CREATE EXTENSION IF NOT EXISTS "vector";

    -- Table: Tenants
    -- Represents different organizations or clients in the multi-tenant system.
    -- Each tenant has its own isolated data.
    CREATE TABLE Tenants (
        tenant_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_name VARCHAR(255) NOT NULL UNIQUE,
        schema_name VARCHAR(255) NOT NULL UNIQUE, -- For schema-based isolation if chosen, or logical separation
        status VARCHAR(50) DEFAULT 'active' NOT NULL, -- active, inactive, suspended
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP WITH TIME ZONE -- For soft deletion
    );

    -- Table: Users
    -- Stores user information, linked to a specific tenant.
    -- Includes PAIM tier, cultural profile, and authentication details.
    CREATE TABLE Users (
        user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        paim_tier VARCHAR(50) NOT NULL, -- System Admin, Company Admin, Power User, Personal
        cultural_profile JSONB, -- Stores cultural preferences, language, dialect, etc.
        status VARCHAR(50) DEFAULT 'active' NOT NULL, -- active, inactive, suspended
        last_login TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP WITH TIME ZONE, -- For soft deletion
        CONSTRAINT fk_user_tenant FOREIGN KEY (tenant_id) REFERENCES Tenants(tenant_id)
    );

    -- Table: Roles
    -- Defines roles within the system, linked to a specific tenant.
    CREATE TABLE Roles (
        role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        role_name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_role_tenant FOREIGN KEY (tenant_id) REFERENCES Tenants(tenant_id),
        CONSTRAINT uq_tenant_role UNIQUE (tenant_id, role_name)
    );

    -- Table: UserRoles
    -- Junction table for many-to-many relationship between Users and Roles.
    CREATE TABLE UserRoles (
        user_id UUID NOT NULL REFERENCES Users(user_id) ON DELETE CASCADE,
        role_id UUID NOT NULL REFERENCES Roles(role_id) ON DELETE CASCADE,
        assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (user_id, role_id)
    );

    -- Table: Permissions
    -- Defines specific permissions that can be granted to roles.
    CREATE TABLE Permissions (
        permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        permission_name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: RolePermissions
    -- Junction table for many-to-many relationship between Roles and Permissions.
    CREATE TABLE RolePermissions (
        role_id UUID NOT NULL REFERENCES Roles(role_id) ON DELETE CASCADE,
        permission_id UUID NOT NULL REFERENCES Permissions(permission_id) ON DELETE CASCADE,
        granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (role_id, permission_id)
    );

    -- Indexes for performance optimization
    CREATE INDEX idx_tenants_status ON Tenants(status);
    CREATE INDEX idx_users_tenant_id ON Users(tenant_id);
    CREATE INDEX idx_users_email ON Users(email);
    CREATE INDEX idx_users_paim_tier ON Users(paim_tier);
    CREATE INDEX idx_roles_tenant_id ON Roles(tenant_id);
    CREATE INDEX idx_userroles_user_id ON UserRoles(user_id);
    CREATE INDEX idx_userroles_role_id ON UserRoles(role_id);
    CREATE INDEX idx_rolepermissions_role_id ON RolePermissions(role_id);
    CREATE INDEX idx_rolepermissions_permission_id ON RolePermissions(permission_id);
  `);
}
async function down(knex) {
    await knex.raw(`
    DROP TABLE IF EXISTS RolePermissions;
    DROP TABLE IF EXISTS Permissions;
    DROP TABLE IF EXISTS UserRoles;
    DROP TABLE IF EXISTS Roles;
    DROP TABLE IF EXISTS Users;
    DROP TABLE IF EXISTS Tenants;
    -- DROP EXTENSION IF EXISTS "vector"; -- Commented out due to environment issues
    DROP EXTENSION IF EXISTS "uuid-ossp";
  `);
}
