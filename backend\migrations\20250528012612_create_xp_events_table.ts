import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('xp_events', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('org_id').notNullable();
    table.uuid('agent_id');
    table.decimal('powerops').notNullable();
    table.integer('xp_gained').notNullable();
    table.string('reason').notNullable();
    table.jsonb('metadata');
    table.timestamp('timestamp').notNullable().defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('xp_events');
}