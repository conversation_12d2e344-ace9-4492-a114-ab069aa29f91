"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticate = void 0;
const authUtils = __importStar(require("../auth/auth.utils")); // Assuming auth.utils has token verification logic
const errors_1 = require("../utils/errors");
const authenticate = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next(new errors_1.AuthorizationError('Authentication failed: No token provided or invalid format.', 401));
        }
        const token = authHeader.split(' ')[1];
        if (!token) {
            return next(new errors_1.AuthorizationError('Authentication failed: Token missing.', 401));
        }
        const decoded = authUtils.verifyToken(token);
        req.user = decoded; // Attach the decoded payload to the request object
        next();
    }
    catch (error) {
        if (error.name === 'TokenExpiredError') {
            return next(new errors_1.AuthorizationError('Authentication failed: Token expired.', 401));
        }
        if (error.name === 'JsonWebTokenError') {
            return next(new errors_1.AuthorizationError('Authentication failed: Invalid token.', 401));
        }
        next(new errors_1.AuthorizationError('Authentication failed: Unknown error.', 401));
    }
};
exports.authenticate = authenticate;
