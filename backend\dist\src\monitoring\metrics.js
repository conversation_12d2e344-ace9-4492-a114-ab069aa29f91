"use strict";
// backend/src/monitoring/metrics.ts
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetMetrics = exports.getMetrics = exports.incrementMetric = void 0;
const logger_1 = __importDefault(require("../config/logger"));
const metrics = {
    api_requests_total: 0,
    api_errors_total: 0,
    db_queries_total: 0,
    db_query_errors_total: 0,
    // Add more metrics as needed
};
const incrementMetric = (metricName, value = 1) => {
    if (metrics.hasOwnProperty(metricName)) {
        metrics[metricName] += value;
    }
    else {
        logger_1.default.warn(`Attempted to increment unknown metric: ${metricName}`);
    }
};
exports.incrementMetric = incrementMetric;
const getMetrics = () => {
    return { ...metrics }; // Return a copy to prevent external modification
};
exports.getMetrics = getMetrics;
const resetMetrics = () => {
    for (const key in metrics) {
        if (metrics.hasOwnProperty(key)) {
            metrics[key] = 0;
        }
    }
    logger_1.default.info('Metrics reset.');
};
exports.resetMetrics = resetMetrics;
// Example of how to use:
// In your API middleware: incrementMetric('api_requests_total');
// In your error handler: incrementMetric('api_errors_total');
// In your database adapter: incrementMetric('db_queries_total');
// In your database adapter error handling: incrementMetric('db_query_errors_total');
