services:

  db:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_DB: theaigency_db
      POSTGRES_USER: theaigency_prod
      POSTGRES_PASSWORD: prod_password
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  qdrant:
    image: qdrant/qdrant:latest
    restart: always
    ports:
      - "6335:6333" # REST API
      - "6334:6334" # gRPC
    volumes:
      - qdrant_data:/qdrant/storage

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    ports:
      - "3000:3000"  # Main API port
      - "3001:3001"  # Agent Framework API port
      - "3002:3002"  # WebSocket port for real-time agent communication
    environment:
      NODE_ENV: development
      DATABASE_URL: ************************************************/theaigency_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your_jwt_secret_key # Replace with a strong secret in production
      QDRANT_URL: http://qdrant:6333 # Qdrant URL for backend
      # Agent Framework Configuration
      AGENT_FRAMEWORK_ENABLED: "true"
      AGENT_FRAMEWORK_LOG_LEVEL: "info"
      AGENT_FRAMEWORK_MAX_CONCURRENCY: "10"
      # API Rate Limiting for CL-API Agent
      CL_API_RATE_LIMIT_ENABLED: "true"
      CL_API_RATE_LIMIT_RPM: "100"
      CL_API_RATE_LIMIT_BURST: "10"
      # R1-Logic Agent Configuration
      R1_LOGIC_CACHE_TIMEOUT: "300000"
      # DevOps Agent Configuration
      DEVOPS_ALERT_CPU_THRESHOLD: "80"
      DEVOPS_ALERT_MEMORY_THRESHOLD: "85"
      DEVOPS_ALERT_DISK_THRESHOLD: "90"
      DEVOPS_ALERT_RESPONSE_TIME_THRESHOLD: "5000"
    depends_on:
      - db
      - redis
      - qdrant
    volumes:
      - ./backend:/app/backend
      - /app/backend/node_modules # Anonymous volume to prevent host node_modules from overwriting container's
    command: ["npm", "run", "dev"] # Use npm run dev for hot reloading

volumes:
  db_data:
  redis_data:
  qdrant_data: