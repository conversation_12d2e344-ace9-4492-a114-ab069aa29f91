"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllTeamsQuerySchema = exports.getAllWorkspacesQuerySchema = exports.getAllNotificationsQuerySchema = exports.getAllTasksQuerySchema = exports.getAllWorkflowsQuerySchema = exports.updateTeamSchema = exports.createTeamSchema = exports.updateWorkspaceSchema = exports.createWorkspaceSchema = exports.delegateTaskSchema = exports.shareWorkflowSchema = exports.createNotificationSchema = exports.crossTenantMessageSchema = exports.userIdBodySchema = exports.startCollaborationSessionSchema = exports.updateTaskSchema = exports.createTaskSchema = exports.updateWorkflowSchema = exports.createWorkflowSchema = void 0;
const joi_1 = __importDefault(require("joi"));
// Joi schema for CreateWorkflowRequest
exports.createWorkflowSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).required(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    ownerId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    paimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    status: joi_1.default.string().valid('active', 'inactive', 'draft').optional(),
    definition: joi_1.default.object().required(),
});
// Joi schema for UpdateWorkflowRequest
exports.updateWorkflowSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).optional(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    status: joi_1.default.string().valid('active', 'inactive', 'draft').optional(),
    definition: joi_1.default.object().optional(),
});
// Joi schema for CreateTaskRequest
exports.createTaskSchema = joi_1.default.object({
    title: joi_1.default.string().trim().min(3).max(200).required(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    status: joi_1.default.string().valid('pending', 'in_progress', 'completed', 'cancelled').required(),
    priority: joi_1.default.string().valid('low', 'medium', 'high', 'critical').required(),
    assignedToId: joi_1.default.string().guid({ version: 'uuidv4' }).optional().allow(null, ''),
    assignedToType: joi_1.default.string().valid('user', 'agent').optional(),
    dueDate: joi_1.default.string().isoDate().optional().allow(null, ''),
    workflowId: joi_1.default.string().guid({ version: 'uuidv4' }).optional().allow(null, ''),
});
// Joi schema for UpdateTaskRequest
exports.updateTaskSchema = joi_1.default.object({
    title: joi_1.default.string().trim().min(3).max(200).optional(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    status: joi_1.default.string().valid('pending', 'in_progress', 'completed', 'cancelled').optional(),
    priority: joi_1.default.string().valid('low', 'medium', 'high', 'critical').optional(),
    assignedToId: joi_1.default.string().guid({ version: 'uuidv4' }).optional().allow(null, ''),
    assignedToType: joi_1.default.string().valid('user', 'agent').optional(),
    dueDate: joi_1.default.string().isoDate().optional().allow(null, ''),
});
// Joi schema for StartCollaborationSessionRequest
exports.startCollaborationSessionSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).required(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    ownerId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    paimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    initialParticipants: joi_1.default.array().items(joi_1.default.string().guid({ version: 'uuidv4' })).optional(),
    workflowId: joi_1.default.string().guid({ version: 'uuidv4' }).optional().allow(null, ''),
    sessionType: joi_1.default.string().optional().allow(null, ''), // Assuming sessionType is a string
});
// Joi schema for Join/Leave Collaboration Session (userId in body)
exports.userIdBodySchema = joi_1.default.object({
    userId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
});
// Joi schema for CrossTenantMessageRequest
exports.crossTenantMessageSchema = joi_1.default.object({
    senderPaimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    recipientPaimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    messageContent: joi_1.default.string().min(1).max(1000).required(),
    messageType: joi_1.default.string().valid('text', 'notification', 'data_request').optional(),
    relatedTaskId: joi_1.default.string().guid({ version: 'uuidv4' }).optional().allow(null, ''),
});
// Joi schema for CreateNotificationRequest
exports.createNotificationSchema = joi_1.default.object({
    userId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    type: joi_1.default.string().required(),
    message: joi_1.default.string().min(1).max(1000).required(),
});
// Joi schema for ShareWorkflowRequest
exports.shareWorkflowSchema = joi_1.default.object({
    workflowId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    sharedWithId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    sharedWithEntityType: joi_1.default.string().valid('user', 'team', 'paim_instance').required(),
    permissionLevel: joi_1.default.string().valid('view', 'edit', 'manage').required(),
});
// Joi schema for DelegateTaskRequest
exports.delegateTaskSchema = joi_1.default.object({
    delegateToId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    delegateToEntityType: joi_1.default.string().valid('user', 'agent').required(),
    escalationPath: joi_1.default.array().items(joi_1.default.string()).optional(),
});
// Joi schema for CreateWorkspaceRequest
exports.createWorkspaceSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).required(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    ownerId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    paimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    initialMembers: joi_1.default.array().items(joi_1.default.object({
        userId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
        role: joi_1.default.string().required(),
    })).optional(),
});
// Joi schema for UpdateWorkspaceRequest
exports.updateWorkspaceSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).optional(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    members: joi_1.default.array().items(joi_1.default.object({
        userId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
        role: joi_1.default.string().required(),
    })).optional(),
});
// Joi schema for CreateTeamRequest
exports.createTeamSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).required(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    paimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    initialMembers: joi_1.default.array().items(joi_1.default.object({
        userId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
        role: joi_1.default.string().required(),
    })).optional(),
});
// Joi schema for UpdateTeamRequest
exports.updateTeamSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).optional(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    members: joi_1.default.array().items(joi_1.default.object({
        userId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
        role: joi_1.default.string().required(),
    })).optional(),
});
// Joi schema for query parameters for getAllWorkflows
exports.getAllWorkflowsQuerySchema = joi_1.default.object({
    status: joi_1.default.string().valid('active', 'inactive', 'draft').optional(),
    page: joi_1.default.number().integer().min(1).default(1),
    size: joi_1.default.number().integer().min(1).max(100).default(10),
    sort: joi_1.default.string().optional(),
});
// Joi schema for query parameters for getAllTasks
exports.getAllTasksQuerySchema = joi_1.default.object({
    status: joi_1.default.string().valid('pending', 'in_progress', 'completed', 'cancelled').optional(),
    assignedTo: joi_1.default.string().guid({ version: 'uuidv4' }).optional(),
    page: joi_1.default.number().integer().min(1).default(1),
    size: joi_1.default.number().integer().min(1).max(100).default(10),
    sort: joi_1.default.string().optional(),
});
// Joi schema for query parameters for getAllNotifications
exports.getAllNotificationsQuerySchema = joi_1.default.object({
    userId: joi_1.default.string().guid({ version: 'uuidv4' }).optional(),
    read: joi_1.default.boolean().optional(),
    page: joi_1.default.number().integer().min(1).default(1),
    size: joi_1.default.number().integer().min(1).max(100).default(10),
    sort: joi_1.default.string().optional(),
});
// Joi schema for query parameters for getAllWorkspaces
exports.getAllWorkspacesQuerySchema = joi_1.default.object({
    ownerId: joi_1.default.string().guid({ version: 'uuidv4' }).optional(),
    paimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).optional(),
    page: joi_1.default.number().integer().min(1).default(1),
    size: joi_1.default.number().integer().min(1).max(100).default(10),
    sort: joi_1.default.string().optional(),
});
// Joi schema for query parameters for getAllTeams
exports.getAllTeamsQuerySchema = joi_1.default.object({
    paimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).optional(),
    page: joi_1.default.number().integer().min(1).default(1),
    size: joi_1.default.number().integer().min(1).max(100).default(10),
    sort: joi_1.default.string().optional(),
});
