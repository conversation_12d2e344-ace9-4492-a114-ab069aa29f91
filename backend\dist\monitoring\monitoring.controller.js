"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringController = void 0;
const express = __importStar(require("express")); // Import express for types
const monitoring_service_1 = require("./monitoring.service");
const monitoring_repository_1 = require("./monitoring.repository"); // For constructor injection
const audit_service_1 = require("../audit/audit.service"); // For constructor injection
const paim_service_1 = require("../paim/paim.service"); // For constructor injection
const powerops_service_1 = require("../powerops/powerops.service"); // For constructor injection
const cultural_sensitivity_service_1 = require("../cultural-sensitivity/cultural-sensitivity.service"); // For constructor injection
const db_1 = __importDefault(require("../database/db")); // For CulturalSensitivityService constructor
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger"));
const creative_api_service_1 = require("../creative-api/creative-api.service"); // Import CreativeApiService
const http_client_service_1 = require("../utils/http-client.service"); // Import defaultHttpClient
const type_guards_1 = require("../utils/type-guards");
class MonitoringController {
    router; // Declare router
    monitoringService;
    creativeApiService; // Declare CreativeApiService
    constructor() {
        const monitoringRepository = new monitoring_repository_1.MonitoringRepository();
        const auditTrailService = new audit_service_1.AuditTrailService();
        const paimService = new paim_service_1.PaimService(null, auditTrailService);
        const powerOpsService = new powerops_service_1.PowerOpsService();
        const culturalSensitivityService = new cultural_sensitivity_service_1.CulturalSensitivityService(db_1.default);
        this.creativeApiService = new creative_api_service_1.CreativeApiService(auditTrailService); // Initialize CreativeApiService with auditTrailService
        this.monitoringService = new monitoring_service_1.MonitoringService(monitoringRepository, auditTrailService, paimService, powerOpsService, culturalSensitivityService);
        this.router = express.Router(); // Initialize router
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/health', this.getHealthStatus.bind(this));
        this.router.get('/ready', this.getReadinessStatus.bind(this));
        this.router.post('/system-health', this.recordSystemHealth.bind(this));
        this.router.post('/performance-metrics', this.recordPerformanceMetrics.bind(this));
        this.router.get('/system-health/:serviceName', this.getLatestSystemHealth.bind(this));
        this.router.get('/alerts', this.getActiveAlerts.bind(this));
        this.router.get('/escalations', this.getCoveEscalations.bind(this));
        this.router.post('/alerts/trigger', this.triggerManualAlert.bind(this));
        this.router.put('/escalations/:escalationId/resolve', this.resolveCoveEscalation.bind(this));
        this.router.get('/metrics/http-client', this.getHttpClientMetrics.bind(this)); // New HTTP client metrics endpoint
    }
    // Health check endpoint
    getHealthStatus = async (req, res, next) => {
        try {
            const dbStatus = await db_1.default.raw('SELECT 1'); // Check database connectivity
            const creativeApiHealth = await this.creativeApiService.checkProviderHealth(); // Check external API health
            // Basic check for defaultHttpClient
            const httpClientStatus = http_client_service_1.defaultHttpClient ? 'initialized' : 'uninitialized';
            const overallHealth = dbStatus && creativeApiHealth.every(p => p.status === 'healthy') && httpClientStatus === 'initialized' ? 'healthy' : 'degraded';
            const uptime = process.uptime(); // Node.js process uptime
            res.status(200).json({
                status: overallHealth,
                database: dbStatus ? 'connected' : 'disconnected',
                creativeApis: creativeApiHealth,
                httpClient: httpClientStatus, // Add HTTP client status
                uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
                version: process.env.APP_VERSION || '1.0.0', // Assuming APP_VERSION env var
            });
        }
        catch (error) {
            console.error('Health check failed:', error);
            res.status(500).json({ status: 'unhealthy', error: error.message });
        }
    };
    // Readiness check endpoint
    getReadinessStatus = async (req, res, next) => {
        try {
            // For readiness, we might check if all critical services are ready to accept traffic
            // This could involve checking database connections, message queues, etc.
            const dbReady = await db_1.default.raw('SELECT 1').then(() => true).catch(() => false);
            const creativeApiReady = await this.creativeApiService.checkProviderHealth().then(statuses => statuses.every(p => p.status === 'healthy')).catch(() => false);
            const httpClientReady = http_client_service_1.defaultHttpClient ? true : false; // Check if http client is ready
            const isReady = dbReady && creativeApiReady && httpClientReady;
            res.status(isReady ? 200 : 503).json({
                status: isReady ? 'ready' : 'not_ready',
                database: dbReady ? 'ready' : 'not_ready',
                creativeApis: creativeApiReady ? 'ready' : 'not_ready',
                httpClient: httpClientReady ? 'ready' : 'not_ready', // Add HTTP client readiness
            });
        }
        catch (error) {
            console.error('Readiness check failed:', error);
            res.status(503).json({ status: 'not_ready', error: error.message });
        }
    };
    // New endpoint for HTTP client metrics
    getHttpClientMetrics = async (req, res, next) => {
        try {
            const metrics = {
                totalRequests: http_client_service_1.defaultHttpClient.getMetrics().length,
                averageResponseTime: http_client_service_1.defaultHttpClient.getAverageResponseTime(),
                errorRate: http_client_service_1.defaultHttpClient.getErrorRate(),
                // You can add more specific metrics here if needed, e.g., per-endpoint metrics
                // For now, we'll return the raw metrics as well for detailed analysis
                rawMetrics: http_client_service_1.defaultHttpClient.getMetrics(),
            };
            res.status(200).json(metrics);
        }
        catch (error) {
            logger_1.default.error('Failed to retrieve HTTP client metrics:', error);
            next(error);
        }
    };
    // Endpoint to record system health (e.g., from a health check agent)
    recordSystemHealth = async (req, res, next) => {
        try {
            const healthData = req.body;
            const recordedHealth = await this.monitoringService.recordSystemHealth(healthData);
            res.status(201).json(recordedHealth);
        }
        catch (error) {
            next(error);
        }
    };
    // Endpoint to record performance metrics
    recordPerformanceMetrics = async (req, res, next) => {
        try {
            const { serviceName, metrics } = req.body;
            const recordedMetrics = await this.monitoringService.recordPerformanceMetrics(serviceName, metrics);
            res.status(201).json(recordedMetrics);
        }
        catch (error) {
            next(error);
        }
    };
    // Endpoint to get latest system health for a service
    getLatestSystemHealth = async (req, res, next) => {
        try {
            const { serviceName } = req.params;
            const health = await this.monitoringService['repository'].getLatestSystemHealth((0, type_guards_1.requireParam)(serviceName, 'serviceName'));
            if (!health) {
                throw new errors_1.CustomError(`System health for service ${serviceName} not found.`, { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
            }
            res.status(200).json(health);
        }
        catch (error) {
            next(error);
        }
    };
    // Endpoint to get active alerts
    getActiveAlerts = async (req, res, next) => {
        try {
            const alerts = await this.monitoringService['repository'].getActiveMonitoringAlerts();
            res.status(200).json(alerts);
        }
        catch (error) {
            next(error);
        }
    };
    // Endpoint to get Cove escalations
    getCoveEscalations = async (req, res, next) => {
        try {
            const escalations = await this.monitoringService['repository'].getAuditLogs('COVE_ESCALATION_INITIATED', 'CoveEscalation');
            res.status(200).json(escalations);
        }
        catch (error) {
            next(error);
        }
    };
    // Endpoint to manually trigger an alert (for testing/admin)
    triggerManualAlert = async (req, res, next) => {
        try {
            const alertData = req.body;
            const triggeredAlert = await this.monitoringService.triggerAlert(alertData);
            res.status(201).json(triggeredAlert);
        }
        catch (error) {
            next(error);
        }
    };
    // Endpoint to resolve a Cove escalation (e.g., from Cove UI)
    resolveCoveEscalation = async (req, res, next) => {
        try {
            const { escalationId } = req.params;
            const { resolvedBy, details } = req.body;
            if (!resolvedBy) {
                throw new errors_1.CustomError('Resolved by user is required.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
            }
            const resolvedEscalation = await this.monitoringService.resolveCoveEscalation((0, type_guards_1.requireParam)(escalationId, 'escalationId'), resolvedBy, details);
            if (!resolvedEscalation) {
                throw new errors_1.CustomError(`Escalation with ID ${escalationId} not found.`, { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
            }
            res.status(200).json(resolvedEscalation);
        }
        catch (error) {
            next(error);
        }
    };
}
exports.MonitoringController = MonitoringController;
