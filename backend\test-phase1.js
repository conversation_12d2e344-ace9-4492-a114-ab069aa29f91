// Phase 1 Test - Basic server startup test
const express = require('express');

console.log('Phase 1 Test: Testing basic Express server setup...');

const app = express();

// Basic middleware
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'theaigency-backend',
    phase: 'Phase 1 - Core Infrastructure'
  });
});

// Test endpoint
app.get('/test', (req, res) => {
  res.status(200).json({ 
    message: 'Phase 1 basic server is working!',
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 3001;

const server = app.listen(PORT, () => {
  console.log(`✅ Phase 1 Test Server running on port ${PORT}`);
  console.log(`✅ Health check: http://localhost:${PORT}/health`);
  console.log(`✅ Test endpoint: http://localhost:${PORT}/test`);
  
  // Auto-shutdown after 5 seconds for testing
  setTimeout(() => {
    console.log('✅ Phase 1 Test completed successfully!');
    server.close();
    process.exit(0);
  }, 5000);
});

server.on('error', (err) => {
  console.error('❌ Phase 1 Test failed:', err);
  process.exit(1);
});
