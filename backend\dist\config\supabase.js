"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyUserTenant = exports.getUserFromToken = exports.supabaseAdmin = exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
// SUPABASE DISABLED: Make Supabase optional for now
const SUPABASE_ENABLED = process.env.SUPABASE_ENABLED === 'true';
if (SUPABASE_ENABLED && (!supabaseUrl || !supabaseAnonKey)) {
    throw new Error('Missing Supabase environment variables');
}
// Mock Supabase client when disabled
const createMockClient = () => ({
    auth: {
        getUser: () => Promise.resolve({ data: { user: null }, error: new Error('Supabase disabled') }),
    },
    from: () => ({
        select: () => ({
            eq: () => ({
                single: () => Promise.resolve({ data: null, error: new Error('Supabase disabled') }),
            }),
        }),
    }),
});
// Client for user-facing operations (with RLS)
exports.supabase = SUPABASE_ENABLED
    ? (0, supabase_js_1.createClient)(supabaseUrl, supabaseAnonKey, {
        auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: false
        },
        realtime: {
            params: {
                eventsPerSecond: 10
            }
        }
    })
    : createMockClient();
// Admin client for server-side operations (bypasses RLS)
exports.supabaseAdmin = SUPABASE_ENABLED
    ? (0, supabase_js_1.createClient)(supabaseUrl, supabaseServiceKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    })
    : createMockClient();
// Helper function to get user from JWT
const getUserFromToken = async (token) => {
    if (!SUPABASE_ENABLED) {
        console.log('[Supabase] DISABLED: getUserFromToken called');
        throw new Error('Supabase disabled');
    }
    const { data: { user }, error } = await exports.supabase.auth.getUser(token);
    if (error)
        throw error;
    return user;
};
exports.getUserFromToken = getUserFromToken;
// Helper function to verify user belongs to tenant
const verifyUserTenant = async (userId, tenantId) => {
    if (!SUPABASE_ENABLED) {
        console.log('[Supabase] DISABLED: verifyUserTenant called');
        throw new Error('Supabase disabled');
    }
    const { data, error } = await exports.supabaseAdmin
        .from('users')
        .select('tenant_id')
        .eq('id', userId)
        .single();
    if (error)
        throw error;
    return data.tenant_id === tenantId;
};
exports.verifyUserTenant = verifyUserTenant;
