"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.ConflictError = exports.ServiceUnavailableError = exports.CustomError = exports.ExternalServiceError = exports.DatabaseError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.BaseError = void 0;
class BaseError extends Error {
    details;
    constructor(message, details) {
        super(message);
        this.details = details;
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.BaseError = BaseError;
exports.default = BaseError;
class ValidationError extends BaseError {
    statusCode = 400;
    isOperational = true;
    errorCode = 'VALIDATION_ERROR';
}
exports.ValidationError = ValidationError;
class AuthenticationError extends BaseError {
    statusCode = 401;
    isOperational = true;
    errorCode = 'AUTHENTICATION_ERROR';
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends BaseError {
    statusCode = 403;
    isOperational = true;
    errorCode = 'AUTHORIZATION_ERROR';
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends BaseError {
    statusCode = 404;
    isOperational = true;
    errorCode = 'NOT_FOUND_ERROR';
}
exports.NotFoundError = NotFoundError;
class DatabaseError extends BaseError {
    statusCode = 500;
    isOperational = true;
    errorCode = 'DATABASE_ERROR';
}
exports.DatabaseError = DatabaseError;
class ExternalServiceError extends BaseError {
    statusCode = 502;
    isOperational = true;
    errorCode = 'EXTERNAL_SERVICE_ERROR';
}
exports.ExternalServiceError = ExternalServiceError;
class CustomError extends BaseError {
    statusCode = 500;
    isOperational = true;
    errorCode = 'CUSTOM_ERROR';
}
exports.CustomError = CustomError;
class ServiceUnavailableError extends BaseError {
    statusCode = 503;
    isOperational = true;
    errorCode = 'SERVICE_UNAVAILABLE';
}
exports.ServiceUnavailableError = ServiceUnavailableError;
class ConflictError extends BaseError {
    statusCode = 409;
    isOperational = true;
    errorCode = 'CONFLICT_ERROR';
}
exports.ConflictError = ConflictError;
