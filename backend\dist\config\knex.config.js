"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const type_guards_1 = require("../utils/type-guards");
const config = {
    development: {
        client: 'postgresql',
        connection: {
            host: (0, type_guards_1.getEnvVar)(process.env.DB_HOST, 'localhost'),
            port: parseInt((0, type_guards_1.getEnvVar)(process.env.DB_PORT, '5432'), 10),
            user: (0, type_guards_1.requireEnvVar)(process.env.DB_USER, 'DB_USER'),
            password: (0, type_guards_1.requireEnvVar)(process.env.DB_PASSWORD, 'DB_PASSWORD'),
            database: (0, type_guards_1.requireEnvVar)(process.env.DB_NAME, 'DB_NAME'),
        },
        pool: {
            min: 2,
            max: 10
        },
        migrations: {
            tableName: 'knex_migrations',
            directory: '../migrations'
        }
    },
    staging: {
        client: 'postgresql',
        connection: {
            host: (0, type_guards_1.requireEnvVar)(process.env.DB_HOST, 'DB_HOST'),
            port: parseInt((0, type_guards_1.getEnvVar)(process.env.DB_PORT, '5432'), 10),
            user: (0, type_guards_1.requireEnvVar)(process.env.DB_USER, 'DB_USER'),
            password: (0, type_guards_1.requireEnvVar)(process.env.DB_PASSWORD, 'DB_PASSWORD'),
            database: (0, type_guards_1.requireEnvVar)(process.env.DB_NAME, 'DB_NAME'),
        },
        pool: {
            min: 2,
            max: 10
        },
        migrations: {
            tableName: 'knex_migrations',
            directory: '../migrations'
        }
    },
    production: {
        client: 'postgresql',
        connection: {
            host: (0, type_guards_1.requireEnvVar)(process.env.DB_HOST, 'DB_HOST'),
            port: parseInt((0, type_guards_1.getEnvVar)(process.env.DB_PORT, '5432'), 10),
            user: (0, type_guards_1.requireEnvVar)(process.env.DB_USER, 'DB_USER'),
            password: (0, type_guards_1.requireEnvVar)(process.env.DB_PASSWORD, 'DB_PASSWORD'),
            database: (0, type_guards_1.requireEnvVar)(process.env.DB_NAME, 'DB_NAME'),
        },
        pool: {
            min: 2,
            max: 10
        },
        migrations: {
            tableName: 'knex_migrations',
            directory: '../migrations'
        }
    }
};
exports.default = config;
