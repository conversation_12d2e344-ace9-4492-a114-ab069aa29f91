import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('activitylogs', (table) => {
    table.boolean('is_archived').notNullable().defaultTo(false);
  });

  // Placeholder for a function, if needed.
  // Example: await knex.raw(`
  //   CREATE OR REPLACE FUNCTION archive_old_activity_logs()
  //   RETURNS TRIGGER AS $$
  //   BEGIN
  //     UPDATE activitylogs SET is_archived = TRUE WHERE timestamp < NOW() - INTERVAL '90 days';
  //     RETURN NULL;
  //   END;
  //   $$ LANGUAGE plpgsql;
  // `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('activitylogs', (table) => {
    table.dropColumn('is_archived');
  });

  // Placeholder for dropping the function, if it was created.
  // Example: await knex.raw('DROP FUNCTION IF EXISTS archive_old_activity_logs();');
}