# WebSocket Event Specifications for The AIgency and PAIM Backend Services

This document defines the WebSocket event types and their payloads for real-time communication within The AIgency and PAIM backend services.

## 1. General Structure

All WebSocket messages will follow a consistent JSON structure:

```json
{
  "eventType": "string",
  "payload": {
    // Event-specific data
  },
  "timestamp": "ISO 8601 datetime"
}
```

*   `eventType`: A string identifying the type of event.
*   `payload`: A JSON object containing the data relevant to the event.
*   `timestamp`: The time the event was generated in ISO 8601 format.

## 2. Connection Management

### 2.1. `connection_established`

*   **Description:** Sent by the server to confirm a successful WebSocket connection.
*   **Payload:**
    ```json
    {
      "userId": "uuid",
      "sessionId": "uuid",
      "message": "WebSocket connection established successfully."
    }
    ```

### 2.2. `authentication_required`

*   **Description:** Sent by the server if authentication is required after connection.
*   **Payload:**
    ```json
    {
      "message": "Authentication required. Please send an 'authenticate' event with your JWT."
    }
    ```

### 2.3. `authenticate` (Client to Server)

*   **Description:** Sent by the client to authenticate the WebSocket connection with a JWT.
*   **Payload:**
    ```json
    {
      "token": "string" # JWT token
    }
    ```

### 2.4. `authentication_success`

*   **Description:** Sent by the server upon successful authentication.
*   **Payload:**
    ```json
    {
      "userId": "uuid",
      "message": "Authentication successful."
    }
    ```

### 2.5. `authentication_failed`

*   **Description:** Sent by the server if authentication fails.
*   **Payload:**
    ```json
    {
      "message": "Authentication failed. Invalid or expired token.",
      "code": "string" # e.g., "INVALID_TOKEN", "EXPIRED_TOKEN"
    }
    ```

## 3. Event Streaming and Subscriptions

Clients can subscribe to specific event streams. The server will send events to subscribed clients.

### 3.1. `subscribe` (Client to Server)

*   **Description:** Sent by the client to subscribe to an event stream.
*   **Payload:**
    ```json
    {
      "streamType": "string", # e.g., "notifications", "workflow_updates", "agent_performance"
      "filter": {
        # Optional: specific filters for the stream (e.g., "userId", "paimInstanceId", "workflowId")
      }
    }
    ```

### 3.2. `unsubscribe` (Client to Server)

*   **Description:** Sent by the client to unsubscribe from an event stream.
*   **Payload:**
    ```json
    {
      "streamType": "string",
      "filter": {
        # Optional: specific filters for the stream
      }
    }
    ```

### 3.3. `subscription_success`

*   **Description:** Sent by the server to confirm a successful subscription.
*   **Payload:**
    ```json
    {
      "streamType": "string",
      "message": "Successfully subscribed to stream."
    }
    ```

### 3.4. `subscription_failed`

*   **Description:** Sent by the server if a subscription request fails.
*   **Payload:**
    ```json
    {
      "streamType": "string",
      "message": "Failed to subscribe to stream.",
      "code": "string" # e.g., "UNAUTHORIZED", "INVALID_STREAM_TYPE"
    }
    ```

## 4. Real-time Notifications

### 4.1. `new_notification`

*   **Description:** Sent by the server when a new notification is generated for a user or PAIM instance.
*   **Payload:**
    ```json
    {
      "notificationId": "uuid",
      "recipientId": "uuid", # User or PAIM instance ID
      "recipientType": "string", # "user" or "paim_instance"
      "type": "string", # e.g., "alert", "info", "warning"
      "title": "string",
      "message": "string",
      "read": "boolean",
      "createdAt": "ISO 8601 datetime",
      "link": "string" # Optional: URL to related resource
    }
    ```

## 5. Live Collaboration Updates

### 5.1. `collaboration_update`

*   **Description:** Sent by the server to all participants in a collaboration session when an update occurs (e.g., document edit, chat message).
*   **Payload:**
    ```json
    {
      "sessionId": "uuid",
      "updateType": "string", # e.g., "document_edit", "chat_message", "participant_joined", "participant_left"
      "data": {
        # Event-specific data, e.g., for document_edit: { "change": "diff_object", "editorId": "uuid" }
        # for chat_message: { "senderId": "uuid", "message": "string" }
      }
    }
    ```

## 6. Performance Monitoring Streams

### 6.1. `agent_performance_update`

*   **Description:** Sent by the server to provide real-time updates on agent performance metrics.
*   **Payload:**
    ```json
    {
      "agentId": "uuid",
      "metricType": "string", # e.g., "cpu_usage", "task_completion_rate"
      "value": "number",
      "timestamp": "ISO 8601 datetime"
    }
    ```

### 6.2. `system_health_update`

*   **Description:** Sent by the server to provide real-time updates on overall system health or specific component health.
*   **Payload:**
    ```json
    {
      "component": "string", # e.g., "overall", "database", "api_gateway"
      "status": "string", # operational, degraded, critical
      "message": "string",
      "timestamp": "ISO 8601 datetime"
    }
    ```

## 7. PowerOps Usage Streams

### 7.1. `powerops_usage_update`

*   **Description:** Sent by the server to provide real-time updates on PowerOps consumption for a user or PAIM instance.
*   **Payload:**
    ```json
    {
      "entityId": "uuid",
      "entityType": "string", # "user" or "paim_instance"
      "powerOpType": "string", # e.g., "AI_COMPUTATION", "DATA_STORAGE"
      "unitsConsumed": "number",
      "currentTotalUsage": "number",
      "estimatedCost": "number",
      "timestamp": "ISO 8601 datetime"
    }
    ```

## 8. Gamification Updates

### 8.1. `xp_update`

*   **Description:** Sent by the server when a user or PAIM instance gains XP or levels up.
*   **Payload:**
    ```json
    {
      "entityId": "uuid",
      "entityType": "string",
      "newXp": "integer",
      "newLevel": "integer",
      "xpGained": "integer",
      "reason": "string"
    }
    ```

### 8.2. `badge_awarded`

*   **Description:** Sent by the server when a user or PAIM instance is awarded a new badge.
*   **Payload:**
    ```json
    {
      "entityId": "uuid",
      "entityType": "string",
      "badgeId": "uuid",
      "badgeName": "string",
      "description": "string",
      "imageUrl": "string"
    }
    ```

### 8.3. `achievement_unlocked`

*   **Description:** Sent by the server when a user or PAIM instance unlocks an achievement.
*   **Payload:**
    ```json
    {
      "entityId": "uuid",
      "entityType": "string",
      "achievementId": "uuid",
      "achievementName": "string",
      "description": "string"
    }
    ```

### 8.4. `streak_update`

*   **Description:** Sent by the server when a streak is extended or broken.
*   **Payload:**
    ```json
    {
      "entityId": "uuid",
      "entityType": "string",
      "streakId": "uuid",
      "streakName": "string",
      "currentLength": "integer",
      "isBroken": "boolean",
      "message": "string" # e.g., "Daily login streak extended to 7 days!"
    }