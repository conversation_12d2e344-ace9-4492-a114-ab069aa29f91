"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
const logger_1 = __importDefault(require("./config/logger"));
const db_1 = __importDefault(require("./database/db"));
const error_handler_1 = require("./middleware/error-handler");
const monitoring_service_1 = require("./monitoring/monitoring.service"); // Import MonitoringService
const websocket_service_1 = require("./websocket/websocket.service"); // Import WebSocketService
const collaboration_events_1 = require("./websocket/events/collaboration.events"); // Import CollaborationEvents
const notification_service_1 = require("./notifications/notification.service"); // Import NotificationService
const notification_repository_1 = require("./notifications/notification.repository"); // Import NotificationRepository
const collaboration_session_service_1 = require("./collaboration/collaboration-session.service"); // Import CollaborationSessionService
const paim_controller_1 = require("./paim/paim.controller"); // Import PaimController
const powerops_controller_1 = require("./powerops/powerops.controller"); // Import PowerOps controller
const workflow_collaboration_controller_1 = require("./workflow-collaboration/workflow-collaboration.controller"); // Import Workflow Collaboration controller
const workflow_collaboration_service_1 = require("./workflow-collaboration/workflow-collaboration.service"); // Import Workflow Collaboration Service
const workflow_collaboration_repository_1 = require("./workflow-collaboration/workflow-collaboration.repository"); // Import Workflow Collaboration Repository
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
// Security Middleware: Helmet for security headers
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "https://*.supabase.co"], // Adjust as needed for Supabase
        },
    },
    crossOriginEmbedderPolicy: false, // Allow embedding resources from other origins
}));
// CORS Middleware
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));
// Rate Limiting Middleware
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again after 15 minutes',
    handler: (req, res, next) => {
        logger_1.default.warn(`Rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
            status: 'error',
            code: 'TOO_MANY_REQUESTS',
            message: 'Too many requests, please try again later.',
        });
    },
});
app.use('/api/', apiLimiter); // Apply to all API routes
// Body Parser Middleware & Request Metric Increment
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((req, res, next) => {
    monitoringService.incrementMetric('api_requests_total');
    next();
});
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'theaigency-backend'
    });
});
// Database health check
app.get('/health/db', async (req, res) => {
    try {
        monitoringService.incrementMetric('db_queries_total'); // Increment DB query metric
        await db_1.default.raw('SELECT 1');
        res.status(200).json({
            status: 'healthy',
            database: 'connected',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        monitoringService.incrementMetric('db_query_errors_total'); // Increment DB query error metric
        logger_1.default.error('Database health check failed:', error);
        res.status(503).json({
            status: 'unhealthy',
            database: 'disconnected',
            timestamp: new Date().toISOString()
        });
    }
});
// Metrics endpoint
app.get('/health/metrics', (req, res) => {
    res.status(200).json(monitoringService.getMetrics());
});
// Basic API info endpoint
app.get('/api', (req, res) => {
    res.json({
        name: 'TheAIgency Backend API',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString()
    });
});
// Initialize WebSocket Service (needs to be done after HTTP server is created)
let wsService;
// Initialize Collaboration Events
let collaborationEvents;
// Initialize Notification Service
let notificationService;
// Initialize Collaboration Session Service
let collaborationSessionService;
// Initialize Monitoring Service
let monitoringService;
// Initialize Notification Repository
const notificationRepository = new notification_repository_1.NotificationRepository();
// Initialize Workflow Collaboration dependencies
const workflowCollaborationRepository = new workflow_collaboration_repository_1.WorkflowCollaborationRepository();
// workflowCollaborationService will be initialized after wsService is available
let workflowCollaborationService;
let workflowCollaborationController;
// PowerOps Controller will be initialized inside server.listen
let powerOpsController;
// API Routes
// All API endpoints should follow URI versioning (e.g., /api/v1/users) as per API Design Standards.
// TODO: Integrate OpenAPI documentation generation (e.g., using swagger-jsdoc or express-oas-generator)
// Remove the original registration of paim-instances routes as it's moved inside server.listen
// Remove the original registration of powerops routes as it's moved inside server.listen
// Remove the original registration of workflow-collaboration routes as it's moved inside server.listen
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
        method: req.method
    });
});
// Centralized error handler (must be the last middleware)
app.use((err, req, res, next) => {
    monitoringService.incrementMetric('api_errors_total'); // Increment API error metric
    (0, error_handler_1.errorHandler)(err, req, res, next);
});
// Start server
const server = app.listen(PORT, () => {
    logger_1.default.info(`🚀 Server running on port ${PORT}`);
    logger_1.default.info(`📊 Health check: http://localhost:${PORT}/health`);
    logger_1.default.info(`🗄️  Database health: http://localhost:${PORT}/health/db`);
    // Initialize WebSocket Service
    wsService = new websocket_service_1.WebSocketService(server);
    logger_1.default.info('WebSocket server initialized and attached to HTTP server.');
    // Initialize Collaboration Events with the WebSocket service
    collaborationEvents = new collaboration_events_1.CollaborationEvents(wsService);
    // Initialize Notification Service with the WebSocket service and repository
    notificationService = new notification_service_1.NotificationService(wsService, notificationRepository);
    // Initialize Collaboration Session Service
    collaborationSessionService = new collaboration_session_service_1.CollaborationSessionService(wsService, collaborationEvents);
    logger_1.default.info('Collaboration Session Service initialized.');
    // Initialize Monitoring Service with WebSocketService
    monitoringService = new monitoring_service_1.MonitoringService(wsService);
    logger_1.default.info('Monitoring Service initialized.');
    // Initialize Workflow Collaboration Service and Controller with CollaborationEvents and CollaborationSessionService
    workflowCollaborationService = new workflow_collaboration_service_1.WorkflowCollaborationService(workflowCollaborationRepository, collaborationEvents, collaborationSessionService // Pass the new service
    );
    workflowCollaborationController = new workflow_collaboration_controller_1.WorkflowCollaborationController(workflowCollaborationService);
    // Initialize PowerOps Controller with NotificationService
    powerOpsController = new powerops_controller_1.PowerOpsController(notificationService);
    logger_1.default.info('PowerOps Controller initialized.');
    // Re-register Workflow and Collaboration Routes now that dependencies are initialized
    app.use('/api/v1/workflow-collaboration', workflowCollaborationController.router);
    logger_1.default.info('Registered /api/v1/workflow-collaboration routes');
    // Initialize Paim Controller with NotificationService and WebSocketService
    paimController = new paim_controller_1.PaimController(notificationService, wsService);
    logger_1.default.info('PAIM Controller initialized.');
    // Re-register PAIM Management Routes
    app.use('/api/v1/paim-instances', paimController.router);
    logger_1.default.info('Registered /api/v1/paim-instances routes');
    // Re-register PowerOps Gamification and Cost Management Routes
    app.use('/api/v1/powerops', powerOpsController.router);
    logger_1.default.info('Registered /api/v1/powerops routes');
});
// Graceful shutdown
process.on('SIGTERM', () => {
    logger_1.default.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
        logger_1.default.info('Process terminated');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger_1.default.info('SIGINT received, shutting down gracefully');
    server.close(() => {
        logger_1.default.info('Process terminated');
        process.exit(0);
    });
});
exports.default = app;
