// Phase 2 Test Server - Authentication and Database Integration
const express = require('express');
const jwt = require('jsonwebtoken');
const knex = require('knex');

console.log('Phase 2 Test Server: Starting authentication and database test server...');

const app = express();
app.use(express.json());

// Test database setup (SQLite in memory)
const db = knex({
  client: 'sqlite3',
  connection: { filename: ':memory:' },
  useNullAsDefault: true
});

// JWT secret for testing
const JWT_SECRET = 'test-jwt-secret-phase2';

// Initialize test database
async function initializeTestDb() {
  await db.schema.createTable('users', (table) => {
    table.increments('id').primary();
    table.string('email').unique();
    table.string('password');
    table.string('paim_tier').defaultTo('standard');
    table.timestamps(true, true);
  });
  
  // Insert test user
  await db('users').insert({
    email: '<EMAIL>',
    password: 'hashed_password',
    paim_tier: 'standard'
  });
  
  console.log('✅ Test database initialized with test user');
}

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'theaigency-backend-phase2',
    phase: 'Phase 2 - Database and Authentication'
  });
});

// Login endpoint
app.post('/api/v1/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password required' });
    }
    
    // Find user in database
    const user = await db('users').where({ email }).first();
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // In real implementation, verify password hash
    // For testing, just check if password is provided
    
    // Generate JWT token
    const payload = {
      userId: user.id.toString(),
      tenantId: 'test-tenant',
      email: user.email,
      paimTier: user.paim_tier,
      roles: ['user']
    };
    
    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '1h' });
    
    res.status(200).json({
      message: 'Login successful',
      token: token,
      user: {
        id: user.id,
        email: user.email,
        paimTier: user.paim_tier
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Protected endpoint - Get user profile
app.get('/api/v1/user/profile', authenticateToken, async (req, res) => {
  try {
    const user = await db('users').where({ id: req.user.userId }).first();
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.status(200).json({
      id: user.id,
      email: user.email,
      paimTier: user.paim_tier,
      createdAt: user.created_at
    });
    
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Protected endpoint - Test database operations
app.get('/api/v1/test/database', authenticateToken, async (req, res) => {
  try {
    // Test various database operations
    const userCount = await db('users').count('* as count').first();
    const allUsers = await db('users').select('id', 'email', 'paim_tier');
    
    res.status(200).json({
      message: 'Database operations successful',
      userCount: userCount.count,
      users: allUsers,
      authenticatedUser: req.user.email
    });
    
  } catch (error) {
    console.error('Database test error:', error);
    res.status(500).json({ error: 'Database operation failed' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Start server
const PORT = process.env.PORT || 3002;

async function startServer() {
  try {
    await initializeTestDb();
    
    const server = app.listen(PORT, () => {
      console.log(`✅ Phase 2 Test Server running on port ${PORT}`);
      console.log(`✅ Health check: http://localhost:${PORT}/health`);
      console.log(`✅ Login endpoint: POST http://localhost:${PORT}/api/v1/auth/login`);
      console.log(`✅ Protected profile: GET http://localhost:${PORT}/api/v1/user/profile`);
      console.log(`✅ Database test: GET http://localhost:${PORT}/api/v1/test/database`);
      console.log('');
      console.log('Test credentials:');
      console.log('  Email: <EMAIL>');
      console.log('  Password: any_password');
      
      // Auto-shutdown after 30 seconds for testing
      setTimeout(() => {
        console.log('✅ Phase 2 Test Server completed successfully!');
        server.close();
        process.exit(0);
      }, 30000);
    });
    
    server.on('error', (err) => {
      console.error('❌ Phase 2 Test Server failed:', err);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ Failed to start Phase 2 Test Server:', error);
    process.exit(1);
  }
}

startServer();
