// Phase 2 Test - Database connectivity and authentication
const knex = require('knex');
const jwt = require('jsonwebtoken');

console.log('Phase 2 Test: Testing database connectivity and authentication...');

// Test database configuration (using SQLite for simplicity)
const testDbConfig = {
  client: 'sqlite3',
  connection: {
    filename: ':memory:'
  },
  useNullAsDefault: true,
  migrations: {
    directory: './migrations'
  }
};

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    const db = knex(testDbConfig);
    
    // Test basic connection
    await db.raw('SELECT 1 as test');
    console.log('✅ Database connection successful');
    
    // Test table creation
    await db.schema.createTable('test_table', (table) => {
      table.increments('id').primary();
      table.string('name');
      table.timestamps(true, true);
    });
    console.log('✅ Table creation successful');
    
    // Test data insertion
    await db('test_table').insert({ name: 'test_user' });
    console.log('✅ Data insertion successful');
    
    // Test data retrieval
    const result = await db('test_table').select('*');
    console.log('✅ Data retrieval successful:', result);
    
    await db.destroy();
    return true;
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    return false;
  }
}

async function testJWTAuthentication() {
  console.log('🔍 Testing JWT authentication...');
  
  try {
    const secret = 'test-jwt-secret';
    const payload = {
      userId: 'test-user-123',
      email: '<EMAIL>',
      roles: ['user'],
      paimTier: 'standard'
    };
    
    // Test token generation
    const token = jwt.sign(payload, secret, { expiresIn: '1h' });
    console.log('✅ JWT token generation successful');
    
    // Test token verification
    const decoded = jwt.verify(token, secret);
    console.log('✅ JWT token verification successful:', decoded.userId);
    
    // Test token expiration handling
    try {
      const expiredToken = jwt.sign(payload, secret, { expiresIn: '0s' });
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
      jwt.verify(expiredToken, secret);
      console.log('❌ Expired token should have failed');
      return false;
    } catch (expiredError) {
      if (expiredError.name === 'TokenExpiredError') {
        console.log('✅ Expired token handling successful');
      } else {
        throw expiredError;
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ JWT authentication test failed:', error.message);
    return false;
  }
}

async function testBasicAPIAuth() {
  console.log('🔍 Testing basic API authentication flow...');
  
  try {
    // Simulate authentication middleware
    const authenticateToken = (token, secret) => {
      try {
        return jwt.verify(token, secret);
      } catch (error) {
        throw new Error('Authentication failed');
      }
    };
    
    const secret = 'test-jwt-secret';
    const validToken = jwt.sign({ userId: 'test-123' }, secret);
    
    // Test valid token
    const user = authenticateToken(validToken, secret);
    console.log('✅ Valid token authentication successful:', user.userId);
    
    // Test invalid token
    try {
      authenticateToken('invalid-token', secret);
      console.log('❌ Invalid token should have failed');
      return false;
    } catch (error) {
      console.log('✅ Invalid token rejection successful');
    }
    
    return true;
  } catch (error) {
    console.error('❌ API authentication test failed:', error.message);
    return false;
  }
}

async function runPhase2Tests() {
  console.log('🚀 Starting Phase 2 Tests...\n');
  
  const dbTest = await testDatabaseConnection();
  console.log('');
  
  const jwtTest = await testJWTAuthentication();
  console.log('');
  
  const apiTest = await testBasicAPIAuth();
  console.log('');
  
  if (dbTest && jwtTest && apiTest) {
    console.log('✅ Phase 2 Tests completed successfully!');
    console.log('✅ Database connectivity: WORKING');
    console.log('✅ JWT authentication: WORKING');
    console.log('✅ API authentication flow: WORKING');
    process.exit(0);
  } else {
    console.log('❌ Phase 2 Tests failed!');
    process.exit(1);
  }
}

// Run the tests
runPhase2Tests().catch(error => {
  console.error('❌ Phase 2 Test suite failed:', error);
  process.exit(1);
});
