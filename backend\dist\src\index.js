"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production'
    ? '.env.production'
    : '.env.development';
dotenv_1.default.config({ path: path_1.default.resolve(__dirname, `../${envFile}`) });
// Fallback to .env if specific env file doesn't exist
dotenv_1.default.config();
const app_1 = __importDefault(require("./app"));
const logger_1 = __importDefault(require("./config/logger"));
const db_1 = __importDefault(require("./database/db")); // Import the database connection
const ws_1 = require("ws");
const http_1 = __importDefault(require("http"));
const PORT = process.env.PORT || 3000;
const server = http_1.default.createServer(app_1.default); // Create HTTP server from Express app
// Initialize WebSocket Server
const wss = new ws_1.WebSocketServer({ server });
wss.on('connection', (ws) => {
    logger_1.default.info('WebSocket client connected');
    // Placeholder for WebSocket authentication
    // In a real scenario, you'd validate a JWT sent by the client
    // and associate the WebSocket connection with a user.
    ws.send(JSON.stringify({
        eventType: 'authentication_required',
        payload: { message: 'Authentication required. Please send an \'authenticate\' event with your JWT.' },
        timestamp: new Date().toISOString()
    }));
    ws.on('message', (message) => {
        logger_1.default.info(`Received WebSocket message: ${message}`);
        try {
            const parsedMessage = JSON.parse(message.toString());
            if (parsedMessage.eventType === 'authenticate' && parsedMessage.payload && parsedMessage.payload.token) {
                // Validate JWT token here
                // For now, just acknowledge
                ws.send(JSON.stringify({
                    eventType: 'authentication_success',
                    payload: { userId: 'mock-user-id', message: 'Authentication successful.' },
                    timestamp: new Date().toISOString()
                }));
            }
            else {
                // Handle other messages
                ws.send(JSON.stringify({
                    eventType: 'error',
                    payload: { message: 'Unknown event type or invalid message format.' },
                    timestamp: new Date().toISOString()
                }));
            }
        }
        catch (error) {
            logger_1.default.error('Failed to parse WebSocket message:', error);
            ws.send(JSON.stringify({
                eventType: 'error',
                payload: { message: 'Invalid JSON format.' },
                timestamp: new Date().toISOString()
            }));
        }
    });
    ws.on('close', () => {
        logger_1.default.info('WebSocket client disconnected');
    });
    ws.on('error', (error) => {
        logger_1.default.error('WebSocket error:', error);
    });
});
server.listen(PORT, () => {
    logger_1.default.info(`Server running on port ${PORT}`);
    logger_1.default.info(`Access Token Expiration: ${process.env.JWT_ACCESS_TOKEN_EXPIRATION || '15m'}`);
    logger_1.default.info(`Refresh Token Expiration: ${process.env.JWT_REFRESH_TOKEN_EXPIRATION || '7d'}`);
});
// Health check endpoints
app_1.default.get('/health', (req, res) => {
    res.status(200).json({ status: 'UP', timestamp: new Date() });
});
app_1.default.get('/health/db', async (req, res) => {
    try {
        await db_1.default.raw('SELECT 1');
        res.status(200).json({ status: 'DB_UP', timestamp: new Date() });
    }
    catch (error) {
        logger_1.default.error('Database health check failed:', error);
        res.status(500).json({ status: 'DB_DOWN', timestamp: new Date(), error: (error instanceof Error) ? error.message : 'An unknown error occurred' });
    }
});
