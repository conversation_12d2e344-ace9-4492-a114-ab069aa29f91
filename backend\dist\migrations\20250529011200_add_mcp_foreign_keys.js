"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.alterTable('mcp_access_controls', table => {
        table.foreign('paim_tier_id').references('paim_tier_id').inTable('PaimTiers').onDelete('CASCADE');
    });
    await knex.schema.alterTable('mcp_rate_limits', table => {
        table.foreign('paim_tier_id').references('paim_tier_id').inTable('PaimTiers').onDelete('CASCADE');
    });
    await knex.schema.alterTable('mcp_quotas', table => {
        table.foreign('paim_tier_id').references('paim_tier_id').inTable('PaimTiers').onDelete('CASCADE');
    });
}
async function down(knex) {
    await knex.schema.alterTable('mcp_access_controls', table => {
        table.dropForeign('paim_tier_id');
    });
    await knex.schema.alterTable('mcp_rate_limits', table => {
        table.dropForeign('paim_tier_id');
    });
    await knex.schema.alterTable('mcp_quotas', table => {
        table.dropForeign('paim_tier_id');
    });
}
