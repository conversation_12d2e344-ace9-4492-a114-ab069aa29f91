"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validate = void 0;
const validate = (schema) => (req, res, next) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    if (!error) {
        next();
        return;
    }
    const extractedErrors = error.details.map(err => ({
        field: err.path.join('.'), // Joi path is an array, join it for a string field name
        code: err.type,
        message: err.message,
    }));
    const errorResponse = {
        timestamp: new Date().toISOString(),
        status: 400,
        error: 'Bad Request',
        message: 'Validation failed.',
        path: req.originalUrl,
        details: extractedErrors,
    };
    res.status(400).json(errorResponse);
    return;
};
exports.validate = validate;
