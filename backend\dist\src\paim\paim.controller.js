"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaimController = void 0;
const express_1 = require("express");
const paim_service_1 = require("./paim.service");
const paim_repository_1 = require("./paim.repository");
const audit_service_1 = require("../audit/audit.service");
const asyncHandler_1 = require("../utils/asyncHandler"); // Named import
const validation_1 = require("../utils/validation"); // Import validate middleware
const errors_1 = require("../utils/errors");
const type_guards_1 = require("../utils/type-guards");
const paim_validation_1 = require("./paim.validation"); // Import Joi schemas
const authorization_1 = require("../middleware/authorization"); // Import authorization middleware
const permissions_1 = require("../auth/permissions"); // Import permissions
class PaimController {
    router;
    paimService;
    paimRepository;
    auditTrailService;
    constructor(notificationService, wsService) {
        this.auditTrailService = new audit_service_1.AuditTrailService();
        this.paimRepository = new paim_repository_1.PaimRepository(this.auditTrailService);
        this.paimService = new paim_service_1.PaimService(this.paimRepository, this.auditTrailService, notificationService, wsService);
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        // PAIM Instance Management
        this.router.get('/', (0, validation_1.validate)(paim_validation_1.getAllPaimInstancesQuerySchema), // Validate query parameters
        (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_READ]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { status, page, size, sort } = req.query;
            const filters = { status: (0, type_guards_1.getParam)(status) };
            const pagination = { page: parseInt((0, type_guards_1.getParam)(page, '1')), size: parseInt((0, type_guards_1.getParam)(size, '10')), sort: (0, type_guards_1.getParam)(sort, 'createdAt,desc') };
            // Ensure req.user is available from a preceding authentication middleware
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { data, totalElements } = await this.paimService.getAllPaimInstances(req.user, // Pass the entire user object
            filters, pagination);
            const paginationMetadata = {
                totalElements,
                totalPages: Math.ceil(totalElements / pagination.size),
                currentPage: pagination.page,
                pageSize: pagination.size,
            };
            res.status(200).json({ data, pagination: paginationMetadata });
        }));
        this.router.post('/', (0, validation_1.validate)(paim_validation_1.createPaimInstanceSchema), // Validate request body
        (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_CREATE]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const paimInstance = await this.paimService.createPaimInstance(req.body, req.user);
            res.status(201).json(paimInstance);
        }));
        this.router.get('/:paimInstanceId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_READ]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const paimInstance = await this.paimService.getPaimInstanceById((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user);
            res.status(200).json(paimInstance);
        }));
        this.router.put('/:paimInstanceId', (0, validation_1.validate)(paim_validation_1.updatePaimInstanceSchema), // Validate request body
        (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_UPDATE]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const updateRequest = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const updatedPaimInstance = await this.paimService.updatePaimInstance((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, // Pass the entire user object
            updateRequest);
            res.status(200).json(updatedPaimInstance);
        }));
        this.router.delete('/:paimInstanceId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_DELETE]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            await this.paimService.deletePaimInstance((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user);
            res.status(204).send();
        }));
        // PAIM Tier Management
        this.router.post('/:paimInstanceId/tier-change-requests', (0, validation_1.validate)(paim_validation_1.paimTierChangeRequestSchema), // Validate request body
        (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const tierChangeRequest = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const status = await this.paimService.requestPaimTierChange((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, // Pass the entire user object
            tierChangeRequest);
            res.status(202).json(status);
        }));
        // PAIM Hierarchy Management
        this.router.get('/:paimInstanceId/hierarchy', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_READ]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const hierarchy = await this.paimService.getPaimHierarchy((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user);
            res.status(200).json(hierarchy);
        }));
        this.router.put('/:paimInstanceId/hierarchy', (0, validation_1.validate)(paim_validation_1.paimHierarchyUpdateSchema), // Validate request body
        (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS]), // Add authorization middleware
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const hierarchyUpdate = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const updatedHierarchy = await this.paimService.updatePaimHierarchy((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, // Pass the entire user object
            hierarchyUpdate);
            res.status(200).json(updatedHierarchy);
        }));
        // Cross-PAIM Communication
        this.router.post('/:paimInstanceId/communicate', (0, validation_1.validate)(paim_validation_1.crossPaimCommunicationRequestSchema), // Validate request body
        (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_VIEW_ALL]), // Example permission for cross-PAIM communication
        (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const communicationRequest = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const response = await this.paimService.communicateWithPaim((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, // Pass the entire user object
            communicationRequest.targetPaimInstanceId, communicationRequest.message, communicationRequest.messageType || 'text');
            res.status(200).json(response);
        }));
    }
}
exports.PaimController = PaimController;
exports.default = PaimController;
