"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateEnvironment = validateEnvironment;
const type_guards_1 = require("../utils/type-guards");
function validateEnvironment() {
    try {
        return {
            appwrite: {
                endpoint: (0, type_guards_1.requireEnvVar)(process.env.APPWRITE_ENDPOINT, 'APPWRITE_ENDPOINT'),
                projectId: (0, type_guards_1.requireEnvVar)(process.env.APPWRITE_PROJECT_ID, 'APPWRITE_PROJECT_ID'),
                databaseId: (0, type_guards_1.requireEnvVar)(process.env.APPWRITE_DATABASE_ID, 'APPWRITE_DATABASE_ID'),
                apiKey: (0, type_guards_1.getEnvVar)(process.env.APPWRITE_API_KEY),
            },
            jwt: {
                secret: (0, type_guards_1.requireEnvVar)(process.env.JWT_SECRET, 'JWT_SECRET'),
            },
            server: {
                port: parseInt((0, type_guards_1.getEnvVar)(process.env.PORT) || '3000', 10),
                nodeEnv: (0, type_guards_1.getEnvVar)(process.env.NODE_ENV) || 'development',
            },
        };
    }
    catch (error) {
        console.error('Environment validation failed:', error);
        console.log('\nRequired environment variables:');
        console.log('- APPWRITE_ENDPOINT');
        console.log('- APPWRITE_PROJECT_ID');
        console.log('- APPWRITE_DATABASE_ID');
        console.log('- JWT_SECRET');
        console.log('\nPlease check your .env.development file.');
        throw error;
    }
}
