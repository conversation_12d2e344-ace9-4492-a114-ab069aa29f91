"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaimService = void 0;
const paim_types_1 = require("./paim.types");
const audit_types_1 = require("../audit/audit.types");
const errors_1 = require("../utils/errors"); // Assuming a custom error class
const logger_1 = __importDefault(require("../config/logger")); // Assuming a logger instance
const uuid_1 = require("uuid"); // Import uuidv4
const db_1 = require("../types/db"); // Import PaimTierEnum
class PaimService {
    paimRepository;
    auditTrailService;
    constructor(paimRepository, auditTrailService) {
        this.paimRepository = paimRepository;
        this.auditTrailService = auditTrailService;
    }
    async notifyCove(escalation) {
        logger_1.default.info(`Notifying Cove for escalation ID: ${escalation.id}`);
        // In a real system, this would involve:
        // 1. Identifying the appropriate Cove agent(s) based on escalation priority, service, etc.
        // 2. Sending a notification (e.g., email, SMS, internal chat, dedicated Cove UI alert).
        // 3. Potentially creating a ticket in an incident management system.
        // For now, we'll just log the notification and simulate the process.
        await this.auditTrailService.logEvent({
            tenantId: 'system', // Assuming system-level for Cove escalations
            userId: 'system',
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.CRITICAL, // Cove notifications are critical
            operationType: 'COVE_NOTIFICATION_SENT',
            description: `Cove notified for escalation: ${escalation.id} - ${escalation.description}`,
            timestamp: new Date(),
            resourceId: escalation.id,
            metadata: { escalation },
        });
        logger_1.default.info(`Cove notification simulated for escalation ID: ${escalation.id}.`);
    }
    // PAIM Instance Management
    async createPaimInstance(data, tenantId, userId) {
        // Business rule: Only System Admin or Company Admin can create PAIM instances
        // This check would typically involve fetching user roles/tier from an auth service
        // For now, assuming the caller (controller) has already validated permissions
        logger_1.default.info(`Attempting to create PAIM instance: ${data.name} for tenant ${tenantId} by user ${userId}`);
        // Validate tier hierarchy for creation if applicable (e.g., a Company Admin can't create a System Admin PAIM)
        const requestedTierLevel = (await this.paimRepository.getPaimTierByName(data.tier))?.hierarchy_level;
        // Assuming user's tier level is available from authentication context
        // const userTierLevel = (await this.paimRepository.getPaimTierByName(userPaimTier))?.hierarchy_level;
        // if (requestedTierLevel <= userTierLevel) {
        //   throw new CustomError('Cannot create PAIM instance of equal or higher tier than your own.', { originalErrorCode: 'Forbidden', originalStatusCode: 403 });
        // }
        try {
            const paimInstance = await this.paimRepository.createPaimInstance(data, tenantId);
            logger_1.default.info(`PAIM instance created: ${paimInstance.id}`);
            return paimInstance;
        }
        catch (error) {
            logger_1.default.error(`Error creating PAIM instance: ${error.message}`, { error });
            if (error.code === '23505') { // Unique violation code for PostgreSQL
                throw new errors_1.CustomError(`PAIM instance with name '${data.name}' already, exists.`, { originalErrorCode: 'Conflict', originalStatusCode: 409 });
            }
            throw new errors_1.CustomError('Failed to create PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    async getAllPaimInstances(tenantId, userId, filters, pagination) {
        logger_1.default.info(`Fetching all PAIM instances for tenant ${tenantId} by user ${userId}`);
        // Permission check: Users can only see PAIMs within their tenant and potentially their hierarchy
        // For System Admin, they can see all. For Company Admin, only their company's PAIMs.
        // For Power User/Personal, only their own PAIM.
        // This logic needs to be implemented based on the user's actual PAIM tier and hierarchy.
        try {
            const result = await this.paimRepository.getAllPaimInstances(tenantId, filters, pagination);
            logger_1.default.info(`Retrieved ${result.data.length} PAIM instances for tenant ${tenantId}`);
            return result;
        }
        catch (error) {
            logger_1.default.error(`Error fetching all PAIM instances: ${error.message}`, { error });
            throw new errors_1.CustomError('Failed to retrieve PAIM instances.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    async getPaimInstanceById(id, tenantId, userId) {
        logger_1.default.info(`Fetching PAIM instance ${id} for tenant ${tenantId} by user ${userId}`);
        const paimInstance = await this.paimRepository.getPaimInstanceById(id, tenantId);
        if (!paimInstance) {
            logger_1.default.warn(`PAIM instance ${id} not found for tenant ${tenantId}`);
            throw new errors_1.CustomError(`PAIM instance with ID ${id} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Permission check: Ensure user has access to this specific PAIM instance
        // e.g., if user is Personal tier, they can only access their own PAIM.
        // If user is Company Admin, they can access any PAIM within their company.
        // If user is System Admin, they can access any PAIM.
        // This requires fetching the user's PAIM tier and comparing with the target PAIM's tier/ownership.
        logger_1.default.info(`PAIM instance ${id} retrieved successfully.`);
        return paimInstance;
    }
    async updatePaimInstance(id, tenantId, userId, data) {
        logger_1.default.info(`Attempting to update PAIM instance ${id} for tenant ${tenantId} by user ${userId}`);
        const existingPaim = await this.paimRepository.getPaimInstanceById(id, tenantId);
        if (!existingPaim) {
            logger_1.default.warn(`PAIM instance ${id} not found for update for tenant ${tenantId}`);
            throw new errors_1.CustomError(`PAIM instance with ID ${id} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Permission check: Only owner or higher-tier PAIMs can update
        // e.g., Personal PAIM can only update itself. Power User can update their own and potentially lower-tier PAIMs they manage.
        // Company Admin can update any PAIM within their company. System Admin can update any.
        try {
            const updatedPaim = await this.paimRepository.updatePaimInstance(id, tenantId, data);
            if (!updatedPaim) {
                throw new errors_1.CustomError('Failed to update PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500 });
            }
            logger_1.default.info(`PAIM instance ${id} updated successfully.`);
            return updatedPaim;
        }
        catch (error) {
            logger_1.default.error(`Error updating PAIM instance ${id}: ${error.message}`, { error });
            throw new errors_1.CustomError('Failed to update PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    async deletePaimInstance(id, tenantId, userId) {
        logger_1.default.info(`Attempting to delete PAIM instance ${id} for tenant ${tenantId} by user ${userId}`);
        const existingPaim = await this.paimRepository.getPaimInstanceById(id, tenantId);
        if (!existingPaim) {
            logger_1.default.warn(`PAIM instance ${id} not found for deletion for tenant ${tenantId}`);
            throw new errors_1.CustomError(`PAIM instance with ID ${id} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Permission check: Only higher-tier PAIMs or owner can delete
        // e.g., Personal PAIM cannot delete itself. Power User can delete lower-tier PAIMs they manage.
        // Company Admin can delete any PAIM within their company. System Admin can delete any.
        try {
            const deleted = await this.paimRepository.deletePaimInstance(id, tenantId);
            if (!deleted) {
                throw new errors_1.CustomError('Failed to delete PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500 });
            }
            logger_1.default.info(`PAIM instance ${id} deleted successfully.`);
        }
        catch (error) {
            logger_1.default.error(`Error deleting PAIM instance ${id}: ${error.message}`, { error });
            throw new errors_1.CustomError('Failed to delete PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    // PAIM Tier Management
    async requestPaimTierChange(paimInstanceId, tenantId, userId, data) {
        logger_1.default.info(`User ${userId} requesting tier change for PAIM instance ${paimInstanceId} to ${data.requestedTier}`);
        const paimInstance = await this.paimRepository.getPaimInstanceById(paimInstanceId, tenantId);
        if (!paimInstance) {
            throw new errors_1.CustomError(`PAIM instance with ID ${paimInstanceId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Business rule: Validate requested tier against current tier and user's permissions
        const currentTierLevel = (await this.paimRepository.getPaimTierByName(paimInstance.tier))?.hierarchy_level;
        const requestedTierLevel = (await this.paimRepository.getPaimTierByName(data.requestedTier))?.hierarchy_level;
        if (currentTierLevel === undefined || requestedTierLevel === undefined) {
            throw new errors_1.CustomError('Invalid current or requested PAIM tier.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        // Example rule: Cannot request a tier higher than System Admin (level 1)
        if (requestedTierLevel < 1) {
            throw new errors_1.CustomError('Cannot request a tier higher than System Admin.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        // Example rule: A PAIM cannot directly upgrade to System Admin, requires manual intervention or specific flow
        if (data.requestedTier === db_1.PaimTierEnum.SystemAdmin && paimInstance.tier !== db_1.PaimTierEnum.SystemAdmin) {
            throw new errors_1.CustomError('Direct upgrade to System Admin tier is not allowed through this API.', { originalErrorCode: 'Forbidden', originalStatusCode: 403 });
        }
        // Delegation/Escalation logic: If a tier change requires approval from a higher authority,
        // this is where the request would be logged and marked as 'pending'.
        // For simplicity, let's assume immediate approval for downgrades and pending for upgrades
        // unless it's a System Admin performing the upgrade.
        let status = paim_types_1.PaimTierChangeRequestStatusEnum.Pending;
        // In a real system, this would involve a workflow engine or approval process.
        // For now, if it's a downgrade, we can auto-approve for simplicity.
        if (requestedTierLevel > currentTierLevel) { // Downgrade
            status = paim_types_1.PaimTierChangeRequestStatusEnum.Approved; // Auto-approve downgrades
            // In a real system, update the PAIM instance tier here
            // await this.paimRepository.updatePaimInstance(paimInstanceId, tenantId, { tier: data.requestedTier });
        }
        const tierChangeRequest = {
            requestId: (0, uuid_1.v4)(),
            paimInstanceId: paimInstanceId,
            requestedTier: data.requestedTier,
            status: status,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        // Log the request (and potentially save to a 'PaimTierChangeRequests' table)
        await this.auditTrailService.logEvent({
            tenantId,
            userId,
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'PAIM_TIER_CHANGE_REQUESTED',
            description: `Tier change requested for PAIM ${paimInstanceId} from ${paimInstance.tier} to ${data.requestedTier}. Status: ${status}`,
            timestamp: new Date(),
            resourceId: paimInstanceId,
            metadata: { paimInstanceId, currentTier: paimInstance.tier, requestedTier: data.requestedTier, status },
        });
        logger_1.default.info(`Tier change request for PAIM ${paimInstanceId} processed with status: ${status}`);
        return tierChangeRequest;
    }
    // PAIM Hierarchy Management
    async getPaimHierarchy(paimInstanceId, tenantId, userId) {
        logger_1.default.info(`Fetching hierarchy for PAIM instance ${paimInstanceId} for tenant ${tenantId} by user ${userId}`);
        const hierarchyTree = await this.paimRepository.getPaimHierarchy(paimInstanceId, tenantId);
        if (!hierarchyTree) {
            throw new errors_1.CustomError(`Hierarchy for PAIM instance with ID ${paimInstanceId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Permission check: Ensure user has access to view this hierarchy.
        // Similar to getPaimInstanceById, based on user's tier and relationship to the PAIM.
        logger_1.default.info(`Hierarchy for PAIM instance ${paimInstanceId} retrieved successfully.`);
        return {
            paimInstanceId: paimInstanceId,
            hierarchyTree: hierarchyTree,
            lastUpdated: new Date().toISOString(), // This should come from the database
        };
    }
    async updatePaimHierarchy(paimInstanceId, tenantId, userId, data) {
        logger_1.default.info(`Updating hierarchy for PAIM instance ${paimInstanceId} for tenant ${tenantId} by user ${userId}`);
        const paimInstance = await this.paimRepository.getPaimInstanceById(paimInstanceId, tenantId);
        if (!paimInstance) {
            throw new errors_1.CustomError(`PAIM instance with ID ${paimInstanceId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Business rule: Validate the incoming hierarchy tree for correctness and adherence to tier rules.
        // e.g., a Personal PAIM cannot have children. A Power User PAIM can only have Personal children.
        // Company Admin can have Power User and Personal children. System Admin can have any.
        // Also, prevent circular dependencies.
        // Permission check: Only higher-tier PAIMs or specific roles can update hierarchy.
        try {
            const updated = await this.paimRepository.updatePaimHierarchy(paimInstanceId, tenantId, data.hierarchyTree);
            if (!updated) {
                throw new errors_1.CustomError('Failed to update PAIM hierarchy.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500 });
            }
            logger_1.default.info(`Hierarchy for PAIM instance ${paimInstanceId} updated successfully.`);
            return this.getPaimHierarchy(paimInstanceId, tenantId, userId); // Return the updated hierarchy
        }
        catch (error) {
            logger_1.default.error(`Error updating PAIM hierarchy for ${paimInstanceId}: ${error.message}`, { error });
            throw new errors_1.CustomError('Failed to update PAIM hierarchy.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    // Cross-PAIM Communication (Placeholder for future implementation)
    async communicateWithPaim(sourcePaimInstanceId, tenantId, userId, targetPaimInstanceId, message, messageType) {
        logger_1.default.info(`PAIM ${sourcePaimInstanceId} attempting to communicate with ${targetPaimInstanceId}`);
        // Business logic for cross-PAIM communication:
        // 1. Validate communication permissions (e.g., can a Personal PAIM communicate with a Company Admin PAIM?)
        // 2. Route the message to the target PAIM (could involve message queues, direct API calls, etc.)
        // 3. Handle different message types (text, command, data_transfer)
        // 4. Ensure multi-tenant isolation is maintained.
        await this.auditTrailService.logEvent({
            tenantId,
            userId,
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'CROSS_PAIM_COMMUNICATION',
            description: `PAIM ${sourcePaimInstanceId} communicated with ${targetPaimInstanceId}`,
            timestamp: new Date(),
            resourceId: sourcePaimInstanceId,
            metadata: { sourcePaimInstanceId, targetPaimInstanceId, messageType, message },
        });
        return { message: 'Message successfully delivered to target PAIM.' };
    }
}
exports.PaimService = PaimService;
