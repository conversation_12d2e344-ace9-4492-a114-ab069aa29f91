import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import AgentFramework from './index';
import { Agent } from './core/Agent';
import { CLAPIAgent } from './agents/CLAPIAgent';
import { R1LogicAgent } from './agents/R1LogicAgent';
import { DevOpsAgent } from './agents/DevOpsAgent';

describe('Agent Framework Integration Tests', () => {
  let framework: AgentFramework;

  beforeEach(async () => {
    framework = AgentFramework.getInstance();
  });

  afterEach(async () => {
    if (framework.isInitialized()) {
      await framework.shutdown();
    }
  });

  describe('Framework Initialization', () => {
    it('should initialize with default configuration', async () => {
      await framework.initialize();
      
      expect(framework.isInitialized()).toBe(true);
      
      const status = framework.getStatus();
      expect(status.initialized).toBe(true);
      expect(status.agents.length).toBeGreaterThan(0);
    });

    it('should initialize with custom configuration', async () => {
      const customConfig = {
        core: {
          logLevel: 'debug',
          timeout: 60000,
          maxConcurrency: 10
        },
        agentRegistry: {
          'CL-API': { enabled: true },
          'R1-Logic': { enabled: false },
          'DevOps': { enabled: true }
        }
      };

      await framework.initialize(customConfig);
      
      expect(framework.isInitialized()).toBe(true);
      
      const config = framework.getConfig();
      expect(config.get('core.logLevel')).toBe('debug');
      expect(config.get('core.timeout')).toBe(60000);
    });

    it('should fail with invalid configuration', async () => {
      const invalidConfig = {
        core: {
          logLevel: 'invalid',
          timeout: -1
        }
      };

      await expect(framework.initialize(invalidConfig as any)).rejects.toThrow();
    });
  });

  describe('Predefined Agents', () => {
    beforeEach(async () => {
      await framework.initialize();
    });

    it('should initialize CL-API agent when enabled', () => {
      const clApiAgent = framework.getAgent('CL-API');
      expect(clApiAgent).toBeInstanceOf(CLAPIAgent);
      expect(clApiAgent?.getIsActive()).toBe(true);
    });

    it('should initialize R1-Logic agent when enabled', () => {
      const r1LogicAgent = framework.getAgent('R1-Logic');
      expect(r1LogicAgent).toBeInstanceOf(R1LogicAgent);
      expect(r1LogicAgent?.getIsActive()).toBe(true);
    });

    it('should initialize DevOps agent when enabled', () => {
      const devOpsAgent = framework.getAgent('DevOps');
      expect(devOpsAgent).toBeInstanceOf(DevOpsAgent);
      expect(devOpsAgent?.getIsActive()).toBe(true);
    });

    it('should not initialize disabled agents', async () => {
      await framework.shutdown();
      
      const config = {
        agentRegistry: {
          'CL-API': { enabled: false },
          'R1-Logic': { enabled: false },
          'DevOps': { enabled: false }
        }
      };

      await framework.initialize(config);
      
      const status = framework.getStatus();
      expect(status.agents).toHaveLength(0);
    });
  });

  describe('Task Execution', () => {
    beforeEach(async () => {
      await framework.initialize();
    });

    it('should execute API-related tasks with CL-API agent', async () => {
      const result = await framework.executeTask('api_request', {
        method: 'GET',
        endpoint: '/test',
        headers: { 'Content-Type': 'application/json' }
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should execute business logic tasks with R1-Logic agent', async () => {
      const result = await framework.executeTask('business_rule', {
        context: {
          userId: 'user123',
          tenantId: 'tenant456',
          paimTier: 'enterprise'
        },
        data: { action: 'create_resource' }
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should execute DevOps tasks with DevOps agent', async () => {
      const result = await framework.executeTask('health_check', {});

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.status).toBeDefined();
    });

    it('should execute task with specific agent', async () => {
      const result = await framework.executeTask('generic_task', { test: 'data' }, 'CL-API');

      expect(result.success).toBe(true);
      expect(result.agentId).toBe('cl-api-agent');
    });

    it('should fail when specified agent not found', async () => {
      await expect(
        framework.executeTask('test_task', {}, 'NonExistentAgent')
      ).rejects.toThrow('Agent \'NonExistentAgent\' not found');
    });
  });

  describe('Agent Communication', () => {
    beforeEach(async () => {
      await framework.initialize();
    });

    it('should allow agents to communicate via events', async () => {
      const clApiAgent = framework.getAgent('CL-API') as CLAPIAgent;
      const devOpsAgent = framework.getAgent('DevOps') as DevOpsAgent;

      let eventReceived = false;
      let eventData: any;

      // Set up event listener
      devOpsAgent.on('api:request', (data) => {
        eventReceived = true;
        eventData = data;
      });

      // Emit event from CL-API agent
      clApiAgent.emit('api:request', { endpoint: '/test', method: 'GET' });

      // Give some time for event propagation
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(eventReceived).toBe(true);
      expect(eventData.endpoint).toBe('/test');
    });
  });

  describe('Framework Lifecycle', () => {
    it('should shutdown gracefully', async () => {
      await framework.initialize();
      expect(framework.isInitialized()).toBe(true);

      await framework.shutdown();
      expect(framework.isInitialized()).toBe(false);

      const status = framework.getStatus();
      expect(status.initialized).toBe(false);
    });

    it('should handle multiple initialization attempts', async () => {
      await framework.initialize();
      
      // Second initialization should not throw
      await framework.initialize();
      
      expect(framework.isInitialized()).toBe(true);
    });

    it('should handle shutdown when not initialized', async () => {
      // Should not throw
      await framework.shutdown();
      expect(framework.isInitialized()).toBe(false);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate configuration schema', () => {
      const validConfig = {
        core: {
          logLevel: 'info',
          timeout: 30000,
          maxConcurrency: 5
        },
        agentRegistry: {
          'CL-API': { enabled: true }
        }
      };

      expect(() => AgentFramework.validate(validConfig)).not.toThrow();
    });

    it('should reject invalid configuration', () => {
      const invalidConfig = {
        core: {
          logLevel: 'invalid',
          timeout: 'not-a-number'
        }
      };

      expect(() => AgentFramework.validate(invalidConfig)).toThrow();
    });
  });

  describe('Agent Creation', () => {
    beforeEach(async () => {
      await framework.initialize();
    });

    it('should create custom agents', () => {
      const customAgent = framework.createAgent('custom-1', 'CustomAgent', 'custom');
      
      expect(customAgent).toBeInstanceOf(Agent);
      expect(customAgent.id).toBe('custom-1');
      expect(customAgent.name).toBe('CustomAgent');
      expect(customAgent.type).toBe('custom');
      
      // Should be registered in global registry
      expect(Agent.getAgent('CustomAgent')).toBe(customAgent);
    });
  });

  describe('Framework Status', () => {
    beforeEach(async () => {
      await framework.initialize();
    });

    it('should provide comprehensive status information', () => {
      const status = framework.getStatus();
      
      expect(status.initialized).toBe(true);
      expect(Array.isArray(status.agents)).toBe(true);
      expect(typeof status.tools).toBe('number');
      expect(typeof status.workflows).toBe('number');
      expect(typeof status.plugins).toBe('number');
      
      // Check agent status details
      status.agents.forEach(agent => {
        expect(typeof agent.name).toBe('string');
        expect(typeof agent.active).toBe('boolean');
        expect(typeof agent.type).toBe('string');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle framework initialization errors gracefully', async () => {
      const invalidConfig = {
        core: {
          timeout: -1 // Invalid timeout
        }
      };

      await expect(framework.initialize(invalidConfig as any)).rejects.toThrow();
      expect(framework.isInitialized()).toBe(false);
    });

    it('should handle task execution errors', async () => {
      await framework.initialize();

      // Try to execute task when framework is not initialized
      await framework.shutdown();

      await expect(
        framework.executeTask('test_task', {})
      ).rejects.toThrow('Framework is not initialized');
    });
  });
});
