"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentRepository = void 0;
const uuid_1 = require("uuid");
const db_1 = __importDefault(require("../database/db"));
const pagination_1 = require("../utils/pagination");
const audit_types_1 = require("../audit/audit.types");
const type_guards_1 = require("../utils/type-guards");
class AgentRepository {
    auditTrailService;
    tableName = 'AgentDefinitions';
    agentAssignmentsTableName = 'AgentAssignments';
    workflowExecutionsTableName = 'WorkflowExecutions';
    agentPerformanceMetricsTableName = 'AgentPerformanceMetrics';
    constructor(auditTrailService) {
        this.auditTrailService = auditTrailService;
    }
    // Helper to ensure tenant isolation
    applyTenantIsolation(queryBuilder, tenantId) {
        return queryBuilder.where('tenant_id', tenantId);
    }
    async getAllAgents(tenantId, options) {
        const { page = 1, size = 10, sort, persona, status } = options;
        let query = (0, db_1.default)(this.tableName)
            .select('agent_id as id', 'name', 'persona', 'description', 'status', 'paim_instance_id as paimInstanceId', 'capabilities', 'created_at as createdAt', 'updated_at as updatedAt');
        query = this.applyTenantIsolation(query, tenantId);
        if (persona) {
            query = query.where('persona', persona);
        }
        if (status) {
            query = query.where('status', status);
        }
        const totalCountQuery = (0, db_1.default)(this.tableName)
            .count('* as count')
            .where((builder) => this.applyTenantIsolation(builder, tenantId));
        if (persona) {
            totalCountQuery.where('persona', persona);
        }
        if (status) {
            totalCountQuery.where('status', status);
        }
        const totalCountResult = await totalCountQuery.first();
        const total = totalCountResult ? totalCountResult.count : 0;
        if (sort) {
            const [sortBy, sortOrder] = (0, type_guards_1.getParam)(sort).split(':');
            query = query.orderBy((0, type_guards_1.requireParam)(sortBy, 'sortBy'), (0, type_guards_1.getParam)(sortOrder, 'asc'));
        }
        const offset = (page - 1) * size;
        query = query.limit(size).offset(offset);
        const agents = (await query).map((agent) => ({
            ...agent,
            capabilities: agent.capabilities ? JSON.parse(agent.capabilities) : [],
        }));
        const pagination = (0, pagination_1.generatePagination)(total, page, size);
        return { agents, pagination };
    }
    async getAgentById(tenantId, id) {
        const agent = await this.applyTenantIsolation((0, db_1.default)(this.tableName).where('agent_id', id), tenantId)
            .select('agent_id as id', 'name', 'persona', 'description', 'status', 'paim_instance_id as paimInstanceId', 'capabilities', 'created_at as createdAt', 'updated_at as updatedAt')
            .first();
        if (!agent) {
            return undefined;
        }
        return {
            ...agent,
            capabilities: agent.capabilities ? JSON.parse(agent.capabilities) : [],
        };
    }
    async createAgent(tenantId, agentData) {
        const newAgent = {
            id: (0, uuid_1.v4)(),
            ...agentData,
            status: 'inactive', // Default status
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            capabilities: agentData.capabilities || [],
            paimInstanceId: agentData.paimInstanceId,
        };
        await (0, db_1.default)(this.tableName).insert({
            agent_id: newAgent.id,
            tenant_id: tenantId,
            paim_instance_id: newAgent.paimInstanceId,
            name: newAgent.name,
            persona: newAgent.persona,
            description: newAgent.description,
            status: newAgent.status,
            capabilities: JSON.stringify(newAgent.capabilities),
            created_at: newAgent.createdAt,
            updated_at: newAgent.updatedAt,
        });
        return newAgent;
    }
    async updateAgent(tenantId, id, agentData) {
        const updateData = {
            updated_at: new Date().toISOString(),
        };
        if (agentData.name)
            updateData.name = agentData.name;
        if (agentData.persona)
            updateData.persona = agentData.persona;
        if (agentData.description)
            updateData.description = agentData.description;
        if (agentData.status)
            updateData.status = agentData.status;
        if (agentData.capabilities)
            updateData.capabilities = JSON.stringify(agentData.capabilities);
        const [count] = await this.applyTenantIsolation((0, db_1.default)(this.tableName).where('agent_id', id), tenantId).update(updateData);
        if (count === 0) {
            return undefined;
        }
        return this.getAgentById(tenantId, id);
    }
    async deleteAgent(tenantId, id) {
        const count = await this.applyTenantIsolation((0, db_1.default)(this.tableName).where('agent_id', id), tenantId).del();
        return count > 0;
    }
    async assignAgent(tenantId, assignmentData) {
        const newAssignment = {
            id: (0, uuid_1.v4)(),
            ...assignmentData,
            assignedAt: new Date().toISOString(),
        };
        await (0, db_1.default)(this.agentAssignmentsTableName).insert({
            assignment_id: newAssignment.id,
            agent_id: newAssignment.agentId,
            assigned_to_type: newAssignment.assignedToType,
            assigned_to_id: newAssignment.assignedToId,
            assignment_type: newAssignment.assignmentType,
            assigned_at: newAssignment.assignedAt,
            tenant_id: tenantId,
        });
        return newAssignment;
    }
    async recordWorkflowExecution(tenantId, executionData) {
        await (0, db_1.default)(this.workflowExecutionsTableName).insert({
            execution_id: executionData.executionId,
            workflow_id: executionData.workflowId,
            tenant_id: tenantId,
            status: executionData.status,
            start_time: executionData.startTime,
            end_time: executionData.endTime,
            output_data: executionData.outputData ? JSON.stringify(executionData.outputData) : null,
            error_details: executionData.error ? JSON.stringify({ message: executionData.error }) : null,
        });
        return executionData;
    }
    async getAgentPerformanceMetrics(tenantId, agentId, startDate, endDate) {
        let query = (0, db_1.default)(this.agentPerformanceMetricsTableName).where('agent_id', agentId);
        query = this.applyTenantIsolation(query, tenantId);
        if (startDate) {
            query = query.where('timestamp', '>=', startDate);
        }
        if (endDate) {
            query = query.where('timestamp', '<=', endDate);
        }
        // This is a simplified aggregation. In a real scenario, you'd aggregate metrics over the period.
        const metrics = await query.first(); // Assuming one entry per agent for simplicity or latest.
        return metrics;
    }
    async recordAuditTrail(tenantId, action, entityId, details, userId) {
        let category;
        let severity = audit_types_1.AuditEventSeverity.INFO;
        switch (action) {
            case 'agent_created':
            case 'agent_updated':
            case 'agent_deleted':
            case 'agent_assigned':
                category = audit_types_1.AuditEventCategory.AGENT_OPERATION;
                break;
            case 'workflow_execution_initiated':
                category = audit_types_1.AuditEventCategory.SYSTEM_OPERATION; // Or a more specific WORKFLOW_OPERATION
                break;
            default:
                category = audit_types_1.AuditEventCategory.SYSTEM_OPERATION;
                break;
        }
        await this.auditTrailService.logEvent({
            tenantId,
            userId: userId || 'system', // Use provided userId or default to 'system'
            category,
            severity,
            operationType: action,
            description: `Agent related action: ${action} for entity ${entityId}`,
            timestamp: new Date(),
            resourceId: entityId,
            metadata: details,
        });
    }
}
exports.AgentRepository = AgentRepository;
