# HTTP Client Development Strategy

## Overview

This document establishes the strategic approach for HTTP client usage across the TheAIgency backend to prevent future issues and ensure consistent, secure, and performant implementations.

## Strategic Principles

### 1. Centralization First
- **Never use axios directly** in service files
- **Always use the centralized HTTP client service**
- **Create specialized clients** for different use cases
- **Maintain single source of truth** for HTTP configurations

### 2. Security by Design
- **Environment validation** on service startup
- **Automatic request sanitization** for all outbound requests
- **Sensitive data redaction** in logs and error messages
- **Security headers enforcement** for all HTTP clients

### 3. Performance Optimization
- **Built-in rate limiting** for all external API calls
- **Intelligent retry mechanisms** with exponential backoff
- **Request metrics collection** for monitoring and optimization
- **Connection reuse** and proper resource management

### 4. Developer Experience
- **Type-safe interfaces** for all HTTP operations
- **Comprehensive error handling** with meaningful messages
- **Easy testing** with proper mocking support
- **Clear documentation** and usage examples

## Development Guidelines

### 1. HTTP Client Usage Rules

#### ✅ DO
```typescript
// Use centralized HTTP client
import { defaultHttpClient, creativeApiClient } from '../utils/http-client.service';

// For general API calls
const response = await defaultHttpClient.get('/api/data');

// For specific use cases
const response = await creativeApiClient.post('/generate', payload);
```

#### ❌ DON'T
```typescript
// Never import axios directly
import axios from 'axios';

// Never create axios instances in services
const client = axios.create({ baseURL: 'https://api.example.com' });
```

### 2. Service Implementation Standards

#### New Service Template
```typescript
import { HttpClientService, defaultHttpClient } from '../utils/http-client.service';
import { AuditTrailService } from '../audit/audit.service';
import { AuditEventCategory, AuditEventSeverity } from '../audit/audit.types';
import logger from '../config/logger';

export class NewService {
  private httpClient: HttpClientService;
  private auditService: AuditTrailService;

  constructor(auditService: AuditTrailService, httpClient?: HttpClientService) {
    this.auditService = auditService;
    this.httpClient = httpClient || defaultHttpClient;
  }

  async makeApiCall(data: any): Promise<any> {
    try {
      const response = await this.httpClient.post('/api/endpoint', data, {
        timeout: 30000,
        headers: { 'Custom-Header': 'value' }
      });

      await this.auditService.logEvent({
        tenantId: 'system',
        category: AuditEventCategory.SYSTEM_OPERATION,
        operationType: 'API_CALL_SUCCESS',
        description: 'API call completed successfully',
        severity: AuditEventSeverity.INFO,
        timestamp: new Date(),
        metadata: { endpoint: '/api/endpoint', status: response.status }
      });

      return response.data;
    } catch (error) {
      await this.auditService.logEvent({
        tenantId: 'system',
        category: AuditEventCategory.SYSTEM_OPERATION,
        operationType: 'API_CALL_FAILED',
        description: `API call failed: ${error.message}`,
        severity: AuditEventSeverity.HIGH,
        timestamp: new Date(),
        metadata: { endpoint: '/api/endpoint', error: error.message }
      });
      throw error;
    }
  }
}
```

### 3. Testing Standards

#### Test Template
```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NewService } from './new.service';
import { AuditTrailService } from '../audit/audit.service';
import { HttpClientService } from '../utils/http-client.service';
import { AxiosResponse } from 'axios';

// Mock helper
const createMockAxiosResponse = <T>(data: T): AxiosResponse<T> => ({
  data,
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {} as any,
});

describe('NewService', () => {
  let service: NewService;
  let auditServiceMock: jest.Mocked<AuditTrailService>;
  let httpClientMock: jest.Mocked<HttpClientService>;

  beforeEach(() => {
    auditServiceMock = { logEvent: vi.fn() } as any;
    httpClientMock = {
      post: vi.fn(),
      get: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
      patch: vi.fn(),
    } as any;

    service = new NewService(auditServiceMock, httpClientMock);
  });

  it('should make successful API call', async () => {
    httpClientMock.post.mockResolvedValueOnce(
      createMockAxiosResponse({ success: true })
    );

    const result = await service.makeApiCall({ test: 'data' });

    expect(result).toEqual({ success: true });
    expect(httpClientMock.post).toHaveBeenCalledWith(
      '/api/endpoint',
      { test: 'data' },
      expect.objectContaining({ timeout: 30000 })
    );
    expect(auditServiceMock.logEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        operationType: 'API_CALL_SUCCESS'
      })
    );
  });
});
```

## Implementation Strategy

### 1. Immediate Actions (Completed)

#### ✅ Core Infrastructure
- [x] Created centralized HTTP client service
- [x] Implemented security features (sanitization, validation)
- [x] Added performance optimizations (rate limiting, metrics)
- [x] Updated existing services (Creative API, MCP)
- [x] Fixed TypeScript errors and improved type safety

#### ✅ Testing Framework
- [x] Updated test files with proper mocking
- [x] Created reusable test utilities
- [x] Ensured comprehensive test coverage

### 2. Ongoing Enforcement

#### Code Review Checklist
- [ ] No direct axios imports in service files
- [ ] Proper use of centralized HTTP client
- [ ] Audit logging for all external API calls
- [ ] Error handling with proper typing
- [ ] Tests include HTTP client mocking

#### Automated Checks
```typescript
// ESLint rule to prevent direct axios usage
{
  "rules": {
    "no-restricted-imports": [
      "error",
      {
        "paths": [
          {
            "name": "axios",
            "message": "Use centralized HTTP client service instead of direct axios import"
          }
        ]
      }
    ]
  }
}
```

### 3. Future Enhancements

#### Phase 1: Enhanced Monitoring (Next Sprint)
- [ ] Request/response interceptor for global monitoring
- [ ] Health check endpoints for external services
- [ ] Performance metrics dashboard
- [ ] Automated alerting for API failures

#### Phase 2: Advanced Features (Implemented)
- [x] Request caching layer
- [x] Circuit breaker pattern enhancement
- [ ] Request queuing for rate-limited APIs (Future consideration)
- [x] Advanced retry strategies

#### Phase 3: Enterprise Features (Long-term)
- [ ] Request signing for enhanced security
- [ ] Multi-region failover
- [ ] Advanced connection pooling
- [ ] Request compression optimization

#### Phase 4: Production Readiness & Documentation (Implemented)
## Advanced Features

This section details the advanced features implemented in the HTTP client service to enhance performance, resilience, and developer experience.

### 1. Request Caching & Deduplication

The HTTP client now supports intelligent caching for `GET` requests and request deduplication to prevent redundant calls for identical requests.

#### Configuration

Enable caching and deduplication in the `HttpClientConfig`:

```typescript
interface HttpClientConfig {
  // ... other configs
  cache?: {
    enabled: boolean;
    ttl: number; // Time to live in milliseconds (default: 60000ms)
  };
  deduplication?: boolean; // Enable request deduplication (default: false)
}
```

#### Usage

When caching is enabled, `GET` requests will first check the cache. If a valid, unexpired response is found, it's returned immediately. If deduplication is enabled, concurrent identical `GET` requests will share a single in-flight promise, preventing multiple network calls.

```typescript
import { defaultHttpClient } from '../utils/http-client.service';

// Example with caching and deduplication enabled in HttpClientService constructor
const cachedResponse = await defaultHttpClient.get('/api/cached-data');
// Subsequent identical GET requests within TTL will return cached data
const anotherCachedResponse = await defaultHttpClient.get('/api/cached-data');
```

### 2. Enhanced Circuit Breaker

The circuit breaker pattern is implemented to prevent cascading failures by temporarily blocking requests to services that are experiencing issues.

#### States

-   **CLOSED**: Normal operation. Requests pass through.
-   **OPEN**: Too many failures. Requests are immediately rejected.
-   **HALF_OPEN**: After a `resetTimeout`, a single test request is allowed. If successful, it transitions to CLOSED; otherwise, back to OPEN.

#### Configuration

Configure the circuit breaker in the `HttpClientConfig`:

```typescript
interface HttpClientConfig {
  // ... other configs
  circuitBreaker?: {
    enabled: boolean; // Enable circuit breaker (default: false)
    failureThreshold?: number; // Number of consecutive failures to open the circuit (default: 5)
    resetTimeout?: number; // Time in milliseconds before transitioning to HALF_OPEN (default: 30000ms)
    successThreshold?: number; // Number of consecutive successes in HALF_OPEN to close the circuit (default: 3)
  };
}
```

#### Usage

When enabled, the circuit breaker automatically monitors request failures and adjusts its state. If the circuit is OPEN, requests will immediately throw an `AxiosError` with code `CIRCUIT_OPEN`.

```typescript
import { defaultHttpClient } from '../utils/http-client.service';

try {
  const response = await defaultHttpClient.get('/api/unstable-service');
  // Handle successful response
} catch (error) {
  if (error.code === 'CIRCUIT_OPEN') {
    console.error('Request blocked by circuit breaker.');
    // Implement fallback logic or notify user
  } else {
    console.error('Request failed:', error.message);
  }
}
```

### 3. Advanced Retry Strategies

The HTTP client now supports configurable retry strategies with exponential or linear backoff, including jitter for better distribution of retries.

#### Configuration

Configure retry behavior in the `HttpClientConfig`:

```typescript
interface HttpClientConfig {
  // ... other configs
  retries?: number; // Maximum number of retries (default: 3)
  retryDelay?: number; // Base delay in milliseconds between retries (default: 1000ms)
  retryStrategy?: 'exponential' | 'linear' | 'none'; // Retry strategy (default: 'exponential')
}
```

#### Retryable Conditions

Requests are retried on:

-   Network errors (no response)
-   Specific HTTP status codes: `408 (Request Timeout)`, `429 (Too Many Requests)`, `500 (Internal Server Error)`, `502 (Bad Gateway)`, `503 (Service Unavailable)`, `504 (Gateway Timeout)`
-   Axios error codes: `ECONNABORTED`, `ETIMEDOUT`

#### Usage

The retry mechanism is automatically applied to requests based on the configured strategy and retryable conditions.

```typescript
import { defaultHttpClient } from '../utils/http-client.service';

// Example with custom retry configuration
const response = await defaultHttpClient.post('/api/flaky-service', data, {
  retries: 5,
  retryDelay: 500,
  retryStrategy: 'linear',
});
```

## Production Readiness

To ensure the HTTP client is production-ready, consider the following aspects:

### 1. Production Configuration Examples

Environment variables are crucial for managing sensitive information and dynamic configurations in production.

#### `.env.production` Template

```dotenv
# Core API Base URL (e.g., for internal services)
CORE_API_BASE_URL=https://api.theaigency.com/v1

# Creative API Base URL (e.g., for external AI services)
CREATIVE_API_BASE_URL=https://creative-ai.theaigency.com/v1
RUNWAYML_API_KEY=your_runwayml_api_key
ADOBE_FIREFLY_API_KEY=your_adobe_firefly_api_key

# MCP API Base URL (e.g., for real-time communication)
MCP_API_BASE_URL=https://mcp.theaigency.com/v1

# General HTTP Client Settings
HTTP_CLIENT_TIMEOUT_MS=30000
HTTP_CLIENT_RETRIES=3
HTTP_CLIENT_RETRY_DELAY_MS=1000
HTTP_CLIENT_RETRY_STRATEGY=exponential
HTTP_CLIENT_RATE_LIMIT_PER_SECOND=10

# Advanced Features (Optional)
HTTP_CLIENT_CACHE_ENABLED=true
HTTP_CLIENT_CACHE_TTL_MS=60000
HTTP_CLIENT_DEDUPLICATION_ENABLED=true
HTTP_CLIENT_CIRCUIT_BREAKER_ENABLED=true
HTTP_CLIENT_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
HTTP_CLIENT_CIRCUIT_BREAKER_RESET_TIMEOUT_MS=30000
HTTP_CLIENT_CIRCUIT_BREAKER_SUCCESS_THRESHOLD=3
```

#### Configuration Loading Example (backend/src/config/index.ts)

```typescript
// Example of how to load HTTP client configurations from environment variables
import dotenv from 'dotenv';
dotenv.config();

export const httpClientConfig = {
  default: {
    baseURL: process.env.CORE_API_BASE_URL,
    timeout: parseInt(process.env.HTTP_CLIENT_TIMEOUT_MS || '30000', 10),
    retries: parseInt(process.env.HTTP_CLIENT_RETRIES || '3', 10),
    retryDelay: parseInt(process.env.HTTP_CLIENT_RETRY_DELAY_MS || '1000', 10),
    retryStrategy: (process.env.HTTP_CLIENT_RETRY_STRATEGY || 'exponential') as 'exponential' | 'linear' | 'none',
    rateLimitPerSecond: parseInt(process.env.HTTP_CLIENT_RATE_LIMIT_PER_SECOND || '10', 10),
    cache: {
      enabled: process.env.HTTP_CLIENT_CACHE_ENABLED === 'true',
      ttl: parseInt(process.env.HTTP_CLIENT_CACHE_TTL_MS || '60000', 10),
    },
    deduplication: process.env.HTTP_CLIENT_DEDUPLICATION_ENABLED === 'true',
    circuitBreaker: {
      enabled: process.env.HTTP_CLIENT_CIRCUIT_BREAKER_ENABLED === 'true',
      failureThreshold: parseInt(process.env.HTTP_CLIENT_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '5', 10),
      resetTimeout: parseInt(process.env.HTTP_CLIENT_CIRCUIT_BREAKER_RESET_TIMEOUT_MS || '30000', 10),
      successThreshold: parseInt(process.env.HTTP_CLIENT_CIRCUIT_BREAKER_SUCCESS_THRESHOLD || '3', 10),
    },
  },
  creativeApi: {
    baseURL: process.env.CREATIVE_API_BASE_URL,
    timeout: 60000, // Specific timeout for creative API
    retries: 2,
    retryDelay: 2000,
    rateLimitPerSecond: 5,
    headers: {
      'Accept': 'application/json',
    },
  },
  mcp: {
    baseURL: process.env.MCP_API_BASE_URL,
    timeout: 15000,
    retries: 3,
    retryDelay: 1000,
    rateLimitPerSecond: 20,
    headers: {
      'Accept-Language': 'ar, en;q=0.9',
    },
  },
};
```

### 2. Performance Benchmarking

A dedicated benchmarking script is essential to measure and optimize the HTTP client's performance under various load conditions.

#### Script Location

`scripts/benchmark-http-client.js` (to be created)

#### Usage

```bash
node scripts/benchmark-http-client.js --duration 60 --concurrency 10 --target-url http://localhost:3000/api/test
```

#### Key Metrics Measured

-   Requests per second (RPS)
-   Average response time
-   Error rate
-   Throughput (data transferred)
-   Latency percentiles (P50, P90, P99)

### 3. Migration Guide

This guide provides examples for migrating existing code and adopting the new advanced features.

#### Using Caching and Deduplication

```typescript
// Before (no caching/deduplication)
const response1 = await defaultHttpClient.get('/api/data');
const response2 = await defaultHttpClient.get('/api/data'); // Redundant call

// After (with caching/deduplication enabled in HttpClientService config)
// Ensure HttpClientService is initialized with cache: { enabled: true } and deduplication: true
const response1 = await defaultHttpClient.get('/api/data'); // Fetches from network
const response2 = await defaultHttpClient.get('/api/data'); // Returns cached/deduplicated response
```

#### Implementing Circuit Breaker

```typescript
// Before (direct error handling)
try {
  const response = await defaultHttpClient.get('/api/flaky-service');
} catch (error) {
  // Manual error handling
}

// After (with circuit breaker enabled in HttpClientService config)
// Ensure HttpClientService is initialized with circuitBreaker: { enabled: true, ... }
try {
  const response = await defaultHttpClient.get('/api/flaky-service');
} catch (error) {
  if (error.code === 'CIRCUIT_OPEN') {
    console.warn('Service is currently unavailable due to circuit breaker.');
    // Implement graceful degradation or fallback
  } else {
    throw error; // Re-throw other errors
  }
}
```

#### Configuring Advanced Retries

```typescript
// Before (default retries)
const response = await defaultHttpClient.post('/api/external-service', data);

// After (custom retry strategy)
// Configure in HttpClientService constructor or per-request config
const response = await defaultHttpClient.post('/api/external-service', data, {
  retries: 5,
  retryDelay: 2000,
  retryStrategy: 'linear', // or 'exponential'
});
```

### 4. Final Validation

A comprehensive validation script ensures all features of the HTTP client work as expected end-to-end.

#### Script Location

`backend/src/utils/http-client.validation.test.ts` (to be created)

#### Test Scenarios

-   **Basic Connectivity**: Successful GET/POST requests.
-   **Rate Limiting**: Verify requests are throttled correctly.
-   **Retry Mechanism**: Simulate transient failures and confirm retries.
-   **Caching**: Test cache hits and misses, and TTL expiration.
-   **Deduplication**: Verify concurrent identical requests are deduplicated.
-   **Circuit Breaker**: Simulate failures to open the circuit, test HALF_OPEN state, and verify closing.
-   **Error Handling**: Test various error responses (4xx, 5xx) and ensure proper logging.
-   **Security**: Verify sensitive data redaction in logs.

### 5. CI/CD Integration

Update the CI/CD pipeline to include new validation checks and ensure the robustness of the HTTP client.

#### `.github/workflows/ci.yaml` Updates

-   **Add Performance Benchmarking Step**: Run the benchmarking script periodically or on significant changes.
-   **Integrate Final Validation Script**: Add a step to run `http-client.validation.test.ts` as part of the test suite.
-   **Environment Variable Checks**: Ensure production environment variables are correctly set and validated in CI.

```yaml
# Example CI/CD step
- name: Run HTTP Client Validation Tests
  run: npm test backend/src/utils/http-client.validation.test.ts

- name: Run HTTP Client Performance Benchmarks
  if: github.event_name == 'push' && github.ref == 'refs/heads/main' # Run on main branch pushes
  run: node scripts/benchmark-http-client.js --duration 30 --concurrency 5 --target-url ${{ secrets.PROD_API_URL }}
```
- [x] Update documentation
- [x] Production configuration examples
- [x] Performance benchmarking script
- [x] Migration guide
- [x] Final validation script
- [x] CI/CD integration for new checks

#### Phase 3: Enterprise Features (Long-term)
- [ ] Request signing for enhanced security
- [ ] Multi-region failover
- [ ] Advanced connection pooling
- [ ] Request compression optimization

## Service-Specific Configurations

### 1. Creative API Services
```typescript
export const creativeApiClient = new HttpClientService({
  timeout: 60000, // Long timeout for AI processing
  retries: 2, // Conservative retries for expensive operations
  retryDelay: 2000,
  rateLimitPerSecond: 5, // Respect API limits
  headers: {
    'Accept': 'application/json',
    'User-Agent': 'TheAIgency-Creative/1.0.0'
  }
});
```

### 2. Real-time Services (MCP, WebSocket)
```typescript
export const realtimeClient = new HttpClientService({
  timeout: 15000, // Faster timeout for real-time
  retries: 3, // More aggressive retries
  retryDelay: 1000,
  rateLimitPerSecond: 20,
  headers: {
    'Accept-Language': 'ar, en;q=0.9'
  }
});
```

### 3. Internal Services
```typescript
export const internalClient = new HttpClientService({
  timeout: 10000, // Standard timeout
  retries: 3,
  retryDelay: 500,
  rateLimitPerSecond: 50, // Higher rate for internal calls
  headers: {
    'X-Internal-Service': 'true'
  }
});
```

## Monitoring and Metrics

### 1. Key Performance Indicators
- **Response Time**: Average response time per service
- **Error Rate**: Percentage of failed requests
- **Rate Limit Hits**: Number of rate limit violations
- **Retry Success Rate**: Effectiveness of retry mechanisms

### 2. Alerting Thresholds
- **High Error Rate**: >5% error rate over 5 minutes
- **Slow Response**: >30s average response time
- **Rate Limit Exceeded**: >10 rate limit hits per minute
- **Service Unavailable**: >3 consecutive failures

### 3. Monitoring Dashboard
```typescript
// Example metrics endpoint
app.get('/metrics/http-client', (req, res) => {
  const metrics = {
    defaultClient: defaultHttpClient.getMetrics(),
    creativeApi: creativeApiClient.getMetrics(),
    mcp: mcpClient.getMetrics(),
    averageResponseTime: defaultHttpClient.getAverageResponseTime(5),
    errorRate: defaultHttpClient.getErrorRate(5)
  };
  res.json(metrics);
});
```

## Migration Strategy for Existing Code

### 1. Identification Phase
```bash
# Find all direct axios usage
grep -r "import.*axios" src/
grep -r "require.*axios" src/
grep -r "axios\." src/
```

### 2. Migration Checklist
- [ ] Replace axios imports with HTTP client service
- [ ] Update method calls to use HTTP client interface
- [ ] Add proper error handling and audit logging
- [ ] Update tests to mock HTTP client instead of axios
- [ ] Verify functionality with integration tests

### 3. Validation Phase
- [ ] Run full test suite
- [ ] Perform integration testing
- [ ] Monitor metrics for performance regression
- [ ] Validate security features are working

## Implementation Checklist

### Immediate Actions Required
- [ ] Add ESLint rules to `.eslintrc.js` to prevent direct axios usage
- [ ] Create custom script `scripts/check-http-client-usage.js` for validation
- [ ] Update `package.json` with pre-commit hooks and scripts
- [ ] Install required dependencies: `husky`, `lint-staged`, `glob`
- [ ] Update existing services to use centralized HTTP client
- [ ] Add monitoring endpoints for HTTP client metrics

### Development Workflow Integration
- [ ] Update CI/CD pipeline to run HTTP client usage checks
- [ ] Add HTTP client metrics to monitoring dashboard
- [ ] Create alerts for HTTP client performance issues
- [ ] Update developer onboarding documentation

### Code Review Guidelines
- [ ] Ensure no direct axios imports in new code
- [ ] Verify proper error handling and audit logging
- [ ] Check for appropriate HTTP client selection (default vs specialized)
- [ ] Validate test coverage includes HTTP client mocking

## Next Steps

1. **Switch to Code Mode** to implement enforcement files:
   - ESLint configuration updates
   - Pre-commit hook scripts
   - Package.json updates
   - Custom validation scripts

2. **Update Existing Services** to use centralized HTTP client:
   - Audit all service files for direct axios usage
   - Migrate remaining services to HTTP client service
   - Update tests to use proper mocking

3. **Monitoring Implementation**:
   - Add metrics endpoints
   - Create performance dashboards
   - Set up alerting for failures

## Conclusion

This comprehensive strategy ensures that all future HTTP client usage follows best practices for security, performance, and maintainability. By enforcing these guidelines through automated checks, code reviews, and comprehensive documentation, we prevent the recurrence of axios-related issues while building a robust foundation for external API integrations.

The centralized approach provides consistency across all services while maintaining the flexibility to customize behavior for specific use cases. Regular monitoring and metrics collection enable proactive identification and resolution of potential issues before they impact production systems.

**Key Success Metrics:**
- Zero direct axios imports in new code
- Consistent error handling across all HTTP operations
- Improved API response times through optimized configurations
- Enhanced security through request sanitization and validation
- Better developer experience through standardized patterns