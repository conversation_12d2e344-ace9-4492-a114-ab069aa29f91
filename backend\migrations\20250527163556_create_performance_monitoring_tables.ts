import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.raw(`
    -- Performance Monitoring and Health Tracking Tables

    -- Table: SystemMetrics
    -- Stores time-series system performance data.
    CREATE TABLE SystemMetrics (
        metric_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID REFERENCES Tenants(tenant_id) ON DELETE CASCADE, -- Optional, some metrics might be global
        metric_name VARCHAR(255) NOT NULL, -- e.g., 'cpu_usage', 'memory_utilization', 'disk_io', 'network_latency'
        metric_value DOUBLE PRECISION NOT NULL,
        unit VARCHAR(50), -- e.g., '%', 'MB', 'ms', 'req/s'
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        metadata JSONB -- Additional context for the metric
    );

    -- Table: ServiceHealth
    -- Tracks the health status of various microservices or components.
    CREATE TABLE ServiceHealth (
        health_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        service_name VARCHAR(255) NOT NULL,
        status VARCHAR(50) NOT NULL, -- e.g., 'healthy', 'degraded', 'unhealthy', 'unknown'
        last_check_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        details JSONB, -- Detailed health check results
        CONSTRAINT uq_service_name UNIQUE (service_name)
    );

    -- Table: Errors
    -- Logs application errors for tracking and resolution.
    CREATE TABLE Errors (
        error_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        service_name VARCHAR(255),
        error_code VARCHAR(100),
        error_message TEXT NOT NULL,
        stack_trace TEXT,
        error_severity VARCHAR(50), -- e.g., 'info', 'warning', 'error', 'critical'
        occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        resolved_at TIMESTAMP WITH TIME ZONE,
        resolved_by UUID REFERENCES Users(user_id),
        metadata JSONB -- Additional context for the error
    );

    -- Table: AutoHealingActions
    -- Records auto-healing actions taken by the system.
    CREATE TABLE AutoHealingActions (
        action_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        triggered_by_error_id UUID REFERENCES Errors(error_id),
        action_type VARCHAR(100) NOT NULL, -- e.g., 'restart_service', 'scale_up', 'clear_cache'
        action_details JSONB, -- Details of the action taken
        status VARCHAR(50) NOT NULL, -- e.g., 'initiated', 'in_progress', 'completed', 'failed'
        action_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: Escalations
    -- Tracks escalations triggered by monitoring alerts.
    CREATE TABLE Escalations (
        escalation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        source_metric_id UUID REFERENCES SystemMetrics(metric_id),
        source_error_id UUID REFERENCES Errors(error_id),
        escalation_level INT NOT NULL, -- e.g., 1 (notification), 2 (page), 3 (on-call)
        status VARCHAR(50) NOT NULL, -- e.g., 'open', 'acknowledged', 'resolved'
        triggered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        resolved_at TIMESTAMP WITH TIME ZONE,
        resolved_by UUID REFERENCES Users(user_id),
        notes TEXT
    );

    -- Indexes for performance optimization
    CREATE INDEX idx_systemmetrics_tenant_id ON SystemMetrics(tenant_id);
    CREATE INDEX idx_systemmetrics_name_time ON SystemMetrics(metric_name, timestamp DESC); -- For time-series queries
    CREATE INDEX idx_servicehealth_status ON ServiceHealth(status);
    CREATE INDEX idx_errors_tenant_id ON Errors(tenant_id);
    CREATE INDEX idx_errors_service_name ON Errors(service_name);
    CREATE INDEX idx_errors_occurred_at ON Errors(occurred_at DESC);
    CREATE INDEX idx_autohealingactions_tenant_id ON AutoHealingActions(tenant_id);
    CREATE INDEX idx_autohealingactions_error_id ON AutoHealingActions(triggered_by_error_id);
    CREATE INDEX idx_escalations_tenant_id ON Escalations(tenant_id);
    CREATE INDEX idx_escalations_triggered_at ON Escalations(triggered_at DESC);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.raw(`
    DROP TABLE IF EXISTS Escalations;
    DROP TABLE IF EXISTS AutoHealingActions;
    DROP TABLE IF EXISTS Errors;
    DROP TABLE IF EXISTS ServiceHealth;
    DROP TABLE IF EXISTS SystemMetrics;
  `);
}