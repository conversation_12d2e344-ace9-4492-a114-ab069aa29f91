"use strict";
// Configuration exports for the backend
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
exports.config = {
    // API Keys (these would normally come from environment variables)
    openaiApiKey: '',
    runwaymlApiKey: '',
    adobeFireflyApiKey: '',
    // Database configuration
    database: {
        host: 'localhost',
        port: 5432,
        user: 'postgres',
        password: '',
        name: 'theaigency_db'
    },
    // JWT configuration
    jwt: {
        secret: 'your_super_secret_jwt_key_here_12345',
        accessTokenExpiration: '15m',
        refreshTokenExpiration: '7d'
    },
    // Server configuration
    server: {
        port: 3000,
        nodeEnv: 'development'
    },
    // Supabase configuration
    supabase: {
        url: '',
        anonKey: '',
        serviceRoleKey: ''
    },
    // Redis configuration
    redis: {
        url: 'redis://localhost:6379'
    },
    // Qdrant configuration
    qdrant: {
        url: 'http://localhost:6333'
    }
};
exports.default = exports.config;
