import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Enable the pgvector extension
  // await knex.raw('CREATE EXTENSION IF NOT EXISTS pgvector'); // Commented out due to Docker environment issues

  // Create the knowledge_bases table with multi-tenant isolation
  await knex.schema.createTable('knowledge_bases', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('tenant_id').notNullable().comment('For multi-tenant isolation');
    table.string('name').notNullable();
    table.text('description');
    table.timestamps(true, true);
    table.unique(['tenant_id', 'name']); // Ensure unique knowledge base names per tenant
  });

  // Create the documents table
  await knex.schema.createTable('documents', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('knowledge_base_id').notNullable().references('id').inTable('knowledge_bases').onDelete('CASCADE');
    table.uuid('tenant_id').notNullable().comment('For multi-tenant isolation');
    table.string('file_name').notNullable();
    table.string('file_type').notNullable();
    table.text('content_hash').notNullable().unique(); // To prevent duplicate document uploads
    table.integer('chunk_count').defaultTo(0);
    table.timestamps(true, true);
    table.index(['knowledge_base_id', 'tenant_id']);
  });

  // Create the document_chunks table for vector embeddings
  await knex.schema.createTable('document_chunks', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('document_id').notNullable().references('id').inTable('documents').onDelete('CASCADE');
    table.uuid('knowledge_base_id').notNullable().references('id').inTable('knowledge_bases').onDelete('CASCADE');
    table.uuid('tenant_id').notNullable().comment('For multi-tenant isolation');
    table.text('content').notNullable();
    // table.specificType('embedding', 'vector(1536)').comment('OpenAI ada-002 embedding dimension'); // Commented out due to pgvector dependency
    table.integer('chunk_index').notNullable();
    table.timestamps(true, true);
    table.index(['document_id', 'chunk_index']);
    table.index(['knowledge_base_id', 'tenant_id']);
    // table.index(['embedding'], 'embedding_gin_idx', 'GIN'); // Commented out due to pgvector dependency
  });

  // Create the ai_operations_audit_trail table
  await knex.schema.createTable('ai_operations_audit_trail', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('tenant_id').notNullable();
    table.uuid('user_id').nullable(); // User who initiated the AI operation
    table.string('operation_type').notNullable(); // e.g., 'vector_search', 'document_upload', 'ai_chat'
    table.jsonb('request_payload').nullable();
    table.jsonb('response_payload').nullable();
    table.string('ai_model_used').nullable();
    table.decimal('cost', 10, 6).nullable(); // Cost of the operation
    table.integer('tokens_used').nullable();
    table.string('status').notNullable(); // 'success', 'failure'
    table.text('error_message').nullable();
    table.timestamps(true, true);
    table.index(['tenant_id', 'operation_type']);
    table.index('created_at');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('ai_operations_audit_trail');
  await knex.schema.dropTableIfExists('document_chunks');
  await knex.schema.dropTableIfExists('documents');
  await knex.schema.dropTableIfExists('knowledge_bases');
  // await knex.raw('DROP EXTENSION IF EXISTS pgvector'); // Commented out due to pgvector dependency
}