"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditController = void 0;
const express_1 = require("express");
const audit_service_1 = require("./audit.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger"));
const type_guards_1 = require("../utils/type-guards");
class AuditController {
    router;
    auditTrailService;
    constructor() {
        this.router = (0, express_1.Router)();
        this.auditTrailService = new audit_service_1.AuditTrailService();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/', (0, asyncHandler_1.asyncHandler)(this.getAuditLogs));
        this.router.get('/report/compliance', (0, asyncHandler_1.asyncHandler)(this.getComplianceReport));
        this.router.get('/analytics', (0, asyncHandler_1.asyncHandler)(this.getAuditAnalytics));
        // Future: Add endpoints for audit configuration management, data retention policies etc.
    }
    // Helper to extract tenantId and userId from request (assuming authentication middleware populates req.user)
    getAuthContext(req) {
        const tenantId = req.headers['x-tenant-id'] || 'mock-tenant-id';
        const userId = req.user?.id || 'mock-user-id';
        return { tenantId, userId };
    }
    /**
     * GET /audit - Retrieve audit logs with filtering and pagination.
     */
    async getAuditLogs(req, res) {
        const { tenantId } = this.getAuthContext(req);
        const { page, limit, userId, category, severity, operationType, startDate, endDate, resourceId, keywords } = req.query;
        const filters = {
            tenantId: tenantId,
            userId: (0, type_guards_1.getParam)(userId),
            category: (0, type_guards_1.getParam)(category),
            severity: (0, type_guards_1.getParam)(severity),
            operationType: (0, type_guards_1.getParam)(operationType),
            startDate: startDate ? new Date((0, type_guards_1.getParam)(startDate)) : undefined,
            endDate: endDate ? new Date((0, type_guards_1.getParam)(endDate)) : undefined,
            resourceId: (0, type_guards_1.getParam)(resourceId),
            keywords: (0, type_guards_1.getParam)(keywords),
        };
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(limit) || 20;
        logger_1.default.info(`Fetching audit logs for tenant ${tenantId} with filters: ${JSON.stringify(filters)}`);
        const auditLogs = await this.auditTrailService.getAuditLogs(filters, pageNum, limitNum);
        res.status(200).json(auditLogs);
    }
    /**
     * GET /audit/report/compliance - Generate compliance reports.
     */
    async getComplianceReport(req, res) {
        const { tenantId } = this.getAuthContext(req);
        const { complianceStandard, startDate, endDate } = req.query;
        if (!complianceStandard || !startDate || !endDate) {
            throw new errors_1.CustomError('Compliance standard, start date, and end date are required.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        logger_1.default.info(`Generating compliance report for tenant ${tenantId}, standard: ${complianceStandard}`);
        const report = await this.auditTrailService.generateComplianceReport(tenantId, complianceStandard, new Date((0, type_guards_1.getParam)(startDate)), new Date((0, type_guards_1.getParam)(endDate)));
        res.status(200).json(report);
    }
    /**
     * GET /audit/analytics - Retrieve audit analytics.
     */
    async getAuditAnalytics(req, res) {
        const { tenantId } = this.getAuthContext(req);
        const { startDate, endDate } = req.query;
        if (!startDate || !endDate) {
            throw new errors_1.CustomError('Start date and end date are required for analytics.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        logger_1.default.info(`Fetching audit analytics for tenant ${tenantId}`);
        const analytics = await this.auditTrailService.getAuditAnalytics(tenantId, new Date((0, type_guards_1.getParam)(startDate)), new Date((0, type_guards_1.getParam)(endDate)));
        res.status(200).json(analytics);
    }
}
exports.AuditController = AuditController;
