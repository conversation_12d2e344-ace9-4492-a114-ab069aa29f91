"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.createTable('xp_events', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('org_id').notNullable();
        table.uuid('agent_id');
        table.decimal('powerops').notNullable();
        table.integer('xp_gained').notNullable();
        table.string('reason').notNullable();
        table.jsonb('metadata');
        table.timestamp('timestamp').notNullable().defaultTo(knex.fn.now());
    });
}
async function down(knex) {
    await knex.schema.dropTable('xp_events');
}
