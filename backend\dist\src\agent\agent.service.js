"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentService = void 0;
const agent_repository_1 = require("./agent.repository");
const paim_service_1 = require("../paim/paim.service");
const paim_repository_1 = require("../paim/paim.repository");
const audit_service_1 = require("../audit/audit.service");
const logger_1 = __importDefault(require("../config/logger"));
const authorization_service_1 = require("../auth/authorization.service");
const permissions_1 = require("../auth/permissions");
const errors_1 = require("../utils/errors");
class AgentService {
    agentRepository;
    paimService;
    auditTrailService;
    constructor() {
        this.auditTrailService = new audit_service_1.AuditTrailService();
        this.agentRepository = new agent_repository_1.AgentRepository(this.auditTrailService);
        // PaimService constructor now expects a PaimRepository and AuditTrailService
        this.paimService = new paim_service_1.PaimService(new paim_repository_1.PaimRepository(this.auditTrailService), this.auditTrailService, {}, // NotificationService placeholder
        {} // WebSocketService placeholder
        );
    }
    // Agent Lifecycle Management
    async getAllAgents(user, options) {
        logger_1.default.info(`Fetching all agents for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AGENT_READ)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view agents.', 403);
        }
        return this.agentRepository.getAllAgents(user.tenantId, options);
    }
    async getAgentById(user, agentId) {
        logger_1.default.info(`Fetching agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AGENT_READ)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view agent.', 403);
        }
        const agent = await this.agentRepository.getAgentById(user.tenantId, agentId);
        if (agent && !authorization_service_1.authorizationService.canAccessResource(user, { ownerId: agent.ownerId, organizationId: agent.tenantId, teamId: agent.teamId }, 'read')) {
            throw new errors_1.AuthorizationError('Forbidden: You do not have access to this specific agent.', 403);
        }
        return agent;
    }
    async createAgent(user, agentData) {
        logger_1.default.info(`Creating new agent for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AGENT_CREATE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to create agent.', 403);
        }
        // Validate PAIM instance ID and ensure user has access to it
        const paimInstance = await this.paimService.getPaimInstanceById(agentData.paimInstanceId, user);
        if (!paimInstance) {
            throw new errors_1.CustomError(`PAIM instance with ID ${agentData.paimInstanceId} not found.`, { originalStatusCode: 404 });
        }
        // Further check if the user has permission to create agents within this PAIM instance
        // This could be a more granular check if needed, e.g., based on PAIM hierarchy.
        // For now, the AGENT_CREATE permission combined with PAIM_READ on the instance is sufficient.
        const agent = await this.agentRepository.createAgent(user.tenantId, agentData);
        await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_created', agent.id, agent, user.userId);
        return agent;
    }
    async updateAgent(user, agentId, agentData) {
        logger_1.default.info(`Updating agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AGENT_UPDATE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to update agent.', 403);
        }
        const existingAgent = await this.agentRepository.getAgentById(user.tenantId, agentId);
        if (!existingAgent) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalStatusCode: 404 });
        }
        if (!authorization_service_1.authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'update')) {
            throw new errors_1.AuthorizationError('Forbidden: You do not have access to update this specific agent.', 403);
        }
        const updatedAgent = await this.agentRepository.updateAgent(user.tenantId, agentId, agentData);
        if (updatedAgent) {
            await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_updated', agentId, updatedAgent, user.userId);
        }
        return updatedAgent;
    }
    async deleteAgent(user, agentId) {
        logger_1.default.info(`Deleting agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AGENT_DELETE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to delete agent.', 403);
        }
        const existingAgent = await this.agentRepository.getAgentById(user.tenantId, agentId);
        if (!existingAgent) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalStatusCode: 404 });
        }
        if (!authorization_service_1.authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'delete')) {
            throw new errors_1.AuthorizationError('Forbidden: You do not have access to delete this specific agent.', 403);
        }
        const deleted = await this.agentRepository.deleteAgent(user.tenantId, agentId);
        if (deleted) {
            await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_deleted', agentId, { agentId }, user.userId);
        }
        return deleted;
    }
    // Agent Assignment
    async assignAgent(user, assignmentData) {
        logger_1.default.info(`Assigning agent ${assignmentData.agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AGENT_UPDATE)) { // Assuming assign is an update operation
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to assign agent.', 403);
        }
        const existingAgent = await this.agentRepository.getAgentById(user.tenantId, assignmentData.agentId);
        if (!existingAgent) {
            throw new errors_1.CustomError(`Agent with ID ${assignmentData.agentId} not found.`, { originalStatusCode: 404 });
        }
        if (!authorization_service_1.authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'update')) {
            throw new errors_1.AuthorizationError('Forbidden: You do not have access to assign this specific agent.', 403);
        }
        const assignment = await this.agentRepository.assignAgent(user.tenantId, assignmentData);
        await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_assigned', assignment.id, assignment, user.userId);
        return assignment;
    }
    // Workflow Orchestration and Agent Execution
    async executeWorkflow(user, workflowId, executionRequest) {
        logger_1.default.info(`Executing workflow ${workflowId} for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.WORKFLOW_CREATE)) { // Assuming execute workflow requires workflow creation permission
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to execute workflow.', 403);
        }
        // In a real scenario, you would fetch the workflow and check resource-level permissions on it.
        // For now, we'll assume the permission check above is sufficient.
        // Placeholder for actual workflow execution logic
        // This would involve:
        // 1. Fetching workflow definition
        // 2. Validating input data
        // 3. Scheduling tasks and assigning to agents
        // 4. Monitoring task execution
        // 5. Handling errors and retries
        // 6. Recording execution history
        const executionStatus = {
            executionId: 'mock-execution-id-' + Math.random().toString(36).substring(7),
            workflowId: workflowId,
            status: 'running',
            startTime: new Date().toISOString(),
            outputData: {},
        };
        // Simulate async execution
        setTimeout(async () => {
            executionStatus.status = 'completed';
            executionStatus.endTime = new Date().toISOString();
            executionStatus.outputData = { message: 'Workflow completed successfully (mock)' };
            await this.agentRepository.recordWorkflowExecution(user.tenantId, executionStatus);
            logger_1.default.info(`Workflow ${workflowId} completed for tenant: ${user.tenantId}`);
        }, 5000); // Simulate 5 seconds execution
        await this.agentRepository.recordAuditTrail(user.tenantId, 'workflow_execution_initiated', workflowId, executionRequest, user.userId);
        return executionStatus;
    }
    // Agent Performance Monitoring
    async getAgentPerformance(user, agentId, startDate, endDate) {
        logger_1.default.info(`Fetching performance metrics for agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AGENT_READ)) { // Assuming performance read is part of agent read
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view agent performance.', 403);
        }
        const existingAgent = await this.agentRepository.getAgentById(user.tenantId, agentId);
        if (!existingAgent) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalStatusCode: 404 });
        }
        if (!authorization_service_1.authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'read')) {
            throw new errors_1.AuthorizationError('Forbidden: You do not have access to view performance for this specific agent.', 403);
        }
        return this.agentRepository.getAgentPerformanceMetrics(user.tenantId, agentId, startDate, endDate);
    }
}
exports.AgentService = AgentService;
