"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.createTable('knowledge_bases', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
        table.uuid('tenant_id').notNullable().comment('For multi-tenant isolation');
        table.string('name').notNullable();
        table.text('description');
        table.timestamps(true, true);
        table.unique(['tenant_id', 'name']);
    });
    await knex.schema.createTable('documents', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
        table.uuid('knowledge_base_id').notNullable().references('id').inTable('knowledge_bases').onDelete('CASCADE');
        table.uuid('tenant_id').notNullable().comment('For multi-tenant isolation');
        table.string('file_name').notNullable();
        table.string('file_type').notNullable();
        table.text('content_hash').notNullable().unique();
        table.integer('chunk_count').defaultTo(0);
        table.timestamps(true, true);
        table.index(['knowledge_base_id', 'tenant_id']);
    });
    await knex.schema.createTable('document_chunks', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
        table.uuid('document_id').notNullable().references('id').inTable('documents').onDelete('CASCADE');
        table.uuid('knowledge_base_id').notNullable().references('id').inTable('knowledge_bases').onDelete('CASCADE');
        table.uuid('tenant_id').notNullable().comment('For multi-tenant isolation');
        table.text('content').notNullable();
        table.integer('chunk_index').notNullable();
        table.timestamps(true, true);
        table.index(['document_id', 'chunk_index']);
        table.index(['knowledge_base_id', 'tenant_id']);
    });
    await knex.schema.createTable('ai_operations_audit_trail', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
        table.uuid('tenant_id').notNullable();
        table.uuid('user_id').nullable();
        table.string('operation_type').notNullable();
        table.jsonb('request_payload').nullable();
        table.jsonb('response_payload').nullable();
        table.string('ai_model_used').nullable();
        table.decimal('cost', 10, 6).nullable();
        table.integer('tokens_used').nullable();
        table.string('status').notNullable();
        table.text('error_message').nullable();
        table.timestamps(true, true);
        table.index(['tenant_id', 'operation_type']);
        table.index('created_at');
    });
}
async function down(knex) {
    await knex.schema.dropTableIfExists('ai_operations_audit_trail');
    await knex.schema.dropTableIfExists('document_chunks');
    await knex.schema.dropTableIfExists('documents');
    await knex.schema.dropTableIfExists('knowledge_bases');
}
