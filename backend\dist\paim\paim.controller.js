"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express = __importStar(require("express")); // Import express for types
const paim_service_1 = require("./paim.service");
const paim_repository_1 = require("./paim.repository");
const audit_service_1 = require("../audit/audit.service");
const asyncHandler_1 = require("../utils/asyncHandler"); // Named import
const type_guards_1 = require("../utils/type-guards");
const paimRouter = express.Router();
const auditTrailService = new audit_service_1.AuditTrailService();
const paimRepository = new paim_repository_1.PaimRepository(auditTrailService);
const paimService = new paim_service_1.PaimService(paimRepository, auditTrailService);
// Middleware to extract tenantId and userId from request (assuming JWT or similar auth)
const authenticateAndAuthorize = (req, res, next) => {
    // Placeholder for authentication and authorization logic
    // In a real app, this would parse JWT, validate user, and set req.tenantId, req.userId, req.userPaimTier
    req.tenantId = req.headers['x-tenant-id'] || 'default-tenant-id'; // Example: get from header
    req.userId = req.user?.userId || 'default-user-id'; // Use req.user?.userId as per JwtPayload
    // req.userPaimTier = req.user?.paimTier; // Example: get user's PAIM tier
    if (!req.tenantId || !req.userId) {
        res.status(401).json({ message: 'Unauthorized: Tenant ID or User ID missing.' });
        return; // Explicitly return after sending response
    }
    next();
};
paimRouter.use(authenticateAndAuthorize);
// PAIM Instance Management
paimRouter.get('/', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { status, page, size, sort } = req.query;
    const filters = { status: (0, type_guards_1.getParam)(status) };
    const pagination = { page: parseInt((0, type_guards_1.getParam)(page, '1')), size: parseInt((0, type_guards_1.getParam)(size, '10')), sort: (0, type_guards_1.getParam)(sort, 'createdAt,desc') };
    const { data, totalElements } = await paimService.getAllPaimInstances((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), filters, pagination);
    const paginationMetadata = {
        totalElements,
        totalPages: Math.ceil(totalElements / pagination.size),
        currentPage: pagination.page,
        pageSize: pagination.size,
    };
    res.status(200).json({ data, pagination: paginationMetadata });
}));
paimRouter.post('/', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const paimInstance = await paimService.createPaimInstance(req.body, (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'));
    res.status(201).json(paimInstance);
}));
paimRouter.get('/:paimInstanceId', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { paimInstanceId } = req.params;
    const paimInstance = await paimService.getPaimInstanceById((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'));
    res.status(200).json(paimInstance);
}));
paimRouter.put('/:paimInstanceId', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { paimInstanceId } = req.params;
    const updateRequest = req.body;
    const updatedPaimInstance = await paimService.updatePaimInstance((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), updateRequest);
    res.status(200).json(updatedPaimInstance);
}));
paimRouter.delete('/:paimInstanceId', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { paimInstanceId } = req.params;
    await paimService.deletePaimInstance((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'));
    res.status(204).send();
}));
// PAIM Tier Management
paimRouter.post('/:paimInstanceId/tier-change-requests', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { paimInstanceId } = req.params;
    const tierChangeRequest = req.body;
    const status = await paimService.requestPaimTierChange((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), tierChangeRequest);
    res.status(202).json(status);
}));
// PAIM Hierarchy Management
paimRouter.get('/:paimInstanceId/hierarchy', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { paimInstanceId } = req.params;
    const hierarchy = await paimService.getPaimHierarchy((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'));
    res.status(200).json(hierarchy);
}));
paimRouter.put('/:paimInstanceId/hierarchy', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { paimInstanceId } = req.params;
    const hierarchyUpdate = req.body;
    const updatedHierarchy = await paimService.updatePaimHierarchy((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), hierarchyUpdate);
    res.status(200).json(updatedHierarchy);
}));
// Cross-PAIM Communication
paimRouter.post('/:paimInstanceId/communicate', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { paimInstanceId } = req.params;
    const communicationRequest = req.body;
    const response = await paimService.communicateWithPaim((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), communicationRequest.targetPaimInstanceId, communicationRequest.message, communicationRequest.messageType || 'text');
    res.status(200).json(response);
}));
exports.default = paimRouter;
