import { Request, Response, NextFunction } from 'express';
import { BaseError, CustomError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError, DatabaseError, ExternalServiceError, ServiceUnavailableError, ConflictError } from '../utils/errors';
import logger from '../config/logger'; // Assuming a logger is configured

export const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  if (err instanceof BaseError) {
    logger.error(`Operational Error: ${err.message}`, {
      statusCode: err.statusCode,
      errorCode: err.errorCode,
      details: err.details,
      path: req.path,
      method: req.method,
      ip: req.ip,
    });

    return res.status(err.statusCode).json({
      status: 'error',
      code: err.errorCode,
      message: err.message,
      details: err.details,
    });
  }

  // Handle unexpected errors
  logger.error(`Unexpected Error: ${err.message}`, {
    stack: err.stack,
    path: req.path,
    method: req.method,
    ip: req.ip,
  });

  return res.status(500).json({
    status: 'error',
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred.',
  });
};