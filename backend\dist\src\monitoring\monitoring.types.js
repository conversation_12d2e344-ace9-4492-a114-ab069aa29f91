"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EscalationPriority = exports.EscalationStatus = exports.HealthStatus = exports.ErrorSeverity = void 0;
// Re-export for backward compatibility
var audit_types_1 = require("../audit/audit.types");
Object.defineProperty(exports, "ErrorSeverity", { enumerable: true, get: function () { return audit_types_1.AuditEventSeverity; } });
var HealthStatus;
(function (HealthStatus) {
    HealthStatus["OK"] = "OK";
    HealthStatus["WARNING"] = "WARNING";
    HealthStatus["CRITICAL"] = "CRITICAL";
    HealthStatus["UNKNOWN"] = "UNKNOWN";
})(HealthStatus || (exports.HealthStatus = HealthStatus = {}));
var EscalationStatus;
(function (EscalationStatus) {
    EscalationStatus["PENDING"] = "PENDING";
    EscalationStatus["ESCALATED"] = "ESCALATED";
    EscalationStatus["IN_PROGRESS"] = "IN_PROGRESS";
    EscalationStatus["RESOLVED"] = "RESOLVED";
    EscalationStatus["CLOSED"] = "CLOSED";
})(EscalationStatus || (exports.EscalationStatus = EscalationStatus = {}));
var EscalationPriority;
(function (EscalationPriority) {
    EscalationPriority["LOW"] = "LOW";
    EscalationPriority["MEDIUM"] = "MEDIUM";
    EscalationPriority["HIGH"] = "HIGH";
    EscalationPriority["URGENT"] = "URGENT";
})(EscalationPriority || (exports.EscalationPriority = EscalationPriority = {}));
