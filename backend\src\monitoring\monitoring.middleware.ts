import { Request, Response, NextFunction } from 'express-serve-static-core';
import { MonitoringService } from './monitoring.service';
import { MonitoringRepository } from './monitoring.repository';
import { AuditTrailService } from '../audit/audit.service';
import { PaimService } from '../paim/paim.service';
import { PowerOpsService } from '../powerops/powerops.service';
import { CulturalSensitivityService } from '../cultural-sensitivity/cultural-sensitivity.service';
import db from '../database/db'; // Default import for knex instance
import logger from '../config/logger';
import { SystemError, ErrorSeverity, PerformanceMetrics, HealthStatus } from './monitoring.types';
import { v4 as uuidv4 } from 'uuid';

export class MonitoringMiddleware {
  private monitoringService: MonitoringService;

  constructor() {
    const monitoringRepository = new MonitoringRepository();
    const auditTrailService = new AuditTrailService();
    const paimService = new PaimService(
      null as any,
      auditTrailService,
      {} as any, // NotificationService placeholder
      {} as any  // WebSocketService placeholder
    ); // PaimRepository and AuditTrailService needed
    const powerOpsService = new PowerOpsService();
    const culturalSensitivityService = new CulturalSensitivityService(db); // Knex instance needed

    this.monitoringService = new MonitoringService(
      monitoringRepository,
      auditTrailService,
      paimService,
      powerOpsService,
      culturalSensitivityService
    );
  }

  // Middleware for request/response monitoring and performance metric collection
  requestMonitor = async (req: Request, res: Response, next: NextFunction) => {
    const start = process.hrtime.bigint();

    res.on('finish', async () => {
      const end = process.hrtime.bigint();
      const duration = Number(end - start) / 1_000_000; // duration in milliseconds

      const serviceName = req.baseUrl.split('/')[2] || 'unknown-service'; // Extract service name from URL
      const endpoint = `${req.method} ${req.originalUrl}`;
      const statusCode = res.statusCode;

      logger.info(`Request: ${endpoint}, Status: ${statusCode}, Duration: ${duration}ms`);

      // Collect performance metrics
      const metrics: PerformanceMetrics = {
        cpuUsage: 0, // Placeholder - actual CPU usage per request is complex
        memoryUsage: 0, // Placeholder
        diskUsage: 0, // Placeholder
        networkLatency: 0, // Placeholder
        requestPerSecond: 1, // This metric is usually aggregated over time
        errorRate: statusCode >= 400 ? 1 : 0, // Simple error rate for this request
        responseTime: duration,
      };

      try {
        await this.monitoringService.recordPerformanceMetrics(serviceName, metrics);
      } catch (error) {
        logger.error(`Failed to record performance metrics: ${error}`);
      }
    });

    next();
  };

  // Middleware for error tracking and classification
  errorTracker = async (err: any, req: Request, res: Response, next: NextFunction) => {
    logger.error(`Unhandled error: ${err.message}`, { stack: err.stack, path: req.path });

    const serviceName = req.baseUrl.split('/')[2] || 'unknown-service';
    const errorId = uuidv4();
    const systemError: SystemError = {
      id: errorId,
      serviceName: serviceName,
      code: err.code || 'UNKNOWN_ERROR',
      message: err.message,
      severity: ErrorSeverity.CRITICAL, // Default to critical for unhandled errors
      timestamp: new Date().toISOString(),
      details: {
        stack: err.stack,
        path: req.path,
        method: req.method,
        body: req.body,
        query: req.query,
        params: req.params,
      },
      isResolved: false,
    };

    try {
      await this.monitoringService['repository'].addSystemError(systemError);
      // Trigger alert for the error
      await this.monitoringService.triggerAlert({
        id: uuidv4(),
        type: 'error',
        serviceName: systemError.serviceName, // Corrected from serviceError
        message: `System Error: ${systemError.message} (Code: ${systemError.code})`,
        timestamp: systemError.timestamp,
        severity: systemError.severity,
        isAcknowledged: false,
      });
    } catch (monitoringError) {
      logger.error(`Failed to record or alert system error: ${monitoringError}`);
    }

    // Pass the error to the next error handling middleware or send a response
    next(err);
  };

  // Health check endpoint middleware (can be used for specific routes)
  healthCheck = (serviceName: string) => async (req: Request, res: Response, next: NextFunction) => {
    try {
      // In a real scenario, this would perform actual checks (DB connection, external services)
      const healthStatus = {
        serviceName: serviceName,
        status: HealthStatus.OK, // Placeholder
        timestamp: new Date().toISOString(),
        details: 'Service is operational.',
      };
      await this.monitoringService.recordSystemHealth(healthStatus);
      res.status(200).json(healthStatus);
    } catch (error) {
      logger.error(`Health check failed for ${serviceName}: ${error}`);
      res.status(500).json({ serviceName, status: HealthStatus.CRITICAL, timestamp: new Date().toISOString(), details: 'Health check failed.' });
    }
  };
}