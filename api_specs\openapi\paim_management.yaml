openapi: 3.0.0
info:
  title: The AIgency & PAIM Management API
  version: 1.0.0
  description: API endpoints for managing PAIM instances, tiers, and inter-PAIM communication.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: PAIM Instances
    description: Management of PAIM instances
  - name: PAIM Tiers
    description: Workflows for PAIM tier upgrades and downgrades
  - name: Cross-PAIM Communication
    description: Endpoints for communication between different PAIM instances
  - name: PAIM Hierarchy
    description: Management of PAIM hierarchical relationships

paths:
  /paim-instances:
    get:
      summary: Get all PAIM instances
      operationId: getAllPaimInstances
      tags:
        - PAIM Instances
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/SizeParam'
        - $ref: '#/components/parameters/SortParam'
        - in: query
          name: status
          schema:
            type: string
            enum: [active, inactive, suspended]
          description: Filter by PAIM instance status.
      responses:
        '200':
          description: List of PAIM instances
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PaimInstance'
                  pagination:
                    $ref: '#/components/schemas/PaginationMetadata'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      summary: Create a new PAIM instance
      operationId: createPaimInstance
      tags:
        - PAIM Instances
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaimInstanceRequest'
      responses:
        '201':
          description: PAIM instance created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaimInstance'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '409':
          description: Conflict, PAIM instance with name already exists
          content:
            application/json:
              schema:
                $ref: '../auth.yaml#/components/schemas/ErrorResponse'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /paim-instances/{paimInstanceId}:
    get:
      summary: Get a specific PAIM instance by ID
      operationId: getPaimInstanceById
      tags:
        - PAIM Instances
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: paimInstanceId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the PAIM instance.
      responses:
        '200':
          description: PAIM instance details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaimInstance'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      summary: Update an existing PAIM instance
      operationId: updatePaimInstance
      tags:
        - PAIM Instances
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: paimInstanceId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the PAIM instance to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePaimInstanceRequest'
      responses:
        '200':
          description: PAIM instance updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaimInstance'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      summary: Delete a PAIM instance
      operationId: deletePaimInstance
      tags:
        - PAIM Instances
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: paimInstanceId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the PAIM instance to delete.
      responses:
        '204':
          description: PAIM instance deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /paim-instances/{paimInstanceId}/tier-change-requests:
    post:
      summary: Request a PAIM tier upgrade or downgrade
      operationId: requestPaimTierChange
      tags:
        - PAIM Tiers
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: paimInstanceId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the PAIM instance requesting a tier change.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaimTierChangeRequest'
      responses:
        '202':
          description: Tier change request accepted for processing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaimTierChangeRequestStatus'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /paim-instances/{paimInstanceId}/communicate:
    post:
      summary: Send a message to another PAIM instance
      operationId: communicateWithPaim
      tags:
        - Cross-PAIM Communication
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: paimInstanceId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the source PAIM instance.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CrossPaimCommunicationRequest'
      responses:
        '200':
          description: Message sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Message successfully delivered to target PAIM.
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /paim-instances/{paimInstanceId}/hierarchy:
    get:
      summary: Get the hierarchical structure of a PAIM instance
      operationId: getPaimHierarchy
      tags:
        - PAIM Hierarchy
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: paimInstanceId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the PAIM instance to retrieve hierarchy for.
      responses:
        '200':
          description: PAIM hierarchy structure
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaimHierarchy'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      summary: Update the hierarchical structure of a PAIM instance
      operationId: updatePaimHierarchy
      tags:
        - PAIM Hierarchy
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: paimInstanceId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the PAIM instance to update hierarchy for.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaimHierarchyUpdate'
      responses:
        '200':
          description: PAIM hierarchy updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaimHierarchy'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      in: query
      name: page
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number for pagination.
    SizeParam:
      in: query
      name: size
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page.
    SortParam:
      in: query
      name: sort
      schema:
        type: string
      description: |
        Sort order for results. Format: `field_name,direction`.
        Example: `name,asc` or `createdAt,desc`.

  schemas:
    PaimInstance:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: d290f1ee-6c54-4b01-90e6-d701748f0851
        name:
          type: string
          example: MyCompanyPAIM
        description:
          type: string
          nullable: true
          example: PAIM instance for MyCompany's operations.
        ownerId:
          type: string
          format: uuid
          description: User ID of the owner of this PAIM instance.
        tier:
          $ref: '../auth.yaml#/components/schemas/PaimTier'
        status:
          type: string
          enum: [active, inactive, suspended]
          example: active
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    CreatePaimInstanceRequest:
      type: object
      required:
        - name
        - ownerId
        - tier
      properties:
        name:
          type: string
          example: NewCompanyPAIM
        description:
          type: string
          nullable: true
          example: PAIM instance for a new client.
        ownerId:
          type: string
          format: uuid
          description: User ID of the owner for the new PAIM instance.
        tier:
          $ref: '../auth.yaml#/components/schemas/PaimTier'

    UpdatePaimInstanceRequest:
      type: object
      properties:
        name:
          type: string
          example: UpdatedCompanyPAIM
        description:
          type: string
          nullable: true
          example: Updated description for MyCompany's operations.
        status:
          type: string
          enum: [active, inactive, suspended]
          example: active

    PaimTierChangeRequest:
      type: object
      required:
        - requestedTier
        - reason
      properties:
        requestedTier:
          $ref: '../auth.yaml#/components/schemas/PaimTier'
        reason:
          type: string
          description: Reason for the tier change request.
          example: Need higher capacity for new project.

    PaimTierChangeRequestStatus:
      type: object
      properties:
        requestId:
          type: string
          format: uuid
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        paimInstanceId:
          type: string
          format: uuid
          example: d290f1ee-6c54-4b01-90e6-d701748f0851
        requestedTier:
          $ref: '../auth.yaml#/components/schemas/PaimTier'
        status:
          type: string
          enum: [pending, approved, rejected, completed]
          example: pending
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    CrossPaimCommunicationRequest:
      type: object
      required:
        - targetPaimInstanceId
        - message
      properties:
        targetPaimInstanceId:
          type: string
          format: uuid
          description: The ID of the target PAIM instance for communication.
          example: f1e2d3c4-b5a6-9876-5432-10fedcba9876
        message:
          type: string
          description: The content of the message to send.
          example: Please share the latest project updates.
        messageType:
          type: string
          enum: [text, command, data_transfer]
          default: text
          description: Type of message being sent.

    PaimHierarchy:
      type: object
      properties:
        paimInstanceId:
          type: string
          format: uuid
          example: d290f1ee-6c54-4b01-90e6-d701748f0851
        hierarchyTree:
          type: object
          description: Nested structure representing the PAIM hierarchy.
          example:
            id: root-paim-id
            name: Root PAIM
            children:
              - id: child-paim-id-1
                name: Child PAIM 1
                children: []
              - id: child-paim-id-2
                name: Child PAIM 2
                children: []
        lastUpdated:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    PaimHierarchyUpdate:
      type: object
      required:
        - hierarchyTree
      properties:
        hierarchyTree:
          type: object
          description: The new nested structure for the PAIM hierarchy.
          example:
            id: root-paim-id
            name: Root PAIM
            children:
              - id: child-paim-id-1
                name: Child PAIM 1
                children:
                  - id: grandchild-paim-id-1
                    name: Grandchild PAIM 1
                    children: []

    PaginationMetadata:
      type: object
      properties:
        totalElements:
          type: integer
          description: Total number of elements across all pages.
          example: 100
        totalPages:
          type: integer
          description: Total number of pages.
          example: 10
        currentPage:
          type: integer
          description: Current page number (1-based).
          example: 1
        pageSize:
          type: integer
          description: Number of elements per page.
          example: 10

  responses:
    Unauthorized:
      $ref: '../auth.yaml#/components/responses/Unauthorized'
    Forbidden:
      $ref: '../auth.yaml#/components/responses/Forbidden'
    BadRequest:
      $ref: '../auth.yaml#/components/responses/BadRequest'
    NotFound:
      $ref: '../auth.yaml#/components/responses/NotFound'
    InternalServerError:
      $ref: '../auth.yaml#/components/responses/InternalServerError'