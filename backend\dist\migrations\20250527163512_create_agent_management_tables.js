"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.raw(`
    -- Agent Management and Workflow Orchestration Tables

    -- Table: AgentDefinitions
    -- Defines the core properties of an AI agent persona.
    CREATE TABLE AgentDefinitions (
        agent_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        agent_name VARCHAR(255) NOT NULL,
        description TEXT,
        persona_details JSONB, -- Stores details about the agent's persona, e.g., traits, background
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP WITH TIME ZONE, -- For soft deletion
        CONSTRAINT uq_tenant_agent_name UNIQUE (tenant_id, agent_name)
    );

    -- Table: AgentVersions
    -- Manages different versions of an agent definition, allowing for updates and rollbacks.
    CREATE TABLE AgentVersions (
        agent_version_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        agent_id UUID NOT NULL REFERENCES AgentDefinitions(agent_id) ON DELETE CASCADE,
        version_number VARCHAR(50) NOT NULL,
        configuration JSONB, -- Agent's specific configuration for this version (e.g., model, parameters)
        -- embedding VECTOR(1536), -- pgvector for agent embeddings (e.g., OpenAI embeddings) - Commented out due to environment issues
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_agent_version UNIQUE (agent_id, version_number)
    );

    -- Table: Workflows
    -- Defines a sequence of tasks or agents to achieve a specific goal.
    -- Includes cultural requirements for the workflow.

    -- Indexes for performance optimization
    CREATE INDEX idx_agentdefinitions_tenant_id ON AgentDefinitions(tenant_id);
    CREATE INDEX idx_agentdefinitions_name ON AgentDefinitions(agent_name);
    CREATE INDEX idx_agentversions_agent_id ON AgentVersions(agent_id);
  `);
}
async function down(knex) {
    await knex.schema.raw(`
    DROP TABLE IF EXISTS AgentVersions;
    DROP TABLE IF EXISTS AgentDefinitions;
  `);
}
