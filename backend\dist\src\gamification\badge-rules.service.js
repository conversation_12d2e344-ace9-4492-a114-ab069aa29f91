"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BadgeRulesService = void 0;
const common_1 = require("@nestjs/common");
const powerops_types_1 = require("../powerops/powerops.types");
let BadgeRulesService = (() => {
    let _classDecorators = [(0, common_1.Injectable)()];
    let _classDescriptor;
    let _classExtraInitializers = [];
    let _classThis;
    var BadgeRulesService = class {
        static { _classThis = this; }
        static {
            const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
            BadgeRulesService = _classThis = _classDescriptor.value;
            if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
            __runInitializers(_classThis, _classExtraInitializers);
        }
        powerOpsService;
        notificationService;
        constructor(powerOpsService, notificationService) {
            this.powerOpsService = powerOpsService;
            this.notificationService = notificationService;
        }
        async evaluateTimeBasedCriteria(userId, criteria) {
            // Implement time-based criteria evaluation
            // Example: "logged in for 7 consecutive days"
            return false;
        }
        async evaluateSequenceBasedCriteria(userId, criteria) {
            // Implement sequence-based criteria evaluation
            // Example: "completed 3 workflows in a row without errors"
            return false;
        }
        async evaluateXpBasedCriteria(userId, criteria) {
            const xp = await this.powerOpsService.getXp({ userId, roles: [], paimTier: 'standard' }, userId, powerops_types_1.EntityType.User);
            return xp.currentXp >= (criteria.requiredXp || 0);
        }
        async evaluateBadgeBasedCriteria(userId, criteria) {
            const badges = await this.powerOpsService.getBadges({ userId, roles: [], paimTier: 'standard' }, userId, powerops_types_1.EntityType.User);
            return criteria.requiredBadgeIds?.every(requiredId => badges.some(badge => badge.id === requiredId)) ?? true;
        }
        async evaluateBadgeEligibility(userId, badge) {
            // Check if badge has criteria (GamificationBadge)
            const gamificationBadge = badge;
            if (!gamificationBadge.criteria)
                return false;
            const criteria = gamificationBadge.criteria;
            const results = await Promise.all([
                this.evaluateTimeBasedCriteria(userId, criteria),
                this.evaluateSequenceBasedCriteria(userId, criteria),
                this.evaluateXpBasedCriteria(userId, criteria),
                this.evaluateBadgeBasedCriteria(userId, criteria),
            ]);
            return results.every(result => result);
        }
        async checkAndAwardBadges(userId) {
            const allBadges = await this.powerOpsService.getAllBadges({ userId, roles: [], paimTier: 'standard' });
            const awardedBadges = [];
            for (const badge of allBadges) {
                const isEligible = await this.evaluateBadgeEligibility(userId, badge);
                if (isEligible) {
                    await this.powerOpsService.awardBadge({ userId, roles: [], paimTier: 'standard' }, {
                        entityId: userId,
                        entityType: powerops_types_1.EntityType.User,
                        badgeId: badge.id
                    });
                    awardedBadges.push(badge);
                    await this.notificationService.createNotification({
                        userId,
                        type: 'success',
                        message: `New Badge Earned: ${badge.name}!`,
                    });
                }
            }
            return awardedBadges;
        }
    };
    return BadgeRulesService = _classThis;
})();
exports.BadgeRulesService = BadgeRulesService;
