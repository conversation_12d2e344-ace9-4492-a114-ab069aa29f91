"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowCollaborationMiddleware = void 0;
const errors_1 = require("../utils/errors");
class WorkflowCollaborationMiddleware {
    // Example middleware for validating workflow creation requests
    validateCreateWorkflow = (req, res, next) => {
        const { name, ownerId, paimInstanceId, definition } = req.body;
        if (!name || !ownerId || !paimInstanceId || !definition) {
            throw new errors_1.CustomError('Missing required fields for workflow creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        // Add more specific validation logic here (e.g., schema validation for definition)
        next();
    };
    // Example middleware for checking workflow permissions
    checkWorkflowPermission = (requiredPermission) => {
        return (req, res, next) => {
            // In a real application, this would involve checking the authenticated user's roles and permissions
            // against the workflow's ownerId, sharedWithId, and permissionLevel.
            // For now, this is a placeholder.
            const hasPermission = true; // Placeholder for actual permission check
            if (!hasPermission) {
                throw new errors_1.CustomError('User does not have sufficient permissions for this workflow', { originalErrorCode: 'AuthorizationError', originalStatusCode: 403 });
            }
            next();
        };
    };
    // Middleware for validating task creation requests
    validateCreateTask = (req, res, next) => {
        const { title, status, priority } = req.body;
        if (!title || !status || !priority) {
            throw new errors_1.CustomError('Missing required fields for task creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        next();
    };
    // Middleware for validating collaboration session creation requests
    validateStartCollaborationSession = (req, res, next) => {
        const { name, ownerId, paimInstanceId } = req.body;
        if (!name || !ownerId || !paimInstanceId) {
            throw new errors_1.CustomError('Missing required fields for collaboration session creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        next();
    };
    // Middleware for validating cross-tenant message requests
    validateCrossTenantMessage = (req, res, next) => {
        const { senderPaimInstanceId, recipientPaimInstanceId, messageContent } = req.body;
        if (!senderPaimInstanceId || !recipientPaimInstanceId || !messageContent) {
            throw new errors_1.CustomError('Missing required fields for cross-tenant message', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        next();
    };
    // Middleware for validating workflow sharing requests
    validateShareWorkflow = (req, res, next) => {
        const { workflowId, sharedWithId, sharedWithEntityType, permissionLevel } = req.body;
        if (!workflowId || !sharedWithId || !sharedWithEntityType || !permissionLevel) {
            throw new errors_1.CustomError('Missing required fields for workflow sharing', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        next();
    };
    // Middleware for validating task delegation requests
    validateDelegateTask = (req, res, next) => {
        const { delegateToId, delegateToEntityType } = req.body;
        if (!delegateToId || !delegateToEntityType) {
            throw new errors_1.CustomError('Missing required fields for task delegation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        next();
    };
    // Middleware for validating workspace creation requests
    validateCreateWorkspace = (req, res, next) => {
        const { name, ownerId, paimInstanceId } = req.body;
        if (!name || !ownerId || !paimInstanceId) {
            throw new errors_1.CustomError('Missing required fields for workspace creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        next();
    };
    // Middleware for validating team creation requests
    validateCreateTeam = (req, res, next) => {
        const { name, paimInstanceId } = req.body;
        if (!name || !paimInstanceId) {
            throw new errors_1.CustomError('Missing required fields for team creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
        }
        next();
    };
}
exports.WorkflowCollaborationMiddleware = WorkflowCollaborationMiddleware;
