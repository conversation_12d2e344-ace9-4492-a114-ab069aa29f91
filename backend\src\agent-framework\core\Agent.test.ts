import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Agent, AgentTask } from './Agent';
import { Config } from './Config';

describe('Agent', () => {
  let agent: Agent;
  let config: Config;

  beforeEach(() => {
    config = new Config();
    agent = new Agent('test-agent-1', 'TestAgent', 'test', config);
  });

  afterEach(() => {
    Agent.unregisterAgent('TestAgent');
  });

  describe('Constructor', () => {
    it('should create an agent with correct properties', () => {
      expect(agent.id).toBe('test-agent-1');
      expect(agent.name).toBe('TestAgent');
      expect(agent.type).toBe('test');
      expect(agent.getIsActive()).toBe(false);
    });

    it('should initialize with default metrics', () => {
      const metrics = agent.getMetrics();
      expect(metrics.tasksExecuted).toBe(0);
      expect(metrics.successRate).toBe(100);
      expect(metrics.averageExecutionTime).toBe(0);
    });
  });

  describe('Agent Registry', () => {
    it('should register an agent', () => {
      Agent.registerAgent('TestAgent', agent);
      const retrievedAgent = Agent.getAgent('TestAgent');
      expect(retrievedAgent).toBe(agent);
    });

    it('should throw error when registering duplicate agent', () => {
      Agent.registerAgent('TestAgent', agent);
      expect(() => {
        Agent.registerAgent('TestAgent', agent);
      }).toThrow('Agent with name \'TestAgent\' is already registered');
    });

    it('should unregister an agent', () => {
      Agent.registerAgent('TestAgent', agent);
      const unregistered = Agent.unregisterAgent('TestAgent');
      expect(unregistered).toBe(true);
      expect(Agent.getAgent('TestAgent')).toBeUndefined();
    });

    it('should return false when unregistering non-existent agent', () => {
      const unregistered = Agent.unregisterAgent('NonExistent');
      expect(unregistered).toBe(false);
    });

    it('should get all registered agents', () => {
      Agent.registerAgent('TestAgent', agent);
      const allAgents = Agent.getAllAgents();
      expect(allAgents.has('TestAgent')).toBe(true);
      expect(allAgents.get('TestAgent')).toBe(agent);
    });
  });

  describe('Agent Lifecycle', () => {
    it('should start an agent', async () => {
      await agent.start();
      expect(agent.getIsActive()).toBe(true);
    });

    it('should stop an agent', async () => {
      await agent.start();
      await agent.stop();
      expect(agent.getIsActive()).toBe(false);
    });

    it('should not start an already active agent', async () => {
      await agent.start();
      // Should not throw, just log warning
      await agent.start();
      expect(agent.getIsActive()).toBe(true);
    });

    it('should not stop an already inactive agent', async () => {
      // Should not throw, just log warning
      await agent.stop();
      expect(agent.getIsActive()).toBe(false);
    });
  });

  describe('Task Execution', () => {
    beforeEach(async () => {
      await agent.start();
    });

    it('should execute a simple string task', async () => {
      const result = await agent.execute('test task');
      
      expect(result.success).toBe(true);
      expect(result.agentId).toBe(agent.id);
      expect(result.data).toBeDefined();
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it('should execute a task object', async () => {
      const task: AgentTask = {
        id: 'test-task-1',
        type: 'test',
        payload: { message: 'test payload' },
        timestamp: new Date()
      };

      const result = await agent.execute(task);
      
      expect(result.success).toBe(true);
      expect(result.taskId).toBe(task.id);
      expect(result.data.payload).toEqual(task.payload);
    });

    it('should fail when agent is not active', async () => {
      await agent.stop();
      
      const result = await agent.execute('test task');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('is not active');
    });

    it('should update metrics after task execution', async () => {
      await agent.execute('test task');
      
      const metrics = agent.getMetrics();
      expect(metrics.tasksExecuted).toBe(1);
      expect(metrics.averageExecutionTime).toBeGreaterThan(0);
      expect(metrics.lastActivity).toBeDefined();
    });

    it('should emit events during task execution', async () => {
      const startSpy = vi.fn();
      const completeSpy = vi.fn();
      
      agent.on('task:start', startSpy);
      agent.on('task:complete', completeSpy);
      
      await agent.execute('test task');
      
      expect(startSpy).toHaveBeenCalledOnce();
      expect(completeSpy).toHaveBeenCalledOnce();
    });
  });

  describe('Capabilities', () => {
    it('should add a capability', () => {
      const capability = {
        name: 'test-capability',
        version: '1.0.0',
        description: 'Test capability',
        enabled: true
      };

      agent.addCapability(capability);
      const capabilities = agent.getCapabilities();
      
      expect(capabilities).toHaveLength(1);
      expect(capabilities[0]).toEqual(capability);
    });

    it('should remove a capability', () => {
      const capability = {
        name: 'test-capability',
        version: '1.0.0',
        description: 'Test capability',
        enabled: true
      };

      agent.addCapability(capability);
      const removed = agent.removeCapability('test-capability');
      
      expect(removed).toBe(true);
      expect(agent.getCapabilities()).toHaveLength(0);
    });

    it('should return false when removing non-existent capability', () => {
      const removed = agent.removeCapability('non-existent');
      expect(removed).toBe(false);
    });

    it('should emit events when adding/removing capabilities', () => {
      const addSpy = vi.fn();
      const removeSpy = vi.fn();
      
      agent.on('capability:added', addSpy);
      agent.on('capability:removed', removeSpy);
      
      const capability = {
        name: 'test-capability',
        version: '1.0.0',
        description: 'Test capability',
        enabled: true
      };

      agent.addCapability(capability);
      agent.removeCapability('test-capability');
      
      expect(addSpy).toHaveBeenCalledOnce();
      expect(removeSpy).toHaveBeenCalledOnce();
    });
  });

  describe('Configuration', () => {
    it('should return agent configuration', () => {
      const agentConfig = agent.getConfig();
      expect(agentConfig).toBe(config);
    });

    it('should use provided configuration', () => {
      const customConfig = new Config({ test: 'value' });
      const customAgent = new Agent('test-2', 'TestAgent2', 'test', customConfig);
      
      expect(customAgent.getConfig().get('test')).toBe('value');
    });
  });

  describe('Metrics', () => {
    beforeEach(async () => {
      await agent.start();
    });

    it('should track successful executions', async () => {
      await agent.execute('task1');
      await agent.execute('task2');
      
      const metrics = agent.getMetrics();
      expect(metrics.tasksExecuted).toBe(2);
      expect(metrics.successRate).toBe(100);
    });

    it('should track failed executions', async () => {
      // Create a custom agent that will fail
      class FailingAgent extends Agent {
        protected async executeTask(): Promise<any> {
          throw new Error('Intentional failure');
        }
      }

      const failingAgent = new FailingAgent('failing-agent', 'FailingAgent', 'test');
      await failingAgent.start();

      await failingAgent.execute('task1');
      await failingAgent.execute('task2');
      
      const metrics = failingAgent.getMetrics();
      expect(metrics.tasksExecuted).toBe(2);
      expect(metrics.successRate).toBe(0);
    });

    it('should calculate average execution time', async () => {
      await agent.execute('task1');
      await agent.execute('task2');
      
      const metrics = agent.getMetrics();
      expect(metrics.averageExecutionTime).toBeGreaterThan(0);
    });
  });
});
