openapi: 3.0.0
info:
  title: The AIgency & PAIM Billing API
  version: 1.0.0
  description: API endpoints for tracking PowerOps usage and managing billing.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Billing
    description: PowerOps usage tracking and invoice management

paths:
  /billing/usage:
    post:
      summary: Track PowerOps usage for billing
      operationId: trackPowerOpsUsage
      tags:
        - Billing
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PowerOpsUsageRequest'
      responses:
        '200':
          description: PowerOps usage tracked successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PowerOpsUsageResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    PowerOpsUsageRequest:
      type: object
      required:
        - org_id
        - powerops_used
      properties:
        org_id:
          type: string
          format: uuid
          description: Unique identifier of the organization.
          example: d290f1ee-6c54-4b01-90e6-d701748f0851
        powerops_used:
          type: number
          format: float
          minimum: 0
          description: Number of PowerOps used.
          example: 100.50

    PowerOpsUsageResponse:
      type: object
      properties:
        message:
          type: string
          example: PowerOps usage tracked successfully
        record:
          type: object
          properties:
            org_id:
              type: string
              format: uuid
              example: d290f1ee-6c54-4b01-90e6-d701748f0851
            month:
              type: string
              format: date
              example: '2025-05-01'
            powerops_used:
              type: number
              format: float
              example: 100.50
            cost_usd:
              type: number
              format: float
              example: 8.04
            created_at:
              type: string
              format: date-time
              example: '2025-05-28T10:00:00Z'
            updated_at:
              type: string
              format: date-time
              example: '2025-05-28T10:00:00Z'

  responses:
    BadRequest:
      description: Bad Request - Invalid input or missing required fields.
      content:
        application/json:
          schema:
            $ref: './auth.yaml#/components/schemas/ErrorResponse' # Reference to common ErrorResponse
    Unauthorized:
      description: Unauthorized - Authentication required or invalid credentials.
      content:
        application/json:
          schema:
            $ref: './auth.yaml#/components/schemas/ErrorResponse'
    InternalServerError:
      description: Internal Server Error - Something went wrong on the server.
      content:
        application/json:
          schema:
            $ref: './auth.yaml#/components/schemas/ErrorResponse'