openapi: 3.0.0
info:
  title: The AIgency & PAIM Authentication and Authorization API
  version: 1.0.0
  description: API endpoints for user authentication, authorization, PAIM tier management, and role-based access control.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: User login and token management
  - name: Authorization
    description: Role and permission management
  - name: PAIM Tier Management
    description: Endpoints for managing PAIM tiers and transitions

paths:
  /auth/login:
    post:
      summary: Authenticate user and obtain JWT token
      operationId: loginUser
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthToken'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/register:
    post:
      summary: Register a new user
      operationId: registerUser
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
      responses:
        '201':
          description: User successfully registered
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: Conflict, user with email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/refresh-token:
    post:
      summary: Refresh an expired JWT token
      operationId: refreshToken
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refreshToken:
                  type: string
                  description: The refresh token obtained during login.
              required:
                - refreshToken
      responses:
        '200':
          description: Token successfully refreshed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthToken'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /users/{userId}/paim-tier:
    put:
      summary: Update a user's PAIM tier
      operationId: updatePaimTier
      tags:
        - PAIM Tier Management
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: userId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the user whose PAIM tier is to be updated.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaimTierUpdateRequest'
      responses:
        '200':
          description: PAIM tier updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /roles:
    get:
      summary: Get all available roles
      operationId: getAllRoles
      tags:
        - Authorization
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of roles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /users/{userId}/roles:
    put:
      summary: Assign roles to a user
      operationId: assignUserRoles
      tags:
        - Authorization
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: userId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the user to assign roles to.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roleIds:
                  type: array
                  items:
                    type: string
                    format: uuid
                  description: Array of role IDs to assign to the user.
              required:
                - roleIds
      responses:
        '200':
          description: Roles assigned successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        password:
          type: string
          format: password
          example: securepassword123

    AuthToken:
      type: object
      properties:
        accessToken:
          type: string
          description: JWT access token
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        refreshToken:
          type: string
          description: JWT refresh token
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        expiresIn:
          type: integer
          description: Access token expiration time in seconds
          example: 3600

    UserRegistrationRequest:
      type: object
      required:
        - email
        - password
        - firstName
        - lastName
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        password:
          type: string
          format: password
          example: newsecurepassword123
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Doe
        companyName:
          type: string
          nullable: true
          description: Optional company name for multi-tenant setup.

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: d290f1ee-6c54-4b01-90e6-d701748f0851
        email:
          type: string
          format: email
          example: <EMAIL>
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Doe
        companyId:
          type: string
          format: uuid
          nullable: true
          description: ID of the company if part of a multi-tenant setup.
        paimTier:
          $ref: '#/components/schemas/PaimTier'
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    PaimTierUpdateRequest:
      type: object
      required:
        - newTier
      properties:
        newTier:
          $ref: '#/components/schemas/PaimTier'
        reason:
          type: string
          nullable: true
          description: Optional reason for the tier change.

    PaimTier:
      type: string
      enum:
        - SYSTEM_ADMIN
        - COMPANY_ADMIN
        - POWER_USER
        - PERSONAL
      description: Defines the PAIM tier level for a user or instance.

    Role:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        name:
          type: string
          example: admin
        description:
          type: string
          example: Administrator role with full access.

    ErrorResponse:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        status:
          type: integer
          example: 400
        error:
          type: string
          example: Bad Request
        message:
          type: string
          example: Invalid input provided.
        path:
          type: string
          example: /api/v1/auth/login
        details:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                example: email
              code:
                type: string
                example: invalid_format
              message:
                type: string
                example: Invalid email format.

  responses:
    BadRequest:
      description: Bad Request - Invalid input or missing required fields.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized - Authentication required or invalid credentials.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden - User does not have necessary permissions.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: Not Found - The requested resource could not be found.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalServerError:
      description: Internal Server Error - Something went wrong on the server.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'