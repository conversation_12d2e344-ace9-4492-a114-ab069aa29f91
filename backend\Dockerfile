# Multi-stage build for better reliability and smaller final image
FROM node:20-alpine AS builder

# Set the working directory
WORKDIR /app/backend

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies with retry logic and better network handling
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    npm config set fetch-retries 10 && \
    npm config set fetch-timeout 600000 && \
    npm install --prefer-offline --no-audit --progress=false --legacy-peer-deps

# Copy source code and other necessary files
COPY src ./src
COPY tsconfig.json ./tsconfig.json
COPY tsconfig.build.json ./tsconfig.build.json
COPY tsconfig.staging.json ./tsconfig.staging.json
COPY knexfile.js ./knexfile.js
COPY migrations ./migrations
COPY seeds ./seeds

# Build the TypeScript code
RUN npm run build:staging

# Production stage
FROM node:20-alpine AS production

# Set the working directory
WORKDIR /app/backend

# Copy necessary files from builder stage
COPY --from=builder /app/backend/node_modules ./node_modules
COPY --from=builder /app/backend/package.json ./package.json
COPY --from=builder /app/backend/package-lock.json ./package-lock.json
COPY --from=builder /app/backend/knexfile.js ./knexfile.js
COPY --from=builder /app/backend/migrations ./migrations
COPY --from=builder /app/backend/seeds ./seeds
COPY --from=builder /app/backend/dist ./dist

# Expose the port
EXPOSE 3001

# Run the application for staging
CMD ["npm", "run", "start:staging"]