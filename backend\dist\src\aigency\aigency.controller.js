"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const aigency_service_1 = require("./aigency.service");
const aigency_repository_1 = require("./aigency.repository");
const audit_service_1 = require("../audit/audit.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const errors_1 = require("../utils/errors");
const joi_1 = __importDefault(require("joi"));
const validation_1 = require("../utils/validation");
const type_guards_1 = require("../utils/type-guards");
// Extend the Request interface to include custom properties (from express.d.ts)
// This is already handled by backend/src/types/express.d.ts
const aigencyRouter = (0, express_1.Router)();
const auditTrailService = new audit_service_1.AuditTrailService();
const aigencyRepository = new aigency_repository_1.AigencyRepository(auditTrailService);
const aigencyService = new aigency_service_1.AigencyService(aigencyRepository);
// Joi Schemas for validation
const createKnowledgeBaseSchema = joi_1.default.object({
    name: joi_1.default.string().required(),
    description: joi_1.default.string().optional().allow(''),
});
const updateKnowledgeBaseSchema = joi_1.default.object({
    name: joi_1.default.string().optional(),
    description: joi_1.default.string().optional().allow(''),
});
const documentUploadSchema = joi_1.default.object({
    knowledgeBaseId: joi_1.default.string().uuid().required(),
    fileName: joi_1.default.string().required(),
    fileType: joi_1.default.string().required(),
    content: joi_1.default.string().required(), // Base64 encoded or raw text
});
const vectorSearchSchema = joi_1.default.object({
    knowledgeBaseId: joi_1.default.string().uuid().required(),
    query: joi_1.default.string().required(),
    topK: joi_1.default.number().integer().min(1).max(100).default(5),
});
const aiChatSchema = joi_1.default.object({
    model: joi_1.default.string().required(),
    messages: joi_1.default.array().items(joi_1.default.object({
        role: joi_1.default.string().valid('user', 'assistant', 'system').required(),
        content: joi_1.default.string().required(),
    })).min(1).required(),
    temperature: joi_1.default.number().min(0).max(1).optional(),
    maxTokens: joi_1.default.number().integer().min(1).optional(),
    stream: joi_1.default.boolean().optional(),
});
const embeddingRequestSchema = joi_1.default.object({
    input: joi_1.default.alternatives().try(joi_1.default.string(), joi_1.default.array().items(joi_1.default.string())).required(),
    model: joi_1.default.string().required(),
});
// Middleware to extract tenantId and userId from request (assuming JWT or similar auth)
const authenticateAndAuthorize = (req, res, next) => {
    // This is a placeholder. In a real app, this would parse JWT, validate user, and set req.tenantId, req.userId
    req.tenantId = req.headers['x-tenant-id'] || 'default-tenant-id'; // Example: get from header
    req.userId = req.user?.userId || 'default-user-id'; // Use req.user?.userId as per JwtPayload
    if (!req.tenantId || !req.userId) {
        res.status(401).json({ message: 'Unauthorized: Tenant ID or User ID missing.' });
        return;
    }
    next();
};
aigencyRouter.use(authenticateAndAuthorize);
// Knowledge Base CRUD Operations
aigencyRouter.post('/knowledge-bases', (0, validation_1.validate)(createKnowledgeBaseSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { name, description } = req.body;
    const knowledgeBase = await aigencyRepository.createKnowledgeBase((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), name, description);
    res.status(201).json(knowledgeBase);
}));
aigencyRouter.get('/knowledge-bases', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const knowledgeBases = await aigencyRepository.listKnowledgeBases((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'));
    res.status(200).json(knowledgeBases);
}));
aigencyRouter.get('/knowledge-bases/:id', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const knowledgeBase = await aigencyRepository.getKnowledgeBaseById((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(id, 'id'));
    if (!knowledgeBase) {
        throw new errors_1.CustomError('Knowledge Base not found.', { originalErrorCode: 'NOT_FOUND', originalStatusCode: 404 });
    }
    res.status(200).json(knowledgeBase);
}));
aigencyRouter.put('/knowledge-bases/:id', (0, validation_1.validate)(updateKnowledgeBaseSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { name, description } = req.body;
    const updatedKnowledgeBase = await aigencyRepository.updateKnowledgeBase((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(id, 'id'), { name, description });
    if (!updatedKnowledgeBase) {
        throw new errors_1.CustomError('Knowledge Base not found.', { originalErrorCode: 'NOT_FOUND', originalStatusCode: 404 });
    }
    res.status(200).json(updatedKnowledgeBase);
}));
aigencyRouter.delete('/knowledge-bases/:id', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const deleted = await aigencyRepository.deleteKnowledgeBase((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(id, 'id'));
    if (!deleted) {
        throw new errors_1.CustomError('Knowledge Base not found.', { originalErrorCode: 'NOT_FOUND', originalStatusCode: 404 });
    }
    res.status(204).send();
}));
// Document Upload and Processing
aigencyRouter.post('/documents/upload', (0, validation_1.validate)(documentUploadSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { knowledgeBaseId, fileName, fileType, content } = req.body;
    // In a real scenario, 'content' would be processed:
    // 1. Calculate content hash to check for duplicates
    // 2. Chunk the document
    // 3. Generate embeddings for each chunk using an AI service (e.g., aigencyService.generateEmbedding)
    // 4. Store document and chunks in the repository
    // For now, a simplified mock process:
    const contentHash = 'mock-hash-' + Date.now(); // Replace with actual hash
    const chunkCount = 1; // Simplified
    const document = await aigencyRepository.createDocument(knowledgeBaseId, (0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), fileName, fileType, contentHash, chunkCount);
    // Mock embedding generation and chunk storage
    const mockEmbedding = Array(1536).fill(0.01); // Example embedding
    await aigencyRepository.createDocumentChunk(document.id, document.knowledgeBaseId, (0, type_guards_1.requireParam)(document.tenantId, 'document.tenantId'), content, // Store raw content for simplicity, in real app store processed chunk
    mockEmbedding, 0);
    res.status(201).json({ message: 'Document uploaded and processed (mock).', documentId: document.id });
}));
// Vector Search and Similarity Queries
aigencyRouter.post('/vector-search', (0, validation_1.validate)(vectorSearchSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { knowledgeBaseId, query, topK } = req.body;
    // Generate embedding for the query using an AI service
    const queryEmbeddingResponse = await aigencyService.generateEmbedding((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), { input: query, model: 'openai-text-embedding-ada-002' } // Use a specific embedding model
    );
    if (!queryEmbeddingResponse.data || queryEmbeddingResponse.data.length === 0) {
        throw new errors_1.CustomError('Failed to generate embedding for query.', { originalErrorCode: 'EMBEDDING_FAILED', originalStatusCode: 500 });
    }
    const queryEmbedding = queryEmbeddingResponse.data?.[0]?.embedding;
    if (!queryEmbedding) {
        throw new errors_1.CustomError('Failed to extract embedding from response.', { originalErrorCode: 'EMBEDDING_PROCESSING_FAILED', originalStatusCode: 500 });
    }
    const searchResults = await aigencyRepository.vectorSearch((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), knowledgeBaseId, queryEmbedding, topK);
    res.status(200).json(searchResults);
}));
// AI Chat Endpoint
aigencyRouter.post('/ai-chat', (0, validation_1.validate)(aiChatSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const chatRequest = req.body;
    const chatResponse = await aigencyService.chat((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), chatRequest);
    res.status(200).json(chatResponse);
}));
// AI Embedding Endpoint
aigencyRouter.post('/ai-embeddings', (0, validation_1.validate)(embeddingRequestSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const embeddingRequest = req.body;
    const embeddingResponse = await aigencyService.generateEmbedding((0, type_guards_1.requireParam)(req.tenantId, 'tenantId'), (0, type_guards_1.requireParam)(req.userId, 'userId'), embeddingRequest);
    res.status(200).json(embeddingResponse);
}));
exports.default = aigencyRouter;
