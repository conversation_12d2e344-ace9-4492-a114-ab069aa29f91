"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    return knex.schema.alterTable('tenants', (table) => {
        table.boolean('founder_club_flag').defaultTo(false);
        table.timestamp('promo_start_date', { useTz: true }).nullable();
        table.timestamp('promo_end_date', { useTz: true }).nullable();
    });
}
async function down(knex) {
    return knex.schema.alterTable('tenants', (table) => {
        table.dropColumn('founder_club_flag');
        table.dropColumn('promo_start_date');
        table.dropColumn('promo_end_date');
    });
}
