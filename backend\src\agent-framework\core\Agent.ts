import { EventEmitter } from './EventEmitter';
import { Config } from './Config';
import { Plugin } from './Plugin';
import logger from '../../config/logger';

export interface AgentTask {
  id: string;
  type: string;
  payload: any;
  timestamp: Date;
  priority?: number;
}

export interface AgentExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
  agentId: string;
  taskId: string;
}

export interface AgentCapability {
  name: string;
  version: string;
  description: string;
  enabled: boolean;
}

export interface AgentMetrics {
  tasksExecuted: number;
  successRate: number;
  averageExecutionTime: number;
  lastActivity: Date;
}

export class Agent extends EventEmitter {
  private static registry: Map<string, Agent> = new Map();
  
  public readonly id: string;
  public readonly name: string;
  public readonly type: string;
  private config: Config;
  private plugins: Map<string, Plugin> = new Map();
  private capabilities: Map<string, AgentCapability> = new Map();
  private metrics: AgentMetrics;
  private isActive: boolean = false;

  constructor(
    id: string,
    name: string,
    type: string,
    config?: Config
  ) {
    super();
    this.id = id;
    this.name = name;
    this.type = type;
    this.config = config || new Config();
    this.metrics = {
      tasksExecuted: 0,
      successRate: 100,
      averageExecutionTime: 0,
      lastActivity: new Date()
    };

    logger.info(`Agent ${this.name} (${this.id}) initialized`);
  }

  /**
   * Execute a task with the agent
   */
  public async execute(task: string | AgentTask): Promise<AgentExecutionResult> {
    const startTime = Date.now();
    const taskObj: AgentTask = typeof task === 'string' 
      ? {
          id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'generic',
          payload: task,
          timestamp: new Date()
        }
      : task;

    logger.info(`Agent ${this.name} executing task: ${taskObj.id}`);

    try {
      // Emit task start event
      this.emit('task:start', { agent: this, task: taskObj });

      // Check if agent is active
      if (!this.isActive) {
        throw new Error(`Agent ${this.name} is not active`);
      }

      // Execute the task (this will be overridden by specialized agents)
      const result = await this.executeTask(taskObj);

      const executionTime = Date.now() - startTime;
      
      // Update metrics
      this.updateMetrics(true, executionTime);

      const executionResult: AgentExecutionResult = {
        success: true,
        data: result,
        executionTime,
        agentId: this.id,
        taskId: taskObj.id
      };

      // Emit task completion event
      this.emit('task:complete', { agent: this, task: taskObj, result: executionResult });

      logger.info(`Agent ${this.name} completed task ${taskObj.id} in ${executionTime}ms`);
      return executionResult;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateMetrics(false, executionTime);

      const executionResult: AgentExecutionResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime,
        agentId: this.id,
        taskId: taskObj.id
      };

      // Emit task error event
      this.emit('task:error', { agent: this, task: taskObj, error, result: executionResult });

      logger.error(`Agent ${this.name} failed to execute task ${taskObj.id}: ${error}`);
      return executionResult;
    }
  }

  /**
   * Override this method in specialized agents
   */
  protected async executeTask(task: AgentTask): Promise<any> {
    // Default implementation - just return the payload
    return {
      message: `Task ${task.id} executed by ${this.name}`,
      payload: task.payload,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get the agent's configuration
   */
  public getConfig(): Config {
    return this.config;
  }

  /**
   * Register an agent in the global registry
   */
  public static registerAgent(name: string, agent: Agent): void {
    if (Agent.registry.has(name)) {
      throw new Error(`Agent with name '${name}' is already registered`);
    }
    
    Agent.registry.set(name, agent);
    logger.info(`Agent '${name}' registered in global registry`);
  }

  /**
   * Get an agent from the global registry
   */
  public static getAgent(name: string): Agent | undefined {
    return Agent.registry.get(name);
  }

  /**
   * Get all registered agents
   */
  public static getAllAgents(): Map<string, Agent> {
    return new Map(Agent.registry);
  }

  /**
   * Unregister an agent
   */
  public static unregisterAgent(name: string): boolean {
    const removed = Agent.registry.delete(name);
    if (removed) {
      logger.info(`Agent '${name}' unregistered from global registry`);
    }
    return removed;
  }

  /**
   * Start the agent
   */
  public async start(): Promise<void> {
    if (this.isActive) {
      logger.warn(`Agent ${this.name} is already active`);
      return;
    }

    this.isActive = true;
    this.emit('agent:start', { agent: this });
    logger.info(`Agent ${this.name} started`);
  }

  /**
   * Stop the agent
   */
  public async stop(): Promise<void> {
    if (!this.isActive) {
      logger.warn(`Agent ${this.name} is already inactive`);
      return;
    }

    this.isActive = false;
    this.emit('agent:stop', { agent: this });
    logger.info(`Agent ${this.name} stopped`);
  }

  /**
   * Install a plugin
   */
  public async installPlugin(plugin: Plugin): Promise<void> {
    if (this.plugins.has(plugin.name)) {
      throw new Error(`Plugin '${plugin.name}' is already installed`);
    }

    await plugin.install(this);
    this.plugins.set(plugin.name, plugin);
    this.emit('plugin:installed', { agent: this, plugin });
    logger.info(`Plugin '${plugin.name}' installed on agent ${this.name}`);
  }

  /**
   * Uninstall a plugin
   */
  public async uninstallPlugin(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new Error(`Plugin '${pluginName}' is not installed`);
    }

    await plugin.uninstall(this);
    this.plugins.delete(pluginName);
    this.emit('plugin:uninstalled', { agent: this, plugin });
    logger.info(`Plugin '${pluginName}' uninstalled from agent ${this.name}`);
  }

  /**
   * Add a capability to the agent
   */
  public addCapability(capability: AgentCapability): void {
    this.capabilities.set(capability.name, capability);
    this.emit('capability:added', { agent: this, capability });
    logger.info(`Capability '${capability.name}' added to agent ${this.name}`);
  }

  /**
   * Remove a capability from the agent
   */
  public removeCapability(capabilityName: string): boolean {
    const removed = this.capabilities.delete(capabilityName);
    if (removed) {
      this.emit('capability:removed', { agent: this, capabilityName });
      logger.info(`Capability '${capabilityName}' removed from agent ${this.name}`);
    }
    return removed;
  }

  /**
   * Get agent capabilities
   */
  public getCapabilities(): AgentCapability[] {
    return Array.from(this.capabilities.values());
  }

  /**
   * Get agent metrics
   */
  public getMetrics(): AgentMetrics {
    return { ...this.metrics };
  }



  /**
   * Check if agent is active
   */
  public getIsActive(): boolean {
    return this.isActive;
  }

  /**
   * Update agent metrics
   */
  private updateMetrics(success: boolean, executionTime: number): void {
    this.metrics.tasksExecuted++;
    this.metrics.lastActivity = new Date();
    
    // Update average execution time
    const totalTime = this.metrics.averageExecutionTime * (this.metrics.tasksExecuted - 1) + executionTime;
    this.metrics.averageExecutionTime = totalTime / this.metrics.tasksExecuted;
    
    // Update success rate
    const successfulTasks = Math.round(this.metrics.successRate * (this.metrics.tasksExecuted - 1) / 100);
    const newSuccessfulTasks = success ? successfulTasks + 1 : successfulTasks;
    this.metrics.successRate = (newSuccessfulTasks / this.metrics.tasksExecuted) * 100;
  }
}
