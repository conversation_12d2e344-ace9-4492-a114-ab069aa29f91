openapi: 3.0.0
info:
  title: The AIgency & PowerOps Gamification API
  version: 1.0.0
  description: API endpoints for PowerOps usage tracking, gamification elements (XP, badges, achievements), and cost management.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: PowerOps Usage
    description: Tracking and managing PowerOps consumption and billing
  - name: Gamification
    description: Managing XP, badges, achievements, and streaks
  - name: Cost Management
    description: Monitoring and budgeting for PowerOps usage

paths:
  /powerops/usage:
    get:
      summary: Get PowerOps usage data for a user or PAIM instance
      operationId: getPowerOpsUsage
      tags:
        - PowerOps Usage
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: ID of the user or PAIM instance to retrieve usage for.
        - in: query
          name: entityType
          schema:
            type: string
            enum: [user, paim_instance]
          description: Type of entity (user or paim_instance).
        - in: query
          name: startDate
          schema:
            type: string
            format: date
          description: Start date for usage data (YYYY-MM-DD).
        - in: query
          name: endDate
          schema:
            type: string
            format: date
          description: End date for usage data (YYYY-MM-DD).
      responses:
        '200':
          description: PowerOps usage data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PowerOpsUsage'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Log PowerOps usage
      operationId: logPowerOpsUsage
      tags:
        - PowerOps Usage
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LogPowerOpsUsageRequest'
      responses:
        '201':
          description: PowerOps usage logged successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PowerOpsUsageEntry'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /gamification/xp/add:
    post:
      summary: Award XP to a user or PAIM instance
      operationId: awardXp
      tags:
        - Gamification
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AwardXpRequest'
      responses:
        '200':
          description: XP awarded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Xp'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /gamification/xp/user/{id}:
    get:
      summary: Get XP summary for a specific user
      operationId: getXpByUser
      tags:
        - Gamification
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the user to retrieve XP for.
      responses:
        '200':
          description: User XP details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Xp'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /gamification/xp/org/{id}:
    get:
      summary: Get XP leaderboard for a specific organization
      operationId: getXpByOrg
      tags:
        - Gamification
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
            format: uuid
          required: true
          description: ID of the organization to retrieve XP leaderboard for.
      responses:
        '200':
          description: Organization XP leaderboard
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Xp' # Assuming Xp can represent an entry in a leaderboard
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /gamification/badges:
    get:
      summary: Get badges for a user or PAIM instance
      operationId: getBadges
      tags:
        - Gamification
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: ID of the user or PAIM instance to retrieve badges for.
        - in: query
          name: entityType
          schema:
            type: string
            enum: [user, paim_instance]
          description: Type of entity (user or paim_instance).
      responses:
        '200':
          description: List of badges
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Badge'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Award a badge to a user or PAIM instance
      operationId: awardBadge
      tags:
        - Gamification
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AwardBadgeRequest'
      responses:
        '200':
          description: Badge awarded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Badge'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /gamification/achievements:
    get:
      summary: Get achievements for a user or PAIM instance
      operationId: getAchievements
      tags:
        - Gamification
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: ID of the user or PAIM instance to retrieve achievements for.
        - in: query
          name: entityType
          schema:
            type: string
            enum: [user, paim_instance]
          description: Type of entity (user or paim_instance).
      responses:
        '200':
          description: List of achievements
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Achievement'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Grant an achievement to a user or PAIM instance
      operationId: grantAchievement
      tags:
        - Gamification
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GrantAchievementRequest'
      responses:
        '200':
          description: Achievement granted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Achievement'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /gamification/streaks:
    get:
      summary: Get streaks for a user or PAIM instance
      operationId: getStreaks
      tags:
        - Gamification
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: ID of the user or PAIM instance to retrieve streaks for.
        - in: query
          name: entityType
          schema:
            type: string
            enum: [user, paim_instance]
          description: Type of entity (user or paim_instance).
      responses:
        '200':
          description: List of streaks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Streak'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /cost-management/budgets:
    get:
      summary: Get budgets for a user or PAIM instance
      operationId: getBudgets
      tags:
        - Cost Management
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: ID of the user or PAIM instance to retrieve budgets for.
        - in: query
          name: entityType
          schema:
            type: string
            enum: [user, paim_instance]
          description: Type of entity (user or paim_instance).
      responses:
        '200':
          description: List of budgets
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Budget'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Create a new budget
      operationId: createBudget
      tags:
        - Cost Management
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBudgetRequest'
      responses:
        '201':
          description: Budget created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Budget'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /cost-management/budgets/{budgetId}:
    put:
      summary: Update an existing budget
      operationId: updateBudget
      tags:
        - Cost Management
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: budgetId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the budget to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBudgetRequest'
      responses:
        '200':
          description: Budget updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Budget'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    delete:
      summary: Delete a budget
      operationId: deleteBudget
      tags:
        - Cost Management
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: budgetId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the budget to delete.
      responses:
        '204':
          description: Budget deleted successfully
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    PowerOpsUsage:
      type: object
      properties:
        entityId:
          type: string
          format: uuid
          description: ID of the user or PAIM instance.
        entityType:
          type: string
          enum: [user, paim_instance]
        totalUsageUnits:
          type: number
          format: float
          example: 1250.75
          description: Total PowerOps units consumed.
        estimatedCost:
          type: number
          format: float
          example: 125.08
          description: Estimated cost based on usage.

    AwardXpRequest:
      type: object
      required:
        - org_id
        - powerops
        - reason
      properties:
        org_id:
          type: string
          format: uuid
          description: Unique identifier of the organization.
          example: d290f1ee-6c54-4b01-90e6-d701748f0851
        agent_id:
          type: string
          format: uuid
          nullable: true
          description: Optional unique identifier of the AI agent.
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        powerops:
          type: number
          format: float
          minimum: 0
          description: Number of PowerOps consumed, used to calculate XP.
          example: 50.0
        reason:
          type: string
          description: Reason for awarding XP (e.g., 'AgentExecution', 'TaskCompletion').
          example: AgentExecution
        metadata:
          type: object
          nullable: true
          description: Additional context or details about the XP transaction.
          example: { "workflowId": "wf-123", "taskName": "Data Analysis" }

    XpEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
          description: Unique identifier for the XP event.
        org_id:
          type: string
          format: uuid
          description: Unique identifier of the organization.
        agent_id:
          type: string
          format: uuid
          nullable: true
          description: Optional unique identifier of the AI agent.
        powerops:
          type: number
          format: float
          description: Number of PowerOps consumed for this event.
        xp_gained:
          type: number
          format: integer
          description: Experience points gained from this event.
        reason:
          type: string
          description: Reason for the XP event.
        metadata:
          type: object
          nullable: true
          description: Additional context or details.
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the XP event.
        currency:
          type: string
          example: USD
        usageBreakdown:
          type: object
          description: Detailed breakdown of usage by PowerOp type.
          example:
            AI_COMPUTATION: 800
            DATA_STORAGE: 200
            API_CALLS: 250.75
        lastUpdated:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    LogPowerOpsUsageRequest:
      type: object
      required:
        - entityId
        - entityType
        - powerOpType
        - unitsConsumed
      properties:
        entityId:
          type: string
          format: uuid
          description: ID of the user or PAIM instance consuming PowerOps.
        entityType:
          type: string
          enum: [user, paim_instance]
        powerOpType:
          type: string
          description: Type of PowerOp consumed (e.g., AI_COMPUTATION, DATA_STORAGE).
          example: AI_COMPUTATION
        unitsConsumed:
          type: number
          format: float
          description: Number of units consumed for this PowerOp.
          example: 10.5
        costPerUnit:
          type: number
          format: float
          nullable: true
          description: Cost per unit for this PowerOp (if applicable).
          example: 0.1

    PowerOpsUsageEntry:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        entityId:
          type: string
          format: uuid
        entityType:
          type: string
          enum: [user, paim_instance]
        powerOpType:
          type: string
        unitsConsumed:
          type: number
          format: float
        cost:
          type: number
          format: float
        timestamp:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    Xp:
      type: object
      properties:
        entityId:
          type: string
          format: uuid
          description: ID of the user or PAIM instance.
        entityType:
          type: string
          enum: [user, paim_instance]
        currentXp:
          type: integer
          example: 5000
        level:
          type: integer
          example: 5
        xpToNextLevel:
          type: integer
          example: 1000
        lastUpdated:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    AwardXpRequest:
      type: object
      required:
        - entityId
        - entityType
        - amount
      properties:
        entityId:
          type: string
          format: uuid
        entityType:
          type: string
          enum: [user, paim_instance]
        amount:
          type: integer
          description: Amount of XP to award.
          example: 100
        reason:
          type: string
          nullable: true
          description: Reason for awarding XP (e.g., "task completion", "daily login").

    Badge:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: b1c2d3e4-f5a6-7890-1234-567890abcdef
        name:
          type: string
          example: Early Adopter
        description:
          type: string
          example: Awarded for being among the first users.
        imageUrl:
          type: string
          format: uri
          nullable: true
          example: https://example.com/badges/early_adopter.png
        awardedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    AwardBadgeRequest:
      type: object
      required:
        - entityId
        - entityType
        - badgeId
      properties:
        entityId:
          type: string
          format: uuid
        entityType:
          type: string
          enum: [user, paim_instance]
        badgeId:
          type: string
          format: uuid
          description: ID of the badge to award.
        reason:
          type: string
          nullable: true
          description: Reason for awarding the badge.

    Achievement:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: c1d2e3f4-a5b6-7890-1234-567890abcdef
        name:
          type: string
          example: Master Orchestrator
        description:
          type: string
          example: Completed 100 workflows successfully.
        progress:
          type: number
          format: float
          example: 1.0
          description: Current progress towards the achievement (0.0 - 1.0).
        isCompleted:
          type: boolean
          example: true
        completedAt:
          type: string
          format: date-time
          nullable: true
          example: '2023-10-27T10:00:00Z'

    GrantAchievementRequest:
      type: object
      required:
        - entityId
        - entityType
        - achievementId
      properties:
        entityId:
          type: string
          format: uuid
        entityType:
          type: string
          enum: [user, paim_instance]
        achievementId:
          type: string
          format: uuid
          description: ID of the achievement to grant.
        progress:
          type: number
          format: float
          nullable: true
          description: Optional progress update for the achievement.

    Streak:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: d1e2f3a4-b5c6-7890-1234-567890abcdef
        name:
          type: string
          example: Daily Login Streak
        currentLength:
          type: integer
          example: 7
        maxLength:
          type: integer
          example: 30
        lastActivityDate:
          type: string
          format: date
          example: '2023-10-26'
        isBroken:
          type: boolean
          example: false

    Budget:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: e1f2a3b4-c5d6-7890-1234-567890abcdef
        entityId:
          type: string
          format: uuid
          description: ID of the user or PAIM instance this budget applies to.
        entityType:
          type: string
          enum: [user, paim_instance]
        monthlyLimit:
          type: number
          format: float
          example: 500.00
        currentSpend:
          type: number
          format: float
          example: 125.50
        currency:
          type: string
          example: USD
        startDate:
          type: string
          format: date
          example: '2023-10-01'
        endDate:
          type: string
          format: date
          example: '2023-10-31'
        alertThreshold:
          type: number
          format: float
          nullable: true
          description: Percentage of budget spent to trigger an alert (e.g., 0.8 for 80%).
          example: 0.8

    CreateBudgetRequest:
      type: object
      required:
        - entityId
        - entityType
        - monthlyLimit
        - currency
      properties:
        entityId:
          type: string
          format: uuid
        entityType:
          type: string
          enum: [user, paim_instance]
        monthlyLimit:
          type: number
          format: float
          example: 750.00
        currency:
          type: string
          example: USD
        alertThreshold:
          type: number
          format: float
          nullable: true
          example: 0.9

    UpdateBudgetRequest:
      type: object
      properties:
        monthlyLimit:
          type: number
          format: float
          example: 600.00
        alertThreshold:
          type: number
          format: float
          nullable: true
          example: 0.85