"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Config = void 0;
const logger_1 = __importDefault(require("../../config/logger"));
class Config {
    data = new Map();
    schema;
    validationOptions = {
        allowUnknown: false,
        stripUnknown: true,
        abortEarly: false
    };
    constructor(initialConfig, schema) {
        if (initialConfig) {
            this.load(initialConfig);
        }
        if (schema) {
            this.schema = schema;
        }
        logger_1.default.debug('Config instance created');
    }
    /**
     * Set a configuration value
     */
    set(key, value) {
        if (!key || typeof key !== 'string') {
            throw new Error('Configuration key must be a non-empty string');
        }
        // Support nested keys using dot notation
        const keys = key.split('.');
        if (keys.length === 1) {
            this.data.set(key, value);
        }
        else {
            // Handle nested configuration
            const rootKey = keys[0];
            let current = this.data.get(rootKey) || {};
            // Navigate to the parent of the target key
            let target = current;
            for (let i = 1; i < keys.length - 1; i++) {
                if (!target[keys[i]]) {
                    target[keys[i]] = {};
                }
                target = target[keys[i]];
            }
            // Set the final value
            target[keys[keys.length - 1]] = value;
            this.data.set(rootKey, current);
        }
        logger_1.default.debug(`Configuration set: ${key} = ${JSON.stringify(value)}`);
        return this;
    }
    /**
     * Get a configuration value
     */
    get(key, defaultValue) {
        if (!key || typeof key !== 'string') {
            throw new Error('Configuration key must be a non-empty string');
        }
        // Support nested keys using dot notation
        const keys = key.split('.');
        if (keys.length === 1) {
            const value = this.data.get(key);
            return value !== undefined ? value : defaultValue;
        }
        else {
            // Handle nested configuration
            const rootKey = keys[0];
            let current = this.data.get(rootKey);
            if (!current) {
                return defaultValue;
            }
            // Navigate through the nested structure
            for (let i = 1; i < keys.length; i++) {
                if (current && typeof current === 'object' && keys[i] in current) {
                    current = current[keys[i]];
                }
                else {
                    return defaultValue;
                }
            }
            return current !== undefined ? current : defaultValue;
        }
    }
    /**
     * Check if a configuration key exists
     */
    has(key) {
        if (!key || typeof key !== 'string') {
            return false;
        }
        const keys = key.split('.');
        if (keys.length === 1) {
            return this.data.has(key);
        }
        else {
            const rootKey = keys[0];
            let current = this.data.get(rootKey);
            if (!current) {
                return false;
            }
            for (let i = 1; i < keys.length; i++) {
                if (current && typeof current === 'object' && keys[i] in current) {
                    current = current[keys[i]];
                }
                else {
                    return false;
                }
            }
            return true;
        }
    }
    /**
     * Delete a configuration key
     */
    delete(key) {
        if (!key || typeof key !== 'string') {
            return false;
        }
        const keys = key.split('.');
        if (keys.length === 1) {
            const deleted = this.data.delete(key);
            if (deleted) {
                logger_1.default.debug(`Configuration deleted: ${key}`);
            }
            return deleted;
        }
        else {
            const rootKey = keys[0];
            let current = this.data.get(rootKey);
            if (!current) {
                return false;
            }
            // Navigate to the parent of the target key
            let target = current;
            for (let i = 1; i < keys.length - 1; i++) {
                if (target && typeof target === 'object' && keys[i] in target) {
                    target = target[keys[i]];
                }
                else {
                    return false;
                }
            }
            // Delete the final key
            const finalKey = keys[keys.length - 1];
            if (target && typeof target === 'object' && finalKey in target) {
                delete target[finalKey];
                logger_1.default.debug(`Configuration deleted: ${key}`);
                return true;
            }
            return false;
        }
    }
    /**
     * Load configuration from an object
     */
    load(config) {
        if (!config || typeof config !== 'object') {
            throw new Error('Configuration must be an object');
        }
        // Validate if schema is provided
        if (this.schema) {
            this.validate(config);
        }
        // Clear existing configuration
        this.data.clear();
        // Load new configuration
        Object.entries(config).forEach(([key, value]) => {
            this.data.set(key, value);
        });
        logger_1.default.info(`Configuration loaded with ${Object.keys(config).length} keys`);
        return this;
    }
    /**
     * Get all configuration as a plain object
     */
    getAll() {
        const result = {};
        this.data.forEach((value, key) => {
            result[key] = value;
        });
        return result;
    }
    /**
     * Get all configuration keys
     */
    keys() {
        return Array.from(this.data.keys());
    }
    /**
     * Clear all configuration
     */
    clear() {
        this.data.clear();
        logger_1.default.debug('Configuration cleared');
        return this;
    }
    /**
     * Set validation schema
     */
    setSchema(schema) {
        this.schema = schema;
        logger_1.default.debug('Configuration schema set');
        return this;
    }
    /**
     * Set validation options
     */
    setValidationOptions(options) {
        this.validationOptions = { ...this.validationOptions, ...options };
        logger_1.default.debug('Configuration validation options updated');
        return this;
    }
    /**
     * Validate configuration against schema
     */
    validate(config) {
        if (!this.schema) {
            logger_1.default.warn('No validation schema provided');
            return this;
        }
        const dataToValidate = config || this.getAll();
        const { error, value } = this.schema.validate(dataToValidate, this.validationOptions);
        if (error) {
            const errorMessage = `Configuration validation failed: ${error.details.map(d => d.message).join(', ')}`;
            logger_1.default.error(errorMessage);
            throw new Error(errorMessage);
        }
        // If validation passed and we're validating current data, update with cleaned values
        if (!config && this.validationOptions.stripUnknown) {
            this.load(value);
        }
        logger_1.default.debug('Configuration validation passed');
        return this;
    }
    /**
     * Merge configuration with another config object
     */
    merge(config) {
        if (!config || typeof config !== 'object') {
            throw new Error('Configuration to merge must be an object');
        }
        Object.entries(config).forEach(([key, value]) => {
            if (this.data.has(key) && typeof this.data.get(key) === 'object' && typeof value === 'object') {
                // Deep merge objects
                const existing = this.data.get(key);
                this.data.set(key, { ...existing, ...value });
            }
            else {
                this.data.set(key, value);
            }
        });
        logger_1.default.debug(`Configuration merged with ${Object.keys(config).length} keys`);
        return this;
    }
    /**
     * Create a copy of the configuration
     */
    clone() {
        const cloned = new Config();
        cloned.data = new Map(this.data);
        cloned.schema = this.schema;
        cloned.validationOptions = { ...this.validationOptions };
        logger_1.default.debug('Configuration cloned');
        return cloned;
    }
    /**
     * Load configuration from environment variables
     */
    static fromEnvironment(prefix = '') {
        const config = new Config();
        Object.entries(process.env).forEach(([key, value]) => {
            if (!prefix || key.startsWith(prefix)) {
                const configKey = prefix ? key.substring(prefix.length) : key;
                // Try to parse JSON values
                let parsedValue = value;
                if (value && (value.startsWith('{') || value.startsWith('['))) {
                    try {
                        parsedValue = JSON.parse(value);
                    }
                    catch {
                        // Keep as string if JSON parsing fails
                    }
                }
                config.set(configKey.toLowerCase(), parsedValue);
            }
        });
        logger_1.default.info(`Configuration loaded from environment with prefix '${prefix}'`);
        return config;
    }
}
exports.Config = Config;
