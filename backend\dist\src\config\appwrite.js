"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationError = exports.NotFoundError = exports.AppwriteError = exports.appwriteService = void 0;
const node_appwrite_1 = require("node-appwrite");
const logger_1 = __importDefault(require("./logger"));
const environment_1 = require("./environment");
class AppwriteService {
    client;
    databases;
    account;
    users;
    storage;
    config; // Made public
    constructor(config) {
        this.config = config;
        this.client = new node_appwrite_1.Client();
        // Check if we're in development mode with placeholder values
        const isPlaceholderConfig = config.projectId.includes('placeholder') ||
            config.databaseId.includes('placeholder');
        if (isPlaceholderConfig) {
            logger_1.default.warn('Appwrite initialized with placeholder configuration - some features may not work', {
                endpoint: config.endpoint,
                projectId: config.projectId,
                databaseId: config.databaseId,
                hasApiKey: !!config.apiKey
            });
        }
        else {
            this.client
                .setEndpoint(config.endpoint)
                .setProject(config.projectId);
            if (config.apiKey) {
                this.client.setKey(config.apiKey);
            }
            logger_1.default.info('Appwrite service initialized', {
                endpoint: config.endpoint,
                projectId: config.projectId,
                databaseId: config.databaseId,
                hasApiKey: !!config.apiKey
            });
        }
        this.databases = new node_appwrite_1.Databases(this.client);
        this.account = new node_appwrite_1.Account(this.client);
        this.users = new node_appwrite_1.Users(this.client); // Initialize Users
        this.storage = new node_appwrite_1.Storage(this.client);
    }
    // Database operations with error handling
    async createDocument(collectionId, documentId, data, permissions) {
        try {
            const result = await this.databases.createDocument(this.config.databaseId, collectionId, documentId, data, permissions);
            logger_1.default.info('Document created successfully', {
                collectionId,
                documentId: result.$id
            });
            return result; // Use any for now to bypass strict type check
        }
        catch (error) {
            logger_1.default.error('Failed to create document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document creation failed', error);
        }
    }
    async getDocument(collectionId, documentId) {
        try {
            const result = await this.databases.getDocument(this.config.databaseId, collectionId, documentId);
            return result;
        }
        catch (error) {
            if (error.code === 404) {
                throw new NotFoundError(`Document not found: ${documentId}`);
            }
            logger_1.default.error('Failed to get document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document retrieval failed', error);
        }
    }
    async updateDocument(collectionId, documentId, data, permissions) {
        try {
            const result = await this.databases.updateDocument(this.config.databaseId, collectionId, documentId, data, permissions);
            logger_1.default.info('Document updated successfully', {
                collectionId,
                documentId: result.$id
            });
            return result; // Use any for now to bypass strict type check
        }
        catch (error) {
            logger_1.default.error('Failed to update document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document update failed', error);
        }
    }
    async deleteDocument(collectionId, documentId) {
        try {
            await this.databases.deleteDocument(this.config.databaseId, collectionId, documentId);
            logger_1.default.info('Document deleted successfully', {
                collectionId,
                documentId
            });
        }
        catch (error) {
            logger_1.default.error('Failed to delete document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document deletion failed', error);
        }
    }
    async listDocuments(collectionId, queries, limit, offset) {
        try {
            const result = await this.databases.listDocuments(this.config.databaseId, collectionId);
            return {
                documents: result.documents,
                total: result.total,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to list documents', {
                collectionId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document listing failed', error);
        }
    }
    // User management operations
    // User management operations (commented out due to Appwrite SDK changes)
    // async createUser(
    //   userId: string,
    //   email: string,
    //   password?: string,
    //   name?: string
    // ): Promise<any> {
    //   try {
    //     const result = await this.users.create(userId, email, password, name);
    //     logger.info('User created successfully', {
    //       userId: result.$id,
    //       email: result.email
    //     });
    //     return result;
    //   } catch (error: any) {
    //     logger.error('Failed to create user', {
    //       userId,
    //       email,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User creation failed', error);
    //   }
    // }
    // async getUser(userId: string): Promise<any> {
    //   try {
    //     const result = await this.users.get(userId);
    //     return result;
    //   } catch (error: any) {
    //     if (error.code === 404) {
    //       throw new NotFoundError(`User not found: ${userId}`);
    //     }
    //     logger.error('Failed to get user', {
    //       userId,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User retrieval failed', error);
    //   }
    // }
    // async updateUser(userId: string, updates: Record<string, any>): Promise<any> {
    //   try {
    //     let result;
    //     if (updates.email) {
    //       result = await this.users.updateEmail(userId, updates.email);
    //     }
    //     if (updates.name) {
    //       result = await this.users.updateName(userId, updates.name);
    //     }
    //     if (updates.password) {
    //       result = await this.users.updatePassword(userId, updates.password);
    //     }
    //     logger.info('User updated successfully', {
    //       userId,
    //       updates: Object.keys(updates)
    //     });
    //     return result;
    //   } catch (error: any) {
    //     logger.error('Failed to update user', {
    //       userId,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User update failed', error);
    //   }
    // }
    // async deleteUser(userId: string): Promise<void> {
    //   try {
    //     await this.users.delete(userId);
    //     logger.info('User deleted successfully', { userId });
    //   } catch (error: any) {
    //     logger.error('Failed to delete user', {
    //       userId,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User deletion failed', error);
    //   }
    // }
    // Authentication operations
    async createSession(email, password) {
        try {
            const result = await this.account.createEmailPasswordSession(email, password);
            logger_1.default.info('Session created successfully', {
                userId: result.userId,
                sessionId: result.$id
            });
            return result;
        }
        catch (error) {
            logger_1.default.error('Failed to create session', {
                email,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session creation failed', error);
        }
    }
    async getSession() {
        try {
            const result = await this.account.getSession('current');
            return result;
        }
        catch (error) {
            if (error.code === 401) {
                throw new AuthenticationError('No active session');
            }
            logger_1.default.error('Failed to get session', {
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session retrieval failed', error);
        }
    }
    async deleteSession(sessionId) {
        try {
            await this.account.deleteSession(sessionId || 'current');
            logger_1.default.info('Session deleted successfully', { sessionId });
        }
        catch (error) {
            logger_1.default.error('Failed to delete session', {
                sessionId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session deletion failed', error);
        }
    }
    // Health check
    async healthCheck() {
        try {
            // First, try to get the project info to test basic connectivity
            const account = this.getAccount();
            await account.get();
            logger_1.default.info('Appwrite connection successful');
            return true;
        }
        catch (error) {
            logger_1.default.error('Appwrite health check failed', {
                error: error.message,
                code: error.code,
                endpoint: this.config.endpoint,
                projectId: this.config.projectId
            });
            return false;
        }
    }
    // Test connection method for debugging
    async testConnection() {
        try {
            logger_1.default.info('Testing Appwrite connection...', {
                endpoint: this.config.endpoint,
                projectId: this.config.projectId,
                databaseId: this.config.databaseId
            });
            // Try to get account info (this requires a valid project)
            const account = this.getAccount();
            await account.get();
            logger_1.default.info('Appwrite connection test successful');
            return { success: true };
        }
        catch (error) {
            const errorMsg = `Connection failed: ${error.message}`;
            logger_1.default.error('Appwrite connection test failed', {
                error: error.message,
                code: error.code,
                type: error.type,
                endpoint: this.config.endpoint,
                projectId: this.config.projectId
            });
            return { success: false, error: errorMsg };
        }
    }
    // Query builders for common operations
    createQuery() {
        return {
            equal: (attribute, value) => node_appwrite_1.Query.equal(attribute, value),
            notEqual: (attribute, value) => node_appwrite_1.Query.notEqual(attribute, value),
            lessThan: (attribute, value) => node_appwrite_1.Query.lessThan(attribute, value),
            greaterThan: (attribute, value) => node_appwrite_1.Query.greaterThan(attribute, value),
            search: (attribute, value) => node_appwrite_1.Query.search(attribute, value),
            orderAsc: (attribute) => node_appwrite_1.Query.orderAsc(attribute),
            orderDesc: (attribute) => node_appwrite_1.Query.orderDesc(attribute),
            limit: (limit) => node_appwrite_1.Query.limit(limit),
            offset: (offset) => node_appwrite_1.Query.offset(offset),
        };
    }
    // Getter methods to expose client and services
    getClient() {
        return this.client;
    }
    getAccount() {
        return this.account;
    }
    getDatabases() {
        return this.databases;
    }
    getStorage() {
        return this.storage;
    }
    getUsers() {
        return this.users;
    }
}
// Custom error classes
class AppwriteError extends Error {
    originalError;
    constructor(message, originalError) {
        super(message);
        this.originalError = originalError;
        this.name = 'AppwriteError';
    }
}
exports.AppwriteError = AppwriteError;
class NotFoundError extends Error {
    constructor(message) {
        super(message);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class AuthenticationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'AuthenticationError';
    }
}
exports.AuthenticationError = AuthenticationError;
// At the top of the file, replace the current config with:
const envConfig = (0, environment_1.validateEnvironment)();
const appwriteConfig = envConfig.appwrite;
exports.appwriteService = new AppwriteService(appwriteConfig);
