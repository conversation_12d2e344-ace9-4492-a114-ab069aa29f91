"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.withTransaction = withTransaction;
exports.generateUuid = generateUuid;
const logger_1 = __importDefault(require("../config/logger"));
/**
 * Executes a database transaction with proper error handling and rollback.
 * @param knex The Knex instance.
 * @param callback The function containing the transaction logic.
 * @returns The result of the transaction callback.
 */
async function withTransaction(knex, callback) {
    return knex.transaction(async (trx) => {
        try {
            const result = await callback(trx);
            await trx.commit();
            return result;
        }
        catch (error) {
            await trx.rollback();
            logger_1.default.error('Transaction failed, rolling back:', error.message, error.stack);
            throw error; // Re-throw the error after rollback
        }
    });
}
/**
 * Helper function to create a new UUID.
 * Note: In a real application, this might be handled by the database itself (e.g., uuid_generate_v4()).
 * This is primarily for client-side generation if needed, or for consistency in testing.
 */
function generateUuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
