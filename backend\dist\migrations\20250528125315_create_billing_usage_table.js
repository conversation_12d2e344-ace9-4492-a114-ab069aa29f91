"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.createTable('billing_usage', (table) => {
        table.uuid('org_id').notNullable();
        table.date('month').notNullable();
        table.decimal('powerops_used', 10, 2).notNullable().defaultTo(0.00);
        table.decimal('cost_usd', 10, 2).notNullable().defaultTo(0.00);
        table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
        table.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
        table.primary(['org_id', 'month']);
    });
}
async function down(knex) {
    await knex.schema.dropTable('billing_usage');
}
