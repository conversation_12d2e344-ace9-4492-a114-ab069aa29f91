"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaimService = void 0;
const paim_types_1 = require("./paim.types");
const audit_types_1 = require("../audit/audit.types");
const errors_1 = require("../utils/errors"); // Assuming a custom error class
const logger_1 = __importDefault(require("../config/logger")); // Assuming a logger instance
const uuid_1 = require("uuid"); // Import uuidv4
const db_1 = require("../types/db"); // Import PaimTierEnum
const authorization_service_1 = require("../auth/authorization.service");
const permissions_1 = require("../auth/permissions");
class PaimService {
    paimRepository;
    auditTrailService;
    notificationService;
    wsService;
    constructor(paimRepository, auditTrailService, notificationService, // Inject NotificationService
    wsService // Inject WebSocketService
    ) {
        this.paimRepository = paimRepository;
        this.auditTrailService = auditTrailService;
        this.notificationService = notificationService;
        this.wsService = wsService;
    }
    async notifyCove(escalation) {
        logger_1.default.info(`Notifying Cove for escalation ID: ${escalation.id}`);
        // In a real system, this would involve:
        // 1. Identifying the appropriate Cove agent(s) based on escalation priority, service, etc.
        // 2. Sending a notification (e.g., email, SMS, internal chat, dedicated Cove UI alert).
        // 3. Potentially creating a ticket in an incident management system.
        // For now, we'll just log the notification and simulate the process.
        await this.auditTrailService.logEvent({
            tenantId: 'system', // Assuming system-level for Cove escalations
            userId: 'system',
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.CRITICAL, // Cove notifications are critical
            operationType: 'COVE_NOTIFICATION_SENT',
            description: `Cove notified for escalation: ${escalation.id} - ${escalation.description}`,
            timestamp: new Date(),
            resourceId: escalation.id,
            metadata: { escalation },
        });
        logger_1.default.info(`Cove notification simulated for escalation ID: ${escalation.id}.`);
    }
    // PAIM Instance Management
    async createPaimInstance(data, user) {
        logger_1.default.info(`Attempting to create PAIM instance: ${data.name} for tenant ${user.tenantId} by user ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_CREATE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to create PAIM instance.', 403);
        }
        // Tier hierarchy validation: A user cannot create a PAIM instance of a tier higher than their own.
        const requestedTierLevel = (await this.paimRepository.getPaimTierByName(data.tier))?.hierarchy_level;
        const userTierLevel = (await this.paimRepository.getPaimTierByName(user.paimTier))?.hierarchy_level;
        if (requestedTierLevel !== undefined && userTierLevel !== undefined && requestedTierLevel < userTierLevel) {
            throw new errors_1.AuthorizationError('Cannot create PAIM instance of a higher tier than your own.', 403);
        }
        try {
            const paimInstance = await this.paimRepository.createPaimInstance(data, user.tenantId);
            logger_1.default.info(`PAIM instance created: ${paimInstance.id}`);
            return paimInstance;
        }
        catch (error) {
            logger_1.default.error(`Error creating PAIM instance: ${error.message}`, { error });
            if (error instanceof errors_1.AuthorizationError) {
                throw error;
            }
            if (error.code === '23505') { // Unique violation code for PostgreSQL
                throw new errors_1.CustomError(`PAIM instance with name '${data.name}' already exists.`, { originalErrorCode: 'Conflict', originalStatusCode: 409 });
            }
            throw new errors_1.CustomError('Failed to create PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    async getAllPaimInstances(user, filters, pagination) {
        logger_1.default.info(`Fetching all PAIM instances for tenant ${user.tenantId} by user ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_READ)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view PAIM instances.', 403);
        }
        // Implement filtering based on user's PAIM tier and associated PAIM instances.
        // For now, we'll fetch all and rely on the permission check above.
        // In a more complex scenario, this would involve modifying the repository query.
        try {
            const result = await this.paimRepository.getAllPaimInstances(user.tenantId, filters, pagination);
            logger_1.default.info(`Retrieved ${result.data.length} PAIM instances for tenant ${user.tenantId}`);
            return result;
        }
        catch (error) {
            logger_1.default.error(`Error fetching all PAIM instances: ${error.message}`, { error });
            throw new errors_1.CustomError('Failed to retrieve PAIM instances.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    async getPaimInstanceById(id, user) {
        logger_1.default.info(`Fetching PAIM instance ${id} for tenant ${user.tenantId} by user ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_READ)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view PAIM instance.', 403);
        }
        const paimInstance = await this.paimRepository.getPaimInstanceById(id, user.tenantId);
        if (!paimInstance) {
            logger_1.default.warn(`PAIM instance ${id} not found for tenant ${user.tenantId}`);
            throw new errors_1.CustomError(`PAIM instance with ID ${id} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Resource-level permission check: Ensure user has access to this specific PAIM instance.
        // This could involve checking if the user's organizationId matches the paimInstance's organizationId,
        // or if the user has a higher-level role (e.g., SuperAdmin, OrgAdmin) that grants access to all PAIMs.
        // For now, we'll assume the PAIM_READ permission is sufficient for viewing any PAIM within their tenant.
        // If cross-tenant access is needed, additional logic would be required here.
        logger_1.default.info(`PAIM instance ${id} retrieved successfully.`);
        return paimInstance;
    }
    async updatePaimInstance(id, user, data) {
        logger_1.default.info(`Attempting to update PAIM instance ${id} for tenant ${user.tenantId} by user ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_UPDATE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to update PAIM instance.', 403);
        }
        const existingPaim = await this.paimRepository.getPaimInstanceById(id, user.tenantId);
        if (!existingPaim) {
            logger_1.default.warn(`PAIM instance ${id} not found for update for tenant ${user.tenantId}`);
            throw new errors_1.CustomError(`PAIM instance with ID ${id} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Resource-level permission check: Ensure user has ownership or appropriate rights to update this specific PAIM instance.
        // For now, we'll assume PAIM_UPDATE permission is sufficient.
        try {
            const updatedPaim = await this.paimRepository.updatePaimInstance(id, user.tenantId, data);
            if (!updatedPaim) {
                throw new errors_1.CustomError('Failed to update PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500 });
            }
            logger_1.default.info(`PAIM instance ${id} updated successfully.`);
            // Check for status or tier changes and emit notifications/WebSocket events
            if (data.status && data.status !== existingPaim.status) {
                await this.notificationService.createNotification({
                    userId: user.userId, // Or the owner of the PAIM instance
                    type: 'info',
                    message: `PAIM instance '${updatedPaim.name}' status changed to: ${updatedPaim.status}`,
                    contextId: updatedPaim.id,
                });
                this.wsService.broadcast(JSON.stringify({
                    event: 'paimStatusUpdate',
                    payload: {
                        userId: user.userId, // Or the owner of the PAIM instance
                        paimId: updatedPaim.id,
                        status: updatedPaim.status,
                        tier: updatedPaim.tier,
                    },
                }));
            }
            if (data.tier && data.tier !== existingPaim.tier) {
                await this.notificationService.createNotification({
                    userId: user.userId, // Or the owner of the PAIM instance
                    type: 'info',
                    message: `PAIM instance '${updatedPaim.name}' tier changed to: ${updatedPaim.tier}`,
                    contextId: updatedPaim.id,
                });
                this.wsService.broadcast(JSON.stringify({
                    event: 'paimStatusUpdate',
                    payload: {
                        userId: user.userId, // Or the owner of the PAIM instance
                        paimId: updatedPaim.id,
                        status: updatedPaim.status,
                        tier: updatedPaim.tier,
                    },
                }));
            }
            return updatedPaim;
        }
        catch (error) {
            logger_1.default.error(`Error updating PAIM instance ${id}: ${error.message}`, { error });
            if (error instanceof errors_1.AuthorizationError) {
                throw error;
            }
            throw new errors_1.CustomError('Failed to update PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    async deletePaimInstance(id, user) {
        logger_1.default.info(`Attempting to delete PAIM instance ${id} for tenant ${user.tenantId} by user ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_DELETE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to delete PAIM instance.', 403);
        }
        const existingPaim = await this.paimRepository.getPaimInstanceById(id, user.tenantId);
        if (!existingPaim) {
            logger_1.default.warn(`PAIM instance ${id} not found for deletion for tenant ${user.tenantId}`);
            throw new errors_1.CustomError(`PAIM instance with ID ${id} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Resource-level permission check: Ensure user has ownership or appropriate rights to delete this specific PAIM instance.
        // For now, we'll assume PAIM_DELETE permission is sufficient.
        try {
            const deleted = await this.paimRepository.deletePaimInstance(id, user.tenantId);
            if (!deleted) {
                throw new errors_1.CustomError('Failed to delete PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500 });
            }
            logger_1.default.info(`PAIM instance ${id} deleted successfully.`);
        }
        catch (error) {
            logger_1.default.error(`Error deleting PAIM instance ${id}: ${error.message}`, { error });
            if (error instanceof errors_1.AuthorizationError) {
                throw error;
            }
            throw new errors_1.CustomError('Failed to delete PAIM instance.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    // PAIM Tier Management
    async requestPaimTierChange(paimInstanceId, user, data) {
        logger_1.default.info(`User ${user.userId} requesting tier change for PAIM instance ${paimInstanceId} to ${data.requestedTier}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to request PAIM tier change.', 403);
        }
        const paimInstance = await this.paimRepository.getPaimInstanceById(paimInstanceId, user.tenantId);
        if (!paimInstance) {
            throw new errors_1.CustomError(`PAIM instance with ID ${paimInstanceId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Business rule: Validate requested tier against current tier and user's permissions
        const currentTierLevel = (await this.paimRepository.getPaimTierByName(paimInstance.tier))?.hierarchy_level;
        const requestedTierLevel = (await this.paimRepository.getPaimTierByName(data.requestedTier))?.hierarchy_level;
        if (currentTierLevel === undefined || requestedTierLevel === undefined) {
            throw new errors_1.CustomError('Invalid current or requested PAIM tier.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        // Example rule: Cannot request a tier higher than Custom (level 1, assuming Custom is the highest)
        // This rule might need adjustment based on the actual hierarchy levels defined in the database.
        // For now, assuming Custom is the highest tier with hierarchy_level 1.
        if (requestedTierLevel < 1) { // Assuming 1 is the highest tier level
            throw new errors_1.CustomError('Cannot request a tier higher than the highest available tier.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        // Example rule: A PAIM cannot directly upgrade to Custom, requires manual intervention or specific flow
        if (data.requestedTier === db_1.PaimTierEnum.Custom && paimInstance.tier !== db_1.PaimTierEnum.Custom) {
            throw new errors_1.CustomError('Direct upgrade to Custom tier is not allowed through this API.', { originalErrorCode: 'Forbidden', originalStatusCode: 403 });
        }
        let status = paim_types_1.PaimTierChangeRequestStatusEnum.Pending;
        // For MVP, if it's a downgrade, we auto-approve and update the PAIM instance.
        // For upgrades, it remains pending, simulating a manual approval process.
        if (requestedTierLevel > currentTierLevel) { // Downgrade (e.g., from Company Admin to Power User)
            status = paim_types_1.PaimTierChangeRequestStatusEnum.Approved;
            const updatedPaim = await this.paimRepository.updatePaimInstance(paimInstanceId, user.tenantId, { tier: data.requestedTier });
            logger_1.default.info(`PAIM instance ${paimInstanceId} tier downgraded to ${data.requestedTier} and auto-approved.`);
            if (updatedPaim) {
                await this.notificationService.createNotification({
                    userId: user.userId, // Or the owner of the PAIM instance
                    type: 'info',
                    message: `PAIM instance '${updatedPaim.name}' tier successfully changed to: ${updatedPaim.tier}`,
                    contextId: updatedPaim.id,
                });
                this.wsService.broadcast(JSON.stringify({
                    event: 'paimStatusUpdate',
                    payload: {
                        userId: user.userId, // Or the owner of the PAIM instance
                        paimId: updatedPaim.id,
                        status: updatedPaim.status,
                        tier: updatedPaim.tier,
                    },
                }));
            }
        }
        else if (requestedTierLevel < currentTierLevel) { // Upgrade (e.g., from Power User to Company Admin)
            // For MVP, upgrades require manual approval.
            logger_1.default.info(`PAIM instance ${paimInstanceId} tier upgrade to ${data.requestedTier} is pending approval.`);
            await this.notificationService.createNotification({
                userId: user.userId, // Or the owner of the PAIM instance
                type: 'info',
                message: `PAIM instance '${paimInstance.name}' tier change to ${data.requestedTier} is pending approval.`,
                contextId: paimInstance.id,
            });
        }
        else { // Same tier or invalid request
            throw new errors_1.CustomError('Requested tier is the same as current tier or an invalid request.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        const tierChangeRequest = {
            requestId: (0, uuid_1.v4)(),
            paimInstanceId: paimInstanceId,
            requestedTier: data.requestedTier,
            status: status,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        // Log the request (and potentially save to a 'PaimTierChangeRequests' table)
        await this.auditTrailService.logEvent({
            tenantId: user.tenantId,
            userId: user.userId,
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'PAIM_TIER_CHANGE_REQUESTED',
            description: `Tier change requested for PAIM ${paimInstanceId} from ${paimInstance.tier} to ${data.requestedTier}. Status: ${status}`,
            timestamp: new Date(),
            resourceId: paimInstanceId,
            metadata: { paimInstanceId, currentTier: paimInstance.tier, requestedTier: data.requestedTier, status },
        });
        logger_1.default.info(`Tier change request for PAIM ${paimInstanceId} processed with status: ${status}`);
        return tierChangeRequest;
    }
    // PAIM Hierarchy Management
    async getPaimHierarchy(paimInstanceId, user) {
        logger_1.default.info(`Fetching hierarchy for PAIM instance ${paimInstanceId} for tenant ${user.tenantId} by user ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_READ)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view PAIM hierarchy.', 403);
        }
        const hierarchyTree = await this.paimRepository.getPaimHierarchy(paimInstanceId, user.tenantId);
        if (!hierarchyTree) {
            throw new errors_1.CustomError(`Hierarchy for PAIM instance with ID ${paimInstanceId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // NOTE: Permission check (e.g., ensure user has access to view this hierarchy)
        // is assumed to be handled by an authentication middleware or a dedicated authorization service.
        logger_1.default.info(`Hierarchy for PAIM instance ${paimInstanceId} retrieved successfully.`);
        return {
            paimInstanceId: paimInstanceId,
            hierarchyTree: hierarchyTree,
            lastUpdated: new Date().toISOString(), // Assuming current timestamp as last updated for MVP
        };
    }
    async updatePaimHierarchy(paimInstanceId, user, data) {
        logger_1.default.info(`Updating hierarchy for PAIM instance ${paimInstanceId} for tenant ${user.tenantId} by user ${user.userId}`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to update PAIM hierarchy.', 403);
        }
        const paimInstance = await this.paimRepository.getPaimInstanceById(paimInstanceId, user.tenantId);
        if (!paimInstance) {
            throw new errors_1.CustomError(`PAIM instance with ID ${paimInstanceId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        // Business rule: Validate the incoming hierarchy tree for correctness and adherence to tier rules.
        // e.g., a Personal PAIM cannot have children. A Power User PAIM can only have Personal children.
        // Company Admin can have Power User and Personal children. System Admin can have any.
        // Also, prevent circular dependencies.
        // NOTE: Permission check (e.g., only higher-tier PAIMs or specific roles can update hierarchy)
        // is assumed to be handled by an authentication middleware or a dedicated authorization service.
        // For MVP, the updatePaimHierarchy in the repository is a placeholder.
        // A full implementation requires a defined hierarchy schema in the database and complex validation logic.
        logger_1.default.warn(`PAIM hierarchy update for ${paimInstanceId} is a placeholder in MVP. No actual database update performed.`);
        try {
            // Simulate update success for MVP
            const updated = await this.paimRepository.updatePaimHierarchy(paimInstanceId, user.tenantId, data.hierarchyTree);
            if (!updated) {
                throw new errors_1.CustomError('Failed to update PAIM hierarchy (MVP placeholder).', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500 });
            }
            logger_1.default.info(`Hierarchy for PAIM instance ${paimInstanceId} updated successfully (MVP placeholder).`);
            return this.getPaimHierarchy(paimInstanceId, user); // Return the updated hierarchy
        }
        catch (error) {
            logger_1.default.error(`Error updating PAIM hierarchy for ${paimInstanceId}: ${error.message}`, { error });
            if (error instanceof errors_1.AuthorizationError) {
                throw error;
            }
            throw new errors_1.CustomError('Failed to update PAIM hierarchy.', { originalErrorCode: 'Internal Server Error', originalStatusCode: 500, originalError: error });
        }
    }
    // Cross-PAIM Communication (Placeholder for future implementation)
    async communicateWithPaim(sourcePaimInstanceId, user, targetPaimInstanceId, message, messageType) {
        logger_1.default.info(`PAIM ${sourcePaimInstanceId} attempting to communicate with ${targetPaimInstanceId}`);
        // Permission check for cross-PAIM communication
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.PAIM_VIEW_ALL)) { // Example permission
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions for cross-PAIM communication.', 403);
        }
        // Business logic for cross-PAIM communication:
        // 1. Validate communication permissions (e.g., can a Personal PAIM communicate with a Company Admin PAIM?)
        // 2. Route the message to the target PAIM (could involve message queues, direct API calls, etc.)
        // 3. Handle different message types (text, command, data_transfer)
        // 4. Ensure multi-tenant isolation is maintained.
        await this.auditTrailService.logEvent({
            tenantId: user.tenantId,
            userId: user.userId,
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'CROSS_PAIM_COMMUNICATION',
            description: `PAIM ${sourcePaimInstanceId} communicated with ${targetPaimInstanceId}`,
            timestamp: new Date(),
            resourceId: sourcePaimInstanceId,
            metadata: { sourcePaimInstanceId, targetPaimInstanceId, messageType, message },
        });
        return { message: 'Message successfully delivered to target PAIM.' };
    }
}
exports.PaimService = PaimService;
