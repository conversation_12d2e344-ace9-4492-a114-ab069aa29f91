"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationError = exports.NotFoundError = exports.AppwriteError = exports.appwriteService = void 0;
const appwrite_1 = require("appwrite"); // Removed Users
const logger_1 = __importDefault(require("./logger"));
class AppwriteService {
    client;
    databases;
    account;
    // private users: Users; // Removed Users
    storage;
    config;
    constructor(config) {
        this.config = config;
        this.client = new appwrite_1.Client();
        this.client
            .setEndpoint(config.endpoint)
            .setProject(config.projectId);
        // if (config.apiKey) { // setKey might be deprecated or used differently
        //   this.client.setKey(config.apiKey);
        // }
        this.databases = new appwrite_1.Databases(this.client);
        this.account = new appwrite_1.Account(this.client);
        // this.users = new Users(this.client); // Removed Users
        this.storage = new appwrite_1.Storage(this.client);
        logger_1.default.info('Appwrite service initialized', {
            endpoint: config.endpoint,
            projectId: config.projectId
        });
    }
    // Database operations with error handling
    async createDocument(collectionId, documentId, data, permissions) {
        try {
            const result = await this.databases.createDocument(this.config.databaseId, collectionId, documentId, data, permissions);
            logger_1.default.info('Document created successfully', {
                collectionId,
                documentId: result.$id
            });
            return result; // Use any for now to bypass strict type check
        }
        catch (error) {
            logger_1.default.error('Failed to create document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document creation failed', error);
        }
    }
    async getDocument(collectionId, documentId) {
        try {
            const result = await this.databases.getDocument(this.config.databaseId, collectionId, documentId);
            return result;
        }
        catch (error) {
            if (error.code === 404) {
                throw new NotFoundError(`Document not found: ${documentId}`);
            }
            logger_1.default.error('Failed to get document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document retrieval failed', error);
        }
    }
    async updateDocument(collectionId, documentId, data, permissions) {
        try {
            const result = await this.databases.updateDocument(this.config.databaseId, collectionId, documentId, data, permissions);
            logger_1.default.info('Document updated successfully', {
                collectionId,
                documentId: result.$id
            });
            return result; // Use any for now to bypass strict type check
        }
        catch (error) {
            logger_1.default.error('Failed to update document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document update failed', error);
        }
    }
    async deleteDocument(collectionId, documentId) {
        try {
            await this.databases.deleteDocument(this.config.databaseId, collectionId, documentId);
            logger_1.default.info('Document deleted successfully', {
                collectionId,
                documentId
            });
        }
        catch (error) {
            logger_1.default.error('Failed to delete document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document deletion failed', error);
        }
    }
    async listDocuments(collectionId, queries, limit, offset) {
        try {
            const result = await this.databases.listDocuments(this.config.databaseId, // Add databaseId back
            collectionId, queries, limit, offset);
            return {
                documents: result.documents,
                total: result.total,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to list documents', {
                collectionId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document listing failed', error);
        }
    }
    // User management operations
    // User management operations (commented out due to Appwrite SDK changes)
    // async createUser(
    //   userId: string,
    //   email: string,
    //   password?: string,
    //   name?: string
    // ): Promise<any> {
    //   try {
    //     const result = await this.users.create(userId, email, password, name);
    //     logger.info('User created successfully', {
    //       userId: result.$id,
    //       email: result.email
    //     });
    //     return result;
    //   } catch (error: any) {
    //     logger.error('Failed to create user', {
    //       userId,
    //       email,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User creation failed', error);
    //   }
    // }
    // async getUser(userId: string): Promise<any> {
    //   try {
    //     const result = await this.users.get(userId);
    //     return result;
    //   } catch (error: any) {
    //     if (error.code === 404) {
    //       throw new NotFoundError(`User not found: ${userId}`);
    //     }
    //     logger.error('Failed to get user', {
    //       userId,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User retrieval failed', error);
    //   }
    // }
    // async updateUser(userId: string, updates: Record<string, any>): Promise<any> {
    //   try {
    //     let result;
    //     if (updates.email) {
    //       result = await this.users.updateEmail(userId, updates.email);
    //     }
    //     if (updates.name) {
    //       result = await this.users.updateName(userId, updates.name);
    //     }
    //     if (updates.password) {
    //       result = await this.users.updatePassword(userId, updates.password);
    //     }
    //     logger.info('User updated successfully', {
    //       userId,
    //       updates: Object.keys(updates)
    //     });
    //     return result;
    //   } catch (error: any) {
    //     logger.error('Failed to update user', {
    //       userId,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User update failed', error);
    //   }
    // }
    // async deleteUser(userId: string): Promise<void> {
    //   try {
    //     await this.users.delete(userId);
    //     logger.info('User deleted successfully', { userId });
    //   } catch (error: any) {
    //     logger.error('Failed to delete user', {
    //       userId,
    //       error: error.message,
    //       code: error.code
    //     });
    //     throw new AppwriteError('User deletion failed', error);
    //   }
    // }
    // Authentication operations
    async createSession(email, password) {
        try {
            const result = await this.account.createEmailPasswordSession(email, password);
            logger_1.default.info('Session created successfully', {
                userId: result.userId,
                sessionId: result.$id
            });
            return result;
        }
        catch (error) {
            logger_1.default.error('Failed to create session', {
                email,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session creation failed', error);
        }
    }
    async getSession() {
        try {
            const result = await this.account.getSession('current');
            return result;
        }
        catch (error) {
            if (error.code === 401) {
                throw new AuthenticationError('No active session');
            }
            logger_1.default.error('Failed to get session', {
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session retrieval failed', error);
        }
    }
    async deleteSession(sessionId) {
        try {
            await this.account.deleteSession(sessionId || 'current');
            logger_1.default.info('Session deleted successfully', { sessionId });
        }
        catch (error) {
            logger_1.default.error('Failed to delete session', {
                sessionId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session deletion failed', error);
        }
    }
    // Health check
    async healthCheck() {
        try {
            await this.databases.listDocuments(this.config.databaseId, 'anyCollectionId');
            return true;
        }
        catch (error) {
            logger_1.default.error('Appwrite health check failed', {
                error: error.message,
                code: error.code
            });
            return false;
        }
    }
    // Query builders for common operations
    createQuery() {
        return {
            equal: (attribute, value) => appwrite_1.Query.equal(attribute, value),
            notEqual: (attribute, value) => appwrite_1.Query.notEqual(attribute, value),
            lessThan: (attribute, value) => appwrite_1.Query.lessThan(attribute, value),
            greaterThan: (attribute, value) => appwrite_1.Query.greaterThan(attribute, value),
            search: (attribute, value) => appwrite_1.Query.search(attribute, value),
            orderAsc: (attribute) => appwrite_1.Query.orderAsc(attribute),
            orderDesc: (attribute) => appwrite_1.Query.orderDesc(attribute),
            limit: (limit) => appwrite_1.Query.limit(limit),
            offset: (offset) => appwrite_1.Query.offset(offset),
        };
    }
}
// Custom error classes
class AppwriteError extends Error {
    originalError;
    constructor(message, originalError) {
        super(message);
        this.originalError = originalError;
        this.name = 'AppwriteError';
    }
}
exports.AppwriteError = AppwriteError;
class NotFoundError extends Error {
    constructor(message) {
        super(message);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class AuthenticationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'AuthenticationError';
    }
}
exports.AuthenticationError = AuthenticationError;
// Configuration validation
const appwriteConfig = {
    endpoint: process.env.APPWRITE_ENDPOINT,
    projectId: process.env.APPWRITE_PROJECT_ID,
    apiKey: process.env.APPWRITE_API_KEY,
    databaseId: process.env.APPWRITE_DATABASE_ID,
};
// Validate required configuration
const requiredEnvVars = ['APPWRITE_ENDPOINT', 'APPWRITE_PROJECT_ID', 'APPWRITE_DATABASE_ID'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
    logger_1.default.error('Missing required Appwrite environment variables', {
        missing: missingVars
    });
    process.exit(1);
}
exports.appwriteService = new AppwriteService(appwriteConfig);
