"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.raw(`
    -- Cultural Sensitivity and Arabic Language Support Tables

    -- Table: CulturalProfiles
    -- Stores detailed cultural preferences and settings, potentially linked to users or tenants.
    CREATE TABLE CulturalProfiles (
        profile_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        profile_name VARCHAR(255) NOT NULL,
        language_code VARCHAR(10) NOT NULL, -- e.g., 'en', 'ar', 'ar-EG', 'ar-SA'
        dialect_code VARCHAR(10), -- Specific dialect if applicable
        cultural_norms JSONB, -- JSON object for specific cultural norms and preferences
        timezone VARCHAR(100),
        currency VARCHAR(10),
        date_format VARCHAR(50),
        time_format VARCHAR(50),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_tenant_profile_name UNIQUE (tenant_id, profile_name)
    );

    -- Table: UserCulturalProfiles
    -- Links users to specific cultural profiles. A user can have one primary profile.
    CREATE TABLE UserCulturalProfiles (
        user_id UUID PRIMARY KEY REFERENCES Users(user_id) ON DELETE CASCADE,
        profile_id UUID NOT NULL REFERENCES CulturalProfiles(profile_id) ON DELETE RESTRICT,
        assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: LanguagePatterns
    -- Stores language-specific patterns, including Arabic dialects, for detection and validation.
    CREATE TABLE LanguagePatterns (
        pattern_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        language_code VARCHAR(10) NOT NULL,
        dialect_code VARCHAR(10),
        pattern_type VARCHAR(50) NOT NULL, -- e.g., 'greeting', 'farewell', 'idiom', 'sentiment'
        pattern_regex TEXT, -- Regular expression or specific phrase
        cultural_context_id UUID, -- Link to CulturalContexts if applicable
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_lang_dialect_pattern UNIQUE (language_code, dialect_code, pattern_type, pattern_regex)
    );

    -- Table: CulturalContexts
    -- Defines and stores various cultural contexts for content validation and adaptation.
    CREATE TABLE CulturalContexts (
        context_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        context_name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        cultural_values JSONB, -- Key cultural values associated with this context
        geographic_region VARCHAR(100),
        demographic_info JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: ArabicBrainLearningAggregation
    -- Aggregates learning data specific to Arabic content, potentially storing embeddings or processed text.
    CREATE TABLE ArabicBrainLearningAggregation (
        aggregation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        source_data_id UUID, -- Reference to original data source (e.g., workflow execution, agent task)
        content_hash VARCHAR(64) UNIQUE, -- SHA256 hash of the processed content to avoid duplicates
        processed_text TEXT,
        language_code VARCHAR(10) DEFAULT 'ar' NOT NULL,
        dialect_detected VARCHAR(10),
        sentiment_score FLOAT,
        cultural_relevance_score FLOAT,
        metadata JSONB, -- Additional metadata about the aggregated learning
        aggregated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: LocalizationTracking
    -- Tracks localization efforts and adaptations for various content types.
    CREATE TABLE LocalizationTracking (
        localization_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        content_type VARCHAR(100) NOT NULL, -- e.g., 'workflow_definition', 'agent_persona', 'ui_text'
        content_id UUID NOT NULL, -- ID of the content being localized
        source_language VARCHAR(10) NOT NULL,
        target_language VARCHAR(10) NOT NULL,
        target_dialect VARCHAR(10),
        adaptation_details JSONB, -- Details of the localization/adaptation applied
        status VARCHAR(50) NOT NULL, -- e.g., 'pending', 'in_progress', 'completed', 'reviewed'
        localized_by UUID REFERENCES Users(user_id),
        localized_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_localization_target UNIQUE (content_id, target_language, target_dialect)
    );

    -- Indexes for performance optimization
    CREATE INDEX idx_culturalprofiles_tenant_id ON CulturalProfiles(tenant_id);
    CREATE INDEX idx_culturalprofiles_language_code ON CulturalProfiles(language_code);
    CREATE INDEX idx_userculturalprofiles_user_id ON UserCulturalProfiles(user_id);
    CREATE INDEX idx_languagepatterns_language_code ON LanguagePatterns(language_code);
    CREATE INDEX idx_languagepatterns_dialect_code ON LanguagePatterns(dialect_code);
    CREATE INDEX idx_culturalcontexts_name ON CulturalContexts(context_name);
    CREATE INDEX idx_arabicbrainlearning_tenant_id ON ArabicBrainLearningAggregation(tenant_id);
    CREATE INDEX idx_arabicbrainlearning_language_code ON ArabicBrainLearningAggregation(language_code);
    CREATE INDEX idx_localizationtracking_tenant_id ON LocalizationTracking(tenant_id);
    CREATE INDEX idx_localizationtracking_content_id ON LocalizationTracking(content_id);
    CREATE INDEX idx_localizationtracking_target_language ON LocalizationTracking(target_language);
  `);
}
async function down(knex) {
    await knex.schema.raw(`
    DROP TABLE IF EXISTS LocalizationTracking;
    DROP TABLE IF EXISTS ArabicBrainLearningAggregation;
    DROP TABLE IF EXISTS CulturalContexts;
    DROP TABLE IF EXISTS LanguagePatterns;
    DROP TABLE IF EXISTS UserCulturalProfiles;
    DROP TABLE IF EXISTS CulturalProfiles;
  `);
}
