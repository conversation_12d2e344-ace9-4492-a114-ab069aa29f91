import { v4 as uuidv4 } from 'uuid';
import db from '../database/db';
import {
  Agent,
  CreateAgentRequest,
  UpdateAgentRequest,
  AgentAssignment,
  AgentAssignmentRequest,
  WorkflowExecutionStatus,
  AgentPerformanceMetrics,
  AgentQueryOptions,
} from './agent.types';
import { generatePagination, PaginationMetadata } from '../utils/pagination';
import { AuditTrailService } from '../audit/audit.service';
import { AuditEventCategory, AuditEventSeverity } from '../audit/audit.types';
import { getParam, requireParam } from '../utils/type-guards';

export class AgentRepository {
  private tableName = 'AgentDefinitions';
  private agentAssignmentsTableName = 'AgentAssignments';
  private workflowExecutionsTableName = 'WorkflowExecutions';
  private agentPerformanceMetricsTableName = 'AgentPerformanceMetrics';

  constructor(private auditTrailService: AuditTrailService) {}

  // Helper to ensure tenant isolation
  private applyTenantIsolation(queryBuilder: any, tenantId: string) {
    return queryBuilder.where('tenant_id', tenantId);
  }

  public async getAllAgents(
    tenantId: string,
    options: AgentQueryOptions,
  ): Promise<{ agents: Agent[]; pagination: PaginationMetadata }> {
    const { page = 1, size = 10, sort, persona, status } = options;

    let query = db(this.tableName)
      .select(
        'agent_id as id',
        'name',
        'persona',
        'description',
        'status',
        'paim_instance_id as paimInstanceId',
        'capabilities',
        'created_at as createdAt',
        'updated_at as updatedAt'
      );
    query = this.applyTenantIsolation(query, tenantId);

    if (persona) {
      query = query.where('persona', persona);
    }
    if (status) {
      query = query.where('status', status);
    }

    const totalCountQuery = db(this.tableName)
      .count('* as count')
      .where((builder: any) => this.applyTenantIsolation(builder, tenantId));

    if (persona) {
      totalCountQuery.where('persona', persona);
    }
    if (status) {
      totalCountQuery.where('status', status);
    }

    const totalCountResult = await totalCountQuery.first();
    const total = totalCountResult ? (totalCountResult.count as number) : 0;

    if (sort) {
      const [sortBy, sortOrder] = getParam(sort).split(':');
      query = query.orderBy(requireParam(sortBy, 'sortBy'), getParam(sortOrder, 'asc'));
    }

    const offset = (page - 1) * size;
    query = query.limit(size).offset(offset);

    const agents = (await query).map((agent: any) => ({
      ...agent,
      capabilities: agent.capabilities ? JSON.parse(agent.capabilities) : [],
    }));
    const pagination = generatePagination(total, page, size);

    return { agents, pagination };
  }

  public async getAgentById(tenantId: string, id: string): Promise<Agent | undefined> {
    const agent = await this.applyTenantIsolation(db(this.tableName).where('agent_id', id), tenantId)
      .select(
        'agent_id as id',
        'name',
        'persona',
        'description',
        'status',
        'paim_instance_id as paimInstanceId',
        'capabilities',
        'created_at as createdAt',
        'updated_at as updatedAt'
      )
      .first();

    if (!agent) {
      return undefined;
    }

    return {
      ...agent,
      capabilities: agent.capabilities ? JSON.parse(agent.capabilities) : [],
    };
  }

  public async createAgent(tenantId: string, agentData: CreateAgentRequest): Promise<Agent> {
    const newAgent: Agent = {
      id: uuidv4(),
      ...agentData,
      ownerId: agentData.ownerId || 'system',
      tenantId: tenantId,
      status: 'inactive', // Default status
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      capabilities: agentData.capabilities || [],
      paimInstanceId: agentData.paimInstanceId,
    };

    await db(this.tableName).insert({
      agent_id: newAgent.id,
      tenant_id: tenantId,
      paim_instance_id: newAgent.paimInstanceId,
      name: newAgent.name,
      persona: newAgent.persona,
      description: newAgent.description,
      status: newAgent.status,
      capabilities: JSON.stringify(newAgent.capabilities),
      created_at: newAgent.createdAt,
      updated_at: newAgent.updatedAt,
    });
    return newAgent;
  }

  public async updateAgent(tenantId: string, id: string, agentData: UpdateAgentRequest): Promise<Agent | undefined> {
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };
    if (agentData.name) updateData.name = agentData.name;
    if (agentData.persona) updateData.persona = agentData.persona;
    if (agentData.description) updateData.description = agentData.description;
    if (agentData.status) updateData.status = agentData.status;
    if (agentData.capabilities) updateData.capabilities = JSON.stringify(agentData.capabilities);

    const [count] = await this.applyTenantIsolation(db(this.tableName).where('agent_id', id), tenantId).update(updateData);

    if (count === 0) {
      return undefined;
    }
    return this.getAgentById(tenantId, id);
  }

  public async deleteAgent(tenantId: string, id: string): Promise<boolean> {
    const count = await this.applyTenantIsolation(db(this.tableName).where('agent_id', id), tenantId).del();
    return count > 0;
  }

  public async assignAgent(tenantId: string, assignmentData: AgentAssignmentRequest & { agentId: string }): Promise<AgentAssignment> {
    const newAssignment: AgentAssignment = {
      id: uuidv4(),
      ...assignmentData,
      assignedAt: new Date().toISOString(),
    };
    await db(this.agentAssignmentsTableName).insert({
      assignment_id: newAssignment.id,
      agent_id: newAssignment.agentId,
      assigned_to_type: newAssignment.assignedToType,
      assigned_to_id: newAssignment.assignedToId,
      assignment_type: newAssignment.assignmentType,
      assigned_at: newAssignment.assignedAt,
      tenant_id: tenantId,
    });
    return newAssignment;
  }

  public async recordWorkflowExecution(tenantId: string, executionData: WorkflowExecutionStatus): Promise<WorkflowExecutionStatus> {
    await db(this.workflowExecutionsTableName).insert({
      execution_id: executionData.executionId,
      workflow_id: executionData.workflowId,
      tenant_id: tenantId,
      status: executionData.status,
      start_time: executionData.startTime,
      end_time: executionData.endTime,
      output_data: executionData.outputData ? JSON.stringify(executionData.outputData) : null,
      error_details: executionData.error ? JSON.stringify({ message: executionData.error }) : null,
    });
    return executionData;
  }

  public async getAgentPerformanceMetrics(tenantId: string, agentId: string, startDate?: string, endDate?: string): Promise<AgentPerformanceMetrics | undefined> {
    let query = db(this.agentPerformanceMetricsTableName).where('agent_id', agentId);
    query = this.applyTenantIsolation(query, tenantId);

    if (startDate) {
      query = query.where('timestamp', '>=', startDate);
    }
    if (endDate) {
      query = query.where('timestamp', '<=', endDate);
    }

    // This is a simplified aggregation. In a real scenario, you'd aggregate metrics over the period.
    const metrics = await query.first(); // Assuming one entry per agent for simplicity or latest.
    return metrics as AgentPerformanceMetrics | undefined;
  }

  public async recordAuditTrail(tenantId: string, action: string, entityId: string, details: object, userId?: string) {
    let category: AuditEventCategory;
    let severity: AuditEventSeverity = AuditEventSeverity.INFO;

    switch (action) {
      case 'agent_created':
      case 'agent_updated':
      case 'agent_deleted':
      case 'agent_assigned':
        category = AuditEventCategory.AGENT_OPERATION;
        break;
      case 'workflow_execution_initiated':
        category = AuditEventCategory.SYSTEM_OPERATION; // Or a more specific WORKFLOW_OPERATION
        break;
      default:
        category = AuditEventCategory.SYSTEM_OPERATION;
        break;
    }

    await this.auditTrailService.logEvent({
      tenantId,
      userId: userId || 'system', // Use provided userId or default to 'system'
      category,
      severity,
      operationType: action,
      description: `Agent related action: ${action} for entity ${entityId}`,
      timestamp: new Date(),
      resourceId: entityId,
      metadata: details,
    });
  }
}