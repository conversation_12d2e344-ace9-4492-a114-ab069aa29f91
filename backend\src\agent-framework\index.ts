/**
 * AI Agent Framework - Main Entry Point
 * 
 * This module provides the main interface for the AI Agent Framework,
 * including initialization, configuration, and access to core components.
 */

import { Agent } from './core/Agent';
import { Config } from './core/Config';
import { EventEmitter } from './core/EventEmitter';
import { Plugin, PluginRegistry } from './core/Plugin';
import { CLAPIAgent } from './agents/CLAPIAgent';
import { R1LogicAgent } from './agents/R1LogicAgent';
import { DevOpsAgent } from './agents/DevOpsAgent';
import { ToolRegistry } from './tools/ToolRegistry';
import { WorkflowEngine } from './workflow/WorkflowEngine';
import logger from '../config/logger';
import Joi from 'joi';

export interface FrameworkConfig {
  core: {
    logLevel: string;
    timeout: number;
    maxConcurrency: number;
  };
  agentRegistry: {
    [key: string]: {
      enabled: boolean;
      config?: Record<string, any>;
    };
  };
  agents?: {
    [key: string]: {
      enabled: boolean;
      module?: string;
      config?: Record<string, any>;
    };
  };
  plugins?: {
    [key: string]: {
      enabled: boolean;
      config?: Record<string, any>;
    };
  };
}

/**
 * Default framework configuration
 */
const DEFAULT_CONFIG: FrameworkConfig = {
  core: {
    logLevel: 'info',
    timeout: 30000,
    maxConcurrency: 5
  },
  agentRegistry: {
    'CL-API': {
      enabled: true,
      config: {
        rateLimit: {
          enabled: true,
          requestsPerMinute: 100,
          burstLimit: 10
        }
      }
    },
    'R1-Logic': {
      enabled: true,
      config: {
        cache: {
          timeout: 300000
        }
      }
    },
    'K1-Perf': {
      enabled: false
    },
    'UX-Design': {
      enabled: false
    },
    'DevOps': {
      enabled: true,
      config: {
        alerts: {
          cpu: { threshold: 80 },
          memory: { threshold: 85 },
          disk: { threshold: 90 },
          responseTime: { threshold: 5000 }
        }
      }
    }
  }
};

/**
 * Configuration validation schema
 */
const CONFIG_SCHEMA = Joi.object({
  core: Joi.object({
    logLevel: Joi.string().valid('debug', 'info', 'warn', 'error').default('info'),
    timeout: Joi.number().min(1000).max(300000).default(30000),
    maxConcurrency: Joi.number().min(1).max(100).default(5)
  }).required(),
  agentRegistry: Joi.object().pattern(
    Joi.string(),
    Joi.object({
      enabled: Joi.boolean().required(),
      config: Joi.object().optional()
    })
  ).required(),
  agents: Joi.object().pattern(
    Joi.string(),
    Joi.object({
      enabled: Joi.boolean().required(),
      module: Joi.string().optional(),
      config: Joi.object().optional()
    })
  ).optional(),
  plugins: Joi.object().pattern(
    Joi.string(),
    Joi.object({
      enabled: Joi.boolean().required(),
      config: Joi.object().optional()
    })
  ).optional()
});

/**
 * Main Agent Framework class
 */
export class AgentFramework extends EventEmitter {
  private static instance: AgentFramework;
  private config: Config;
  private initialized: boolean = false;
  private predefinedAgents: Map<string, Agent> = new Map();
  private toolRegistry: ToolRegistry;
  private workflowEngine: WorkflowEngine;
  private pluginRegistry: PluginRegistry;

  private constructor() {
    super();
    this.config = new Config();
    this.toolRegistry = ToolRegistry.getInstance();
    this.workflowEngine = WorkflowEngine.getInstance();
    this.pluginRegistry = PluginRegistry.getInstance();
  }

  /**
   * Get the singleton instance of the framework
   */
  public static getInstance(): AgentFramework {
    if (!AgentFramework.instance) {
      AgentFramework.instance = new AgentFramework();
    }
    return AgentFramework.instance;
  }

  /**
   * Initialize the framework with configuration
   */
  public async initialize(userConfig?: Partial<FrameworkConfig>): Promise<void> {
    if (this.initialized) {
      logger.warn('Framework is already initialized');
      return;
    }

    try {
      // Merge user config with defaults
      const mergedConfig = this.mergeConfig(DEFAULT_CONFIG, userConfig || {});

      // Validate configuration
      const { error, value } = CONFIG_SCHEMA.validate(mergedConfig);
      if (error) {
        throw new Error(`Configuration validation failed: ${error.message}`);
      }

      // Load configuration
      this.config.load(value);

      // Initialize core components
      await this.initializeCore();

      // Initialize predefined agents
      await this.initializePredefinedAgents();

      // Initialize custom agents
      if (value.agents) {
        await this.initializeCustomAgents(value.agents);
      }

      // Initialize plugins
      if (value.plugins) {
        await this.initializePlugins(value.plugins);
      }

      this.initialized = true;
      this.emit('framework:initialized', { config: value });
      
      logger.info('AI Agent Framework initialized successfully');

    } catch (error) {
      logger.error('Framework initialization failed:', error);
      throw error;
    }
  }

  /**
   * Shutdown the framework
   */
  public async shutdown(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      // Stop all agents
      for (const agent of this.predefinedAgents.values()) {
        await agent.stop();
      }

      // Clear registries
      this.toolRegistry.clear();
      this.pluginRegistry.clear();

      this.initialized = false;
      this.emit('framework:shutdown');
      
      logger.info('AI Agent Framework shutdown completed');

    } catch (error) {
      logger.error('Framework shutdown failed:', error);
      throw error;
    }
  }

  /**
   * Get framework configuration
   */
  public getConfig(): Config {
    return this.config;
  }

  /**
   * Get a predefined agent by name
   */
  public getAgent(name: string): Agent | undefined {
    return this.predefinedAgents.get(name) || Agent.getAgent(name);
  }

  /**
   * Get all predefined agents
   */
  public getPredefinedAgents(): Map<string, Agent> {
    return new Map(this.predefinedAgents);
  }

  /**
   * Get tool registry
   */
  public getToolRegistry(): ToolRegistry {
    return this.toolRegistry;
  }

  /**
   * Get workflow engine
   */
  public getWorkflowEngine(): WorkflowEngine {
    return this.workflowEngine;
  }

  /**
   * Get plugin registry
   */
  public getPluginRegistry(): PluginRegistry {
    return this.pluginRegistry;
  }

  /**
   * Check if framework is initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Validate configuration
   */
  public static validate(config: any): void {
    const { error } = CONFIG_SCHEMA.validate(config);
    if (error) {
      throw new Error(`Configuration validation failed: ${error.message}`);
    }
  }

  /**
   * Create a new agent instance
   */
  public createAgent(id: string, name: string, type: string, config?: Config): Agent {
    const agent = new Agent(id, name, type, config);
    Agent.registerAgent(name, agent);
    return agent;
  }

  /**
   * Execute a task with the best available agent
   */
  public async executeTask(taskType: string, payload: any, agentName?: string): Promise<any> {
    if (!this.initialized) {
      throw new Error('Framework is not initialized');
    }

    let agent: Agent | undefined;

    if (agentName) {
      agent = this.getAgent(agentName);
      if (!agent) {
        throw new Error(`Agent '${agentName}' not found`);
      }
    } else {
      // Find the best agent for the task type
      agent = this.findBestAgent(taskType);
      if (!agent) {
        throw new Error(`No suitable agent found for task type '${taskType}'`);
      }
    }

    const task = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: taskType,
      payload,
      timestamp: new Date()
    };

    return await agent.execute(task);
  }

  /**
   * Get framework status
   */
  public getStatus(): {
    initialized: boolean;
    agents: { name: string; active: boolean; type: string }[];
    tools: number;
    workflows: number;
    plugins: number;
  } {
    const agents = Array.from(this.predefinedAgents.values()).map(agent => ({
      name: agent.name,
      active: agent.getIsActive(),
      type: agent.type
    }));

    return {
      initialized: this.initialized,
      agents,
      tools: this.toolRegistry.getAll().length,
      workflows: this.workflowEngine.getAllWorkflows().length,
      plugins: this.pluginRegistry.getAll().length
    };
  }

  // Private methods

  private mergeConfig(defaultConfig: FrameworkConfig, userConfig: Partial<FrameworkConfig>): FrameworkConfig {
    return {
      core: { ...defaultConfig.core, ...userConfig.core },
      agentRegistry: { ...defaultConfig.agentRegistry, ...userConfig.agentRegistry },
      agents: userConfig.agents || {},
      plugins: userConfig.plugins || {}
    };
  }

  private async initializeCore(): Promise<void> {
    // Set up core configuration
    const coreConfig = this.config.get('core');
    
    // Configure logger level if needed
    if (coreConfig.logLevel) {
      // Logger level configuration would go here
    }

    logger.info('Core framework components initialized');
  }

  private async initializePredefinedAgents(): Promise<void> {
    const agentRegistry = this.config.get('agentRegistry');

    for (const [agentName, agentConfig] of Object.entries(agentRegistry)) {
      if (!(agentConfig as any).enabled) {
        continue;
      }

      try {
        let agent: Agent;
        const config = new Config((agentConfig as any).config || {});

        switch (agentName) {
          case 'CL-API':
            agent = new CLAPIAgent(config);
            break;
          case 'R1-Logic':
            agent = new R1LogicAgent(config);
            break;
          case 'DevOps':
            agent = new DevOpsAgent(config);
            break;
          default:
            logger.warn(`Unknown predefined agent: ${agentName}`);
            continue;
        }

        await agent.start();
        this.predefinedAgents.set(agentName, agent);
        Agent.registerAgent(agentName, agent);

        logger.info(`Predefined agent '${agentName}' initialized and started`);

      } catch (error) {
        logger.error(`Failed to initialize predefined agent '${agentName}':`, error);
      }
    }
  }

  private async initializeCustomAgents(agentsConfig: Record<string, any>): Promise<void> {
    for (const [agentName, agentConfig] of Object.entries(agentsConfig)) {
      if (!agentConfig.enabled) {
        continue;
      }

      try {
        // Custom agent initialization would go here
        // This would typically involve loading modules dynamically
        logger.info(`Custom agent '${agentName}' would be initialized here`);

      } catch (error) {
        logger.error(`Failed to initialize custom agent '${agentName}':`, error);
      }
    }
  }

  private async initializePlugins(pluginsConfig: Record<string, any>): Promise<void> {
    for (const [pluginName, pluginConfig] of Object.entries(pluginsConfig)) {
      if (!pluginConfig.enabled) {
        continue;
      }

      try {
        // Plugin initialization would go here
        logger.info(`Plugin '${pluginName}' would be initialized here`);

      } catch (error) {
        logger.error(`Failed to initialize plugin '${pluginName}':`, error);
      }
    }
  }

  private findBestAgent(taskType: string): Agent | undefined {
    // Simple agent selection logic
    // In production, this would be more sophisticated
    
    if (taskType.includes('api') || taskType.includes('request')) {
      return this.predefinedAgents.get('CL-API');
    }
    
    if (taskType.includes('logic') || taskType.includes('business') || taskType.includes('decision')) {
      return this.predefinedAgents.get('R1-Logic');
    }
    
    if (taskType.includes('deploy') || taskType.includes('health') || taskType.includes('monitor')) {
      return this.predefinedAgents.get('DevOps');
    }

    // Default to first available agent
    return Array.from(this.predefinedAgents.values())[0];
  }
}

// Export main classes and interfaces
export {
  Agent,
  Config,
  EventEmitter,
  Plugin,
  PluginRegistry,
  CLAPIAgent,
  R1LogicAgent,
  DevOpsAgent,
  ToolRegistry,
  WorkflowEngine
};

// Export types
export * from './core/Agent';
export * from './core/Config';
export * from './core/Plugin';
export * from './agents/CLAPIAgent';
export * from './agents/R1LogicAgent';
export * from './agents/DevOpsAgent';
export * from './tools/ToolRegistry';
export * from './workflow/WorkflowEngine';

// Default export
export default AgentFramework;
