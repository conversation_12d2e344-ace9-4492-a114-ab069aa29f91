import Joi from 'joi';
import {
  EntityType,
  CostCategory,
  // CrossPaimCommunicationMessageType, // Not used in PowerOps validation
} from './powerops.types';

// Joi schema for LogPowerOpsUsageRequest
export const logPowerOpsUsageSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  usageUnits: Joi.number().min(0).required(),
  costCategory: Joi.string().valid(...Object.values(CostCategory)).required(),
  description: Joi.string().trim().max(500).optional().allow(null, ''),
  timestamp: Joi.string().isoDate().required(),
});

// Joi schema for AwardXpRequest
export const awardXpSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  org_id: Joi.string().required(), // Assuming org_id is a string
  agent_id: Joi.string().optional().allow(null, ''),
  powerops: Joi.number().integer().min(1).required(),
  reason: Joi.string().trim().min(3).max(200).required(),
  metadata: Joi.object().optional(),
});

// Joi schema for AwardBadgeRequest
export const awardBadgeSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  badgeId: Joi.string().guid({ version: 'uuidv4' }).required(),
});

// Joi schema for GrantAchievementRequest
export const grantAchievementSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  achievementId: Joi.string().guid({ version: 'uuidv4' }).required(),
});

// Joi schema for CreateBudgetRequest
export const createBudgetSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  monthlyLimit: Joi.number().min(0).required(),
  currency: Joi.string().length(3).uppercase().required(),
  alertThreshold: Joi.number().min(0).max(100).optional(),
});

// Joi schema for UpdateBudgetRequest
export const updateBudgetSchema = Joi.object({
  monthlyLimit: Joi.number().min(0).optional(),
  alertThreshold: Joi.number().min(0).max(100).optional(),
});

// Joi schema for CreateInvoiceRequest
export const createInvoiceSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  billingPeriodStart: Joi.string().isoDate().required(),
  billingPeriodEnd: Joi.string().isoDate().required(),
  lineItems: Joi.array().items(Joi.object({
    description: Joi.string().required(),
    quantity: Joi.number().min(0).required(),
    unitPrice: Joi.number().min(0).required(),
    total: Joi.number().min(0).required(),
  })).min(1).required(),
  paymentMethodId: Joi.string().optional().allow(null, ''),
});

// Joi schema for ProcessPaymentRequest
export const processPaymentSchema = Joi.object({
  invoiceId: Joi.string().guid({ version: 'uuidv4' }).required(),
  amount: Joi.number().min(0.01).required(),
  currency: Joi.string().length(3).uppercase().required(),
  paymentMethod: Joi.string().required(),
  transactionDetails: Joi.object().optional(),
});

// Joi schema for SetResourceUsageLimitRequest
export const setResourceUsageLimitSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  resourceType: Joi.string().required(),
  limit: Joi.number().min(0).required(),
  unit: Joi.string().required(),
});

// Joi schema for CreateNotificationRequest
export const createNotificationSchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
  type: Joi.string().required(), // Can be more specific with enum if available
  message: Joi.string().min(1).max(1000).required(),
});

// Joi schema for query parameters with entityId and entityType
export const entityIdEntityTypeQuerySchema = Joi.object({
  entityId: Joi.string().guid({ version: 'uuidv4' }).required(),
  entityType: Joi.string().valid(...Object.values(EntityType)).required(),
});

// Joi schema for query parameters for getPowerOpsUsage
export const getPowerOpsUsageQuerySchema = entityIdEntityTypeQuerySchema.keys({
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
});

// Joi schema for query parameters for getLeaderboard
export const getLeaderboardQuerySchema = Joi.object({
  metric: Joi.string().required(),
  limit: Joi.number().integer().min(1).max(100).optional(),
});