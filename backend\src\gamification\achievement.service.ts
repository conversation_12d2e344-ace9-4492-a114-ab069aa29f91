import { Injectable } from '@nestjs/common';
import { PowerOpsService } from '../powerops/powerops.service';
import { NotificationService } from '../notifications/notification.service';
import { JwtPayload } from '../auth/auth.types';
import { EntityType } from '../powerops/powerops.types';
import { PaimTierEnum } from '../types/db';

@Injectable()
export class AchievementService {
  constructor(
    private powerOpsService: PowerOpsService,
    private notificationService: NotificationService
  ) {}

  async getAchievementProgress(userId: string) {
    const user: JwtPayload = {
      userId,
      tenantId: 'tenant-123',
      email: '<EMAIL>',
      roles: [],
      paimTier: PaimTierEnum.Basic,
      organizationId: 'org-123',
      teamId: 'team-456'
    };
    
    const achievements = await this.powerOpsService.getAchievements(
      user,
      userId,
      EntityType.User
    );

    return {
      achievements: achievements,
      totalCount: achievements.length
    };
  }
}