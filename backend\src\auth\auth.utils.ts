import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { AuthToken, JwtPayload } from './auth.types';
import { User } from '../types/db';

// Configuration for JWT
const JWT_SECRET: jwt.Secret = process.env.JWT_SECRET || 'supersecretjwtkey'; // TODO: Use a strong, environment-specific secret
const JWT_ACCESS_TOKEN_EXPIRATION: string = process.env.JWT_ACCESS_TOKEN_EXPIRATION || '15m'; // Short-lived access token
const JWT_REFRESH_TOKEN_EXPIRATION: string = process.env.JWT_REFRESH_TOKEN_EXPIRATION || '7d'; // Longer-lived refresh token

// Password Hashing
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 10; // Recommended salt rounds for bcrypt
  return bcrypt.hash(password, saltRounds);
};

export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};

// JWT Token Generation
export const generateTokens = (user: User, tenantId: string, roles: string[]): AuthToken => {
  const payload: JwtPayload = {
    userId: user.user_id,
    tenantId: tenantId,
    email: user.email,
    paimTier: user.paim_tier,
    roles: roles as any[], // Temporarily cast for Phase 2
  };

  const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_ACCESS_TOKEN_EXPIRATION } as jwt.SignOptions);
  const refreshToken = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_REFRESH_TOKEN_EXPIRATION } as jwt.SignOptions);

  // Calculate expiresIn in seconds for the AuthToken interface
  const parseDurationToSeconds = (duration: string): number => {
    const value = parseInt(duration);
    if (duration.includes('m')) {
      return value * 60;
    } else if (duration.includes('h')) {
      return value * 3600;
    } else if (duration.includes('d')) {
      return value * 86400;
    }
    return value; // Assume seconds if no unit
  };

  return {
    accessToken,
    refreshToken,
    expiresIn: parseDurationToSeconds(JWT_ACCESS_TOKEN_EXPIRATION),
  };
};

// JWT Token Verification
export const verifyToken = (token: string): JwtPayload | null => {
  try {
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};