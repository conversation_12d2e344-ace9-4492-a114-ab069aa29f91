"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingRepository = void 0;
class BillingRepository {
    knex;
    constructor(knex) {
        this.knex = knex;
    }
    async insertPowerOpsUsage(data) {
        const [record] = await this.knex('billing_usage').insert(data).returning('*');
        return record;
    }
    async getBillingUsageRecord(org_id, month, trx) {
        const query = (trx || this.knex)('billing_usage')
            .where({ org_id, month })
            .first();
        return query;
    }
    async updatePowerOpsUsage(org_id, month, powerops_used, cost_usd, trx) {
        const [record] = await (trx || this.knex)('billing_usage')
            .where({ org_id, month })
            .update({ powerops_used, cost_usd, updated_at: this.knex.fn.now() })
            .returning('*');
        return record;
    }
    async getTotalPowerOpsUsage(org_id) {
        const result = await this.knex('billing_usage')
            .where({ org_id })
            .sum('powerops_used as total_powerops_used')
            .sum('cost_usd as total_cost_usd')
            .first();
        if (result && (result.total_powerops_used !== null || result.total_cost_usd !== null)) {
            return {
                total_powerops_used: parseFloat(result.total_powerops_used) || 0,
                total_cost_usd: parseFloat(result.total_cost_usd) || 0,
            };
        }
        return undefined;
    }
    async getMonthlyPowerOpsUsage(org_id) {
        return this.knex('billing_usage')
            .where({ org_id })
            .orderBy('month', 'asc');
    }
    async getAllOrganizationsMonthlyUsage() {
        return this.knex('billing_usage')
            .orderBy('month', 'asc')
            .orderBy('org_id', 'asc');
    }
}
exports.BillingRepository = BillingRepository;
