"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationRepository = void 0;
const db_1 = __importDefault(require("../database/db")); // Assuming 'db' is your Knex instance
class NotificationRepository {
    tableName = 'notifications'; // Assuming a 'notifications' table exists or will be created
    async createNotification(notificationData) {
        const [newNotification] = await (0, db_1.default)(this.tableName)
            .insert({
            userId: notificationData.userId,
            type: notificationData.type,
            message: notificationData.message,
            link: notificationData.link,
            contextId: notificationData.contextId,
            read: false, // Default to unread
            createdAt: new Date().toISOString(),
        })
            .returning('*'); // Return all columns of the inserted row
        return newNotification;
    }
    async getNotifications(userId, read, page = 1, size = 10, sort) {
        let query = (0, db_1.default)(this.tableName);
        if (userId) {
            query = query.where({ userId });
        }
        if (read !== undefined) {
            query = query.where({ read });
        }
        const total = await query.clone().count('* as count').first();
        const notifications = await query
            .limit(size)
            .offset((page - 1) * size)
            .orderBy(sort || 'createdAt', 'desc'); // Default sort by createdAt descending
        return { notifications, total: parseInt(total.count, 10) };
    }
    async getNotificationById(notificationId) {
        return (0, db_1.default)(this.tableName).where({ id: notificationId }).first();
    }
    async updateNotification(notificationId, updateData) {
        const [updatedNotification] = await (0, db_1.default)(this.tableName)
            .where({ id: notificationId })
            .update(updateData)
            .returning('*');
        return updatedNotification;
    }
    async deleteNotification(notificationId) {
        const deletedCount = await (0, db_1.default)(this.tableName).where({ id: notificationId }).del();
        return deletedCount > 0;
    }
}
exports.NotificationRepository = NotificationRepository;
