"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreativeApiService = void 0;
const creative_api_types_1 = require("./creative-api.types");
const audit_types_1 = require("../audit/audit.types");
const http_client_service_1 = require("../utils/http-client.service");
class CreativeApiService {
    auditTrailService;
    providers;
    localStableDiffusionEndpoint;
    httpClient;
    // Circuit Breaker state
    circuitBreakerState = 'CLOSED';
    failureCount = 0;
    lastFailureTime = 0;
    FAILURE_THRESHOLD = 5; // Number of consecutive failures to open circuit
    RESET_TIMEOUT_MS = 30000; // 30 seconds to transition to HALF_OPEN
    constructor(auditTrailService) {
        this.auditTrailService = auditTrailService;
        this.httpClient = http_client_service_1.creativeApiClient;
        // Configure primary and fallback API providers
        this.providers = [
            {
                name: creative_api_types_1.CreativeApiProvider.RUNWAYML,
                endpoint: process.env.RUNWAYML_API_ENDPOINT || 'https://api.runwayml.com/v1/generate',
                apiKey: process.env.RUNWAYML_API_KEY,
                priority: 1,
                rateLimitPerMinute: 100, // Example rate limit
                costPerRequest: 0.01, // Example cost
            },
            {
                name: creative_api_types_1.CreativeApiProvider.ADOBE_FIREFLY,
                endpoint: process.env.ADOBE_FIREFLY_API_ENDPOINT || 'https://api.adobe.com/firefly/v1/generate',
                apiKey: process.env.ADOBE_FIREFLY_API_KEY,
                priority: 2,
                rateLimitPerMinute: 50,
                costPerRequest: 0.02,
            },
        ].sort((a, b) => a.priority - b.priority); // Sort by priority
        this.localStableDiffusionEndpoint = process.env.LOCAL_STABLE_DIFFUSION_ENDPOINT || 'http://localhost:8080/api/v1/creative/local';
    }
    async generateCreative(request) {
        // Circuit Breaker logic
        if (this.circuitBreakerState === 'OPEN') {
            const now = Date.now();
            if (now - this.lastFailureTime > this.RESET_TIMEOUT_MS) {
                this.circuitBreakerState = 'HALF_OPEN';
                console.warn('Circuit breaker is HALF_OPEN. Allowing a single request to test service health.');
            }
            else {
                throw new Error('Circuit breaker is OPEN. Creative API is currently unavailable.');
            }
        }
        const MAX_RETRIES = parseInt(process.env.CREATIVE_API_MAX_RETRIES || '2', 10);
        const RETRY_DELAY_MS = parseInt(process.env.CREATIVE_API_RETRY_DELAY_MS || '2000', 10); // 2 seconds
        for (const provider of this.providers) {
            for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
                try {
                    console.log(`Attempting to generate creative using ${provider.name} (Attempt ${attempt + 1})...`);
                    const response = await this.httpClient.post(provider.endpoint, request, {
                        headers: {
                            'Authorization': `Bearer ${provider.apiKey}`,
                        },
                        timeout: 60000, // Use longer timeout for creative APIs
                    });
                    const creativeResponse = {
                        image_url: response.data.image_url || 'mock_image_url', // Adjust based on actual API response structure
                        model_used: provider.name,
                        cost_usd: provider.costPerRequest,
                        metadata: { provider: provider.name, attempt: attempt + 1 },
                    };
                    await this.auditTrailService.logEvent({
                        tenantId: request.metadata?.tenantId || 'N/A',
                        userId: request.metadata?.userId || 'N/A',
                        category: audit_types_1.AuditEventCategory.AI_OPERATION,
                        operationType: `CREATIVE_API_CALL_SUCCESS_${provider.name.toUpperCase()}`,
                        description: `Creative API call successful using ${provider.name}.`,
                        severity: audit_types_1.AuditEventSeverity.INFO,
                        timestamp: new Date(),
                        metadata: { provider: provider.name, attempt: attempt + 1, prompt: request.prompt, response: creativeResponse },
                    });
                    // If successful, reset circuit breaker
                    this.circuitBreakerState = 'CLOSED';
                    this.failureCount = 0;
                    return creativeResponse; // Success, return response
                }
                catch (error) {
                    const axiosError = error;
                    const isRateLimit = axiosError.response && axiosError.response.status === 429;
                    const isNetworkError = !axiosError.response;
                    this.failureCount++;
                    this.lastFailureTime = Date.now();
                    if (this.failureCount >= this.FAILURE_THRESHOLD) {
                        this.circuitBreakerState = 'OPEN';
                        console.error('Circuit breaker is OPEN due to too many failures.');
                    }
                    await this.auditTrailService.logEvent({
                        tenantId: request.metadata?.tenantId || 'N/A',
                        userId: request.metadata?.userId || 'N/A',
                        category: audit_types_1.AuditEventCategory.AI_OPERATION,
                        operationType: `CREATIVE_API_CALL_FAILED_${provider.name.toUpperCase()}`,
                        description: `Creative API call failed using ${provider.name}: ${error.message}.`,
                        severity: isRateLimit ? audit_types_1.AuditEventSeverity.MEDIUM : audit_types_1.AuditEventSeverity.HIGH,
                        timestamp: new Date(),
                        metadata: { provider: provider.name, attempt: attempt + 1, prompt: request.prompt, error: error.message, status: error.response?.status },
                    });
                    if ((isRateLimit || isNetworkError) && attempt < MAX_RETRIES) {
                        console.warn(`Retrying ${provider.name} in ${RETRY_DELAY_MS / 1000} seconds due to ${isRateLimit ? 'rate limit' : 'network error'}...`);
                        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
                    }
                    else {
                        console.error(`Failed to generate creative using ${provider.name} after ${attempt + 1} attempts.`);
                        break; // Break from inner loop to try next provider
                    }
                }
            }
        }
        // Fallback to local Stable Diffusion after all primary providers fail
        console.warn('All primary creative API providers failed. Falling back to local Stable Diffusion...');
        try {
            const fallbackResponse = await this.httpClient.post(this.localStableDiffusionEndpoint, request, {
                timeout: 30000, // Longer timeout for local fallback
            });
            const creativeResponse = {
                image_url: fallbackResponse.data.image_url || 'mock_fallback_image_url',
                model_used: creative_api_types_1.CreativeApiProvider.LOCAL_STABLE_DIFFUSION,
                cost_usd: 0, // Local model has no external cost
                metadata: { provider: creative_api_types_1.CreativeApiProvider.LOCAL_STABLE_DIFFUSION },
            };
            await this.auditTrailService.logEvent({
                tenantId: request.metadata?.tenantId || 'N/A',
                userId: request.metadata?.userId || 'N/A',
                category: audit_types_1.AuditEventCategory.AI_OPERATION,
                operationType: 'CREATIVE_API_FALLBACK_SUCCESS_LOCAL_SD',
                description: 'Creative API fallback to local Stable Diffusion successful.',
                severity: audit_types_1.AuditEventSeverity.INFO,
                timestamp: new Date(),
                metadata: { prompt: request.prompt, response: creativeResponse },
            });
            return creativeResponse;
        }
        catch (fallbackError) {
            console.error('Failed to generate creative using local Stable Diffusion fallback:', fallbackError);
            await this.auditTrailService.logEvent({
                tenantId: request.metadata?.tenantId || 'N/A',
                userId: request.metadata?.userId || 'N/A',
                category: audit_types_1.AuditEventCategory.AI_OPERATION,
                operationType: 'CREATIVE_API_FALLBACK_FAILED_LOCAL_SD',
                description: `Creative API fallback to local Stable Diffusion failed: ${fallbackError.message}.`,
                severity: audit_types_1.AuditEventSeverity.CRITICAL,
                timestamp: new Date(),
                metadata: { prompt: request.prompt, error: fallbackError.message },
            });
            throw new Error('Failed to generate creative after all retry and fallback attempts.');
        }
    }
    async checkProviderHealth() {
        const healthStatuses = [];
        for (const provider of this.providers) {
            try {
                // Attempt a lightweight call to check connectivity/authentication
                // This might be a specific health check endpoint provided by the API,
                // or a very small, cheap request. For now, a simple GET request.
                await this.httpClient.get(provider.endpoint, {
                    headers: { 'Authorization': `Bearer ${provider.apiKey}` },
                    timeout: 5000, // 5 seconds timeout for health check
                });
                healthStatuses.push({
                    name: provider.name,
                    status: 'healthy',
                    lastChecked: new Date(),
                });
            }
            catch (error) {
                healthStatuses.push({
                    name: provider.name,
                    status: 'unhealthy',
                    lastChecked: new Date(),
                    error: error.message,
                });
            }
        }
        // Check local Stable Diffusion endpoint
        try {
            await this.httpClient.get(this.localStableDiffusionEndpoint, { timeout: 5000 });
            healthStatuses.push({
                name: creative_api_types_1.CreativeApiProvider.LOCAL_STABLE_DIFFUSION,
                status: 'healthy',
                lastChecked: new Date(),
            });
        }
        catch (error) {
            healthStatuses.push({
                name: creative_api_types_1.CreativeApiProvider.LOCAL_STABLE_DIFFUSION,
                status: 'unhealthy',
                lastChecked: new Date(),
                error: error.message,
            });
        }
        return healthStatuses;
    }
    async pollRunwayApiQuota() {
        console.log('Polling RunwayML admin API for quota information...');
        // In a real scenario, this would make an actual API call to RunwayML's admin API
        // For now, return mock data.
        const mockQuota = {
            providerName: creative_api_types_1.CreativeApiProvider.RUNWAYML,
            totalOperations: 10000,
            remainingOperations: Math.floor(Math.random() * 10000), // Random remaining for testing
            resetTime: new Date(Date.now() + 3600000), // 1 hour from now
            lastPolled: new Date(),
        };
        // In a real system, you would expose this data as Prometheus metrics
        // Example:
        // prometheus.gauge('runway_remaining_operations').set(mockQuota.remainingOperations);
        // prometheus.gauge('runway_total_operations').set(mockQuota.totalOperations);
        await this.auditTrailService.logEvent({
            tenantId: 'SYSTEM', // Use 'SYSTEM' for system-level monitoring events
            category: audit_types_1.AuditEventCategory.MONITORING,
            operationType: 'RUNWAY_API_QUOTA_POLLED',
            description: `RunwayML API quota polled. Remaining: ${mockQuota.remainingOperations}/${mockQuota.totalOperations}.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: mockQuota,
        });
        return mockQuota;
    }
}
exports.CreativeApiService = CreativeApiService;
