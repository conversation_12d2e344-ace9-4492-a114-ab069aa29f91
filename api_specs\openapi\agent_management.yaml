openapi: 3.0.0
info:
  title: The AIgency & Agent Management API
  version: 1.0.0
  description: API endpoints for managing AI agents, their lifecycle, assignments, and performance.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Agent Lifecycle
    description: Management of AI agent creation, update, and deletion
  - name: Agent Assignment
    description: Assigning agents to tasks and users
  - name: Workflow Orchestration
    description: Managing agent workflows and execution
  - name: Agent Performance
    description: Monitoring and retrieving agent performance metrics

paths:
  /agents:
    get:
      summary: Get all AI agents
      operationId: getAllAgents
      tags:
        - Agent Lifecycle
      security:
        - bearerAuth: []
      parameters:
        - $ref: '../paim_management.yaml#/components/parameters/PageParam'
        - $ref: '../paim_management.yaml#/components/parameters/SizeParam'
        - $ref: '../paim_management.yaml#/components/parameters/SortParam'
        - in: query
          name: persona
          schema:
            type: string
          description: Filter agents by persona type.
        - in: query
          name: status
          schema:
            type: string
            enum: [active, inactive, busy, available]
          description: Filter agents by status.
      responses:
        '200':
          description: List of AI agents
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Agent'
                  pagination:
                    $ref: '../paim_management.yaml#/components/schemas/PaginationMetadata'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Create a new AI agent
      operationId: createAgent
      tags:
        - Agent Lifecycle
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAgentRequest'
      responses:
        '201':
          description: Agent created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /agents/{agentId}:
    get:
      summary: Get a specific AI agent by ID
      operationId: getAgentById
      tags:
        - Agent Lifecycle
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: agentId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the AI agent.
      responses:
        '200':
          description: Agent details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    put:
      summary: Update an existing AI agent
      operationId: updateAgent
      tags:
        - Agent Lifecycle
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: agentId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the AI agent to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAgentRequest'
      responses:
        '200':
          description: Agent updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    delete:
      summary: Delete an AI agent
      operationId: deleteAgent
      tags:
        - Agent Lifecycle
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: agentId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the AI agent to delete.
      responses:
        '204':
          description: Agent deleted successfully
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /agents/{agentId}/assign:
    post:
      summary: Assign an agent to a task or user
      operationId: assignAgent
      tags:
        - Agent Assignment
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: agentId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the agent to assign.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentAssignmentRequest'
      responses:
        '200':
          description: Agent assigned successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentAssignment'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /workflows/{workflowId}/execute:
    post:
      summary: Execute a specific workflow with agents
      operationId: executeWorkflow
      tags:
        - Workflow Orchestration
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: workflowId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the workflow to execute.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkflowExecutionRequest'
      responses:
        '200':
          description: Workflow execution initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowExecutionStatus'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /agents/{agentId}/performance:
    get:
      summary: Get performance metrics for a specific agent
      operationId: getAgentPerformance
      tags:
        - Agent Performance
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: agentId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the agent to retrieve performance metrics for.
        - in: query
          name: startDate
          schema:
            type: string
            format: date
          description: Start date for performance metrics (YYYY-MM-DD).
        - in: query
          name: endDate
          schema:
            type: string
            format: date
          description: End date for performance metrics (YYYY-MM-DD).
      responses:
        '200':
          description: Agent performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentPerformanceMetrics'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Agent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        name:
          type: string
          example: CustomerSupportAgent
        persona:
          type: string
          description: The AI agent persona (e.g., "Customer Support", "Sales", "Technical Writer").
          example: Customer Support
        description:
          type: string
          nullable: true
          example: AI agent specialized in handling customer inquiries.
        status:
          type: string
          enum: [active, inactive, busy, available]
          example: available
        paimInstanceId:
          type: string
          format: uuid
          description: The PAIM instance this agent belongs to.
        capabilities:
          type: array
          items:
            type: string
          description: List of capabilities or skills the agent possesses.
          example: ["natural language processing", "sentiment analysis"]
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    CreateAgentRequest:
      type: object
      required:
        - name
        - persona
        - paimInstanceId
      properties:
        name:
          type: string
          example: NewSalesAgent
        persona:
          type: string
          example: Sales
        description:
          type: string
          nullable: true
          example: New AI agent for sales lead qualification.
        paimInstanceId:
          type: string
          format: uuid
          description: The PAIM instance this agent will belong to.
        capabilities:
          type: array
          items:
            type: string
          example: ["lead qualification", "CRM integration"]

    UpdateAgentRequest:
      type: object
      properties:
        name:
          type: string
          example: UpdatedCustomerSupportAgent
        persona:
          type: string
          example: Customer Support
        description:
          type: string
          nullable: true
          example: Updated description for customer support agent.
        status:
          type: string
          enum: [active, inactive, busy, available]
          example: busy
        capabilities:
          type: array
          items:
            type: string
          example: ["natural language processing", "sentiment analysis", "troubleshooting"]

    AgentAssignmentRequest:
      type: object
      required:
        - assignedToType
        - assignedToId
        - assignmentType
      properties:
        assignedToType:
          type: string
          enum: [user, task, workflow]
          description: Type of entity the agent is being assigned to.
        assignedToId:
          type: string
          format: uuid
          description: ID of the user, task, or workflow.
        assignmentType:
          type: string
          enum: [primary, secondary, observer]
          description: Role of the agent in the assignment.
        notes:
          type: string
          nullable: true
          description: Any additional notes about the assignment.

    AgentAssignment:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: f1e2d3c4-b5a6-9876-5432-10fedcba9876
        agentId:
          type: string
          format: uuid
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        assignedToType:
          type: string
          enum: [user, task, workflow]
        assignedToId:
          type: string
          format: uuid
        assignmentType:
          type: string
          enum: [primary, secondary, observer]
        assignedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    WorkflowExecutionRequest:
      type: object
      required:
        - inputData
        - agentIds
      properties:
        inputData:
          type: object
          description: Input data for the workflow execution.
          example:
            customerQuery: "I need help with my account."
            priority: "high"
        agentIds:
          type: array
          items:
            type: string
            format: uuid
          description: List of agent IDs to involve in the workflow.
        callbackUrl:
          type: string
          format: uri
          nullable: true
          description: Optional URL for status updates or results.

    WorkflowExecutionStatus:
      type: object
      properties:
        executionId:
          type: string
          format: uuid
          example: g1h2i3j4-k5l6-7890-1234-567890abcdef
        workflowId:
          type: string
          format: uuid
          example: m1n2o3p4-q5r6-7890-1234-567890abcdef
        status:
          type: string
          enum: [pending, in_progress, completed, failed, cancelled]
          example: in_progress
        startTime:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        endTime:
          type: string
          format: date-time
          nullable: true
          example: '2023-10-27T10:05:00Z'
        outputData:
          type: object
          nullable: true
          description: Output data from the workflow execution.

    AgentPerformanceMetrics:
      type: object
      properties:
        agentId:
          type: string
          format: uuid
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        period:
          type: string
          description: The time period for the metrics (e.g., "daily", "weekly", "monthly").
          example: monthly
        totalTasksCompleted:
          type: integer
          example: 150
        averageResponseTimeMs:
          type: number
          format: float
          example: 250.75
        errorRate:
          type: number
          format: float
          description: Percentage of tasks that resulted in an error.
          example: 0.02
        customerSatisfactionScore:
          type: number
          format: float
          nullable: true
          description: Average customer satisfaction score (if applicable).
          example: 4.5
        resourceUtilization:
          type: object
          properties:
            cpuUsagePercent:
              type: number
              format: float
              example: 35.2
            memoryUsageMb:
              type: number
              format: float
              example: 1024.5
          description: Resource utilization metrics.
        metricsByPersona:
          type: object
          additionalProperties:
            type: object
            properties:
              tasksCompleted:
                type: integer
              avgResponseTime:
                type: number
          description: Performance metrics broken down by persona (if agent handles multiple).
          example:
            Sales:
              tasksCompleted: 50
              avgResponseTime: 300
            Marketing:
              tasksCompleted: 100
              avgResponseTime: 200
        lastUpdated:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'