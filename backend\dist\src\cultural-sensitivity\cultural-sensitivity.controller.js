"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CulturalSensitivityController = void 0;
const express = __importStar(require("express"));
const cultural_sensitivity_service_1 = require("./cultural-sensitivity.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const errors_1 = require("../utils/errors");
class CulturalSensitivityController {
    router;
    culturalSensitivityService;
    constructor(db) {
        this.router = express.Router();
        this.culturalSensitivityService = new cultural_sensitivity_service_1.CulturalSensitivityService(db);
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/localization/settings', (0, asyncHandler_1.asyncHandler)(this.getLocalizationSettings));
        this.router.put('/localization/settings', (0, asyncHandler_1.asyncHandler)(this.updateLocalizationSettings));
        this.router.get('/cultural-context', (0, asyncHandler_1.asyncHandler)(this.getCulturalContext));
        this.router.post('/cultural-context', (0, asyncHandler_1.asyncHandler)(this.createOrUpdateCulturalContext));
        this.router.post('/arabic-processing/dialect-detection', (0, asyncHandler_1.asyncHandler)(this.detectArabicDialect));
        this.router.post('/arabic-processing/adapt-content', (0, asyncHandler_1.asyncHandler)(this.adaptArabicContent));
    }
    getLocalizationSettings = async (req, res) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (entityType !== 'user' && entityType !== 'paim_instance') {
            throw new errors_1.CustomError('entityType must be "user" or "paim_instance".', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const settings = await this.culturalSensitivityService.getLocalizationSettings(entityId, entityType);
        res.status(200).json(settings);
    };
    updateLocalizationSettings = async (req, res) => {
        const { entityId, entityType, ...settings } = req.body;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required in the request body.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (entityType !== 'user' && entityType !== 'paim_instance') {
            throw new errors_1.CustomError('entityType must be "user" or "paim_instance".', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const updatedSettings = await this.culturalSensitivityService.updateLocalizationSettings(entityId, entityType, settings);
        res.status(200).json(updatedSettings);
    };
    getCulturalContext = async (req, res) => {
        const { locale, entityId, entityType } = req.query;
        if (!locale) {
            throw new errors_1.CustomError('locale is a required query parameter.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (entityType && entityType !== 'user' && entityType !== 'paim_instance') {
            throw new errors_1.CustomError('entityType must be "user" or "paim_instance" if provided.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const context = await this.culturalSensitivityService.getCulturalContext(locale, entityId, entityType);
        res.status(200).json(context);
    };
    createOrUpdateCulturalContext = async (req, res) => {
        const { locale, ...contextData } = req.body;
        const { entityId, entityType } = req.query; // Optional entityId/Type from query for context
        if (!locale) {
            throw new errors_1.CustomError('locale is required in the request body.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (entityType && entityType !== 'user' && entityType !== 'paim_instance') {
            throw new errors_1.CustomError('entityType must be "user" or "paim_instance" if provided.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const createdOrUpdatedContext = await this.culturalSensitivityService.createOrUpdateCulturalContext({ locale, ...contextData }, entityId, entityType);
        res.status(200).json(createdOrUpdatedContext);
    };
    detectArabicDialect = async (req, res) => {
        const { text } = req.body;
        if (!text) {
            throw new errors_1.CustomError('text is required in the request body.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const result = await this.culturalSensitivityService.detectArabicDialect(text);
        res.status(200).json(result);
    };
    adaptArabicContent = async (req, res) => {
        const { text, targetDialect } = req.body;
        if (!text || !targetDialect) {
            throw new errors_1.CustomError('text and targetDialect are required in the request body.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const result = await this.culturalSensitivityService.adaptArabicContent(text, targetDialect);
        res.status(200).json(result);
    };
}
exports.CulturalSensitivityController = CulturalSensitivityController;
