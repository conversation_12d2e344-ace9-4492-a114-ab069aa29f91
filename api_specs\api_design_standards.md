# API Design Standards for The AIgency and PAIM Backend Services

This document outlines the design standards for all APIs developed for The AIgency and PAIM backend services. Adherence to these standards ensures consistency, maintainability, security, and a superior developer experience.

## 1. General Principles

*   **Consistency:** All APIs should follow a consistent design philosophy, naming conventions, and response structures.
*   **Predictability:** API behavior should be predictable and well-documented, allowing developers to anticipate responses and errors.
*   **Usability:** APIs should be easy to understand and use, with clear documentation and intuitive resource modeling.
*   **Scalability:** Designs should consider future growth and high-load scenarios.
*   **Security:** APIs must be designed with security as a paramount concern, incorporating authentication, authorization, and data protection.
*   **Observability:** APIs should provide sufficient information for monitoring, logging, and tracing.

## 2. OpenAPI 3.0 Specifications

All RESTful APIs MUST be documented using OpenAPI 3.0.

*   **Structure:** Each major API category (e.g., Authentication, PAIM Management) will have its own OpenAPI YAML file.
*   **Detailed Documentation:**
    *   Clear summaries and descriptions for all endpoints, parameters, and responses.
    *   Examples for request and response bodies.
    *   Detailed descriptions of error responses.
*   **Tags:** Use tags to group related operations.
*   **Security Schemes:** Define security schemes (e.g., JWT <PERSON>) and apply them to relevant operations.

## 3. Naming Conventions

*   **Resources:** Use plural nouns for resource collections (e.g., `/users`, `/agents`).
*   **Resource Identifiers:** Use `id` for primary identifiers (e.g., `/users/{id}`).
*   **Actions/Operations:** Use verbs for custom actions that are not standard CRUD operations (e.g., `/users/{id}/activate`).
*   **Parameters:** Use `camelCase` for query parameters and `snake_case` for path and body parameters where appropriate (e.g., `user_id`).
*   **JSON Fields:** Use `camelCase` for JSON field names in request and response bodies.
*   **Enums:** Use `UPPER_SNAKE_CASE` for enum values.

## 4. Request and Response Formats

*   **JSON:** All request and response bodies MUST use JSON (`application/json`).
*   **Dates and Times:** Use ISO 8601 format for all date and time values (e.g., `2023-10-27T10:00:00Z`).
*   **Pagination:**
    *   Use `page` and `size` query parameters for pagination (e.g., `/agents?page=1&size=10`).
    *   Responses should include pagination metadata (e.g., `total_elements`, `total_pages`, `current_page`, `page_size`).
*   **Filtering:** Use query parameters for filtering (e.g., `/agents?status=active`).
*   **Sorting:** Use `sort` query parameter with comma-separated fields and optional direction (e.g., `/agents?sort=name,asc`).

## 5. Error Handling and Status Codes

*   **Standard HTTP Status Codes:** Use appropriate HTTP status codes to indicate the outcome of an API request.
    *   `2xx` (Success): `200 OK`, `201 Created`, `204 No Content`.
    *   `4xx` (Client Error): `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found`, `409 Conflict`, `429 Too Many Requests`.
    *   `5xx` (Server Error): `500 Internal Server Error`, `503 Service Unavailable`.
*   **Consistent Error Response Format:** All error responses MUST follow a consistent JSON structure:

    ```json
    {
      "timestamp": "2023-10-27T10:00:00Z",
      "status": 400,
      "error": "Bad Request",
      "message": "Detailed error message explaining the issue.",
      "path": "/api/v1/users",
      "details": [
        {
          "field": "email",
          "code": "invalid_format",
          "message": "Invalid email format."
        }
      ]
    }
    ```
    *   `timestamp`: When the error occurred.
    *   `status`: HTTP status code.
    *   `error`: Short, human-readable error description.
    *   `message`: Detailed, developer-friendly error message.
    *   `path`: The requested API path.
    *   `details`: (Optional) Array of specific validation errors or additional context.

## 6. Rate Limiting and Throttling

*   **Headers:** Implement `X-RateLimit-Limit`, `X-RateLimit-Remaining`, and `X-RateLimit-Reset` headers in responses.
*   **`429 Too Many Requests`:** Return this status code when rate limits are exceeded.

## 7. Versioning Strategy

*   **URI Versioning:** Use URI versioning (e.g., `/api/v1/users`).
*   **Backward Compatibility:** Strive for backward compatibility. Breaking changes should be introduced only with major version increments and clearly communicated.

## 8. Request/Response Validation

*   **Schema Validation:** All incoming requests and outgoing responses MUST be validated against their defined OpenAPI schemas.
*   **Input Sanitization:** All user inputs MUST be sanitized to prevent injection attacks.

## 9. Authentication and Authorization

*   **JWT-based Authentication:** Use JSON Web Tokens (JWT) for authentication.
*   **Bearer Token:** Tokens should be passed in the `Authorization` header as `Bearer <token>`.
*   **Role-Based Access Control (RBAC):** Implement RBAC to control access to resources based on user roles (System Admin, Company Admin, Power User, Personal).
*   **Multi-tenant Security:** Enforce strict multi-tenant isolation with data boundaries.

## 10. GraphQL Schema Design

*   **Single Endpoint:** A single GraphQL endpoint (e.g., `/graphql`).
*   **Strongly Typed:** All types, fields, and arguments MUST be explicitly typed.
*   **Clear Naming:** Use clear, descriptive names for types, fields, and arguments.
*   **Pagination:** Implement Relay-style pagination using connections and `pageInfo`.
*   **Error Handling:** Return errors in the `errors` field of the GraphQL response.

## 11. WebSocket Event Specifications

*   **Event-driven:** Define clear event names and payload structures.
*   **JSON Format:** All WebSocket messages MUST use JSON.
*   **Message Types:** Define distinct message types for different events (e.g., `notification`, `collaboration_update`).
*   **Authentication:** Secure WebSocket connections using initial authentication (e.g., JWT handshake).

## 12. Advanced API Features

*   **Bulk Operations:** Provide endpoints for bulk creation, update, or deletion where appropriate.
*   **File Uploads:** Use `multipart/form-data` for file uploads.
*   **Caching:** Utilize appropriate HTTP caching headers (e.g., `Cache-Control`, `ETag`, `Last-Modified`).
*   **Search:** Implement robust search capabilities with relevant query parameters.

## 13. Cultural Sensitivity and Localization

*   **`Accept-Language` Header:** Support the `Accept-Language` header for content negotiation.
*   **Locale Parameter:** Allow `locale` as a query parameter or header for explicit language selection.
*   **Arabic-first:** Ensure all localization mechanisms support Arabic as a primary language, including dialect detection.

## 14. PowerOps and Gamification

*   **Real-time Tracking:** Design endpoints for real-time tracking of PowerOps usage, XP, and achievements.
*   **Webhooks/Callbacks:** Consider webhooks for notifying external systems of significant gamification events.

## 15. System Administration

*   **Restricted Access:** System administration endpoints MUST be highly secured and accessible only to System Admins.
*   **Auditability:** All system administration actions MUST be logged for auditing purposes.