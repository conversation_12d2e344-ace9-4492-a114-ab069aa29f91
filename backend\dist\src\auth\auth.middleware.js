"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.enforceTenantContext = exports.authorizeRoles = exports.authorizePaimTier = exports.authenticate = void 0;
const db_1 = require("../types/db"); // Import PaimTierEnum from db types
const node_appwrite_1 = require("node-appwrite"); // Import AppwriteException
const appwrite_adapter_1 = require("../database/adapters/appwrite-adapter"); // Import AppwriteAdapter
const logger_1 = __importDefault(require("../config/logger")); // Import logger
const errors_1 = require("../utils/errors"); // Import custom error classes
/**
 * Middleware to authenticate requests using JWT.
 * Attaches decoded user payload and tenantId to the request object.
 */
const appwriteAdapter = new appwrite_adapter_1.AppwriteAdapter();
const authenticate = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    const ipAddress = req.ip;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger_1.default.warn('Authentication failed: No token provided or invalid format.', { ipAddress });
        return next(new errors_1.AuthenticationError('No token provided or invalid token format.'));
    }
    const token = authHeader.split(' ')[1];
    try {
        const user = await appwriteAdapter.getCurrentUser();
        if (!user) {
            logger_1.default.warn('Authentication failed: Invalid or expired token.', { ipAddress, token });
            return next(new errors_1.AuthenticationError('Invalid or expired token.'));
        }
        // Map Appwrite user to JwtPayload
        req.user = {
            userId: user.$id,
            email: user.email || '',
            tenantId: user.prefs?.tenant_id || '', // Assuming tenant_id is in user.prefs
            paimTier: user.prefs?.paim_tier || '', // Assuming paim_tier is in user.prefs
            roles: user.prefs?.roles || [], // Assuming roles are in user.prefs
            iat: user.accessedAt ? Math.floor(new Date(user.accessedAt).getTime() / 1000) : 0, // Use accessedAt for iat
            exp: user.expire ? Math.floor(new Date(user.expire).getTime() / 1000) : 0, // Use expire for exp
        };
        req.tenantId = req.user.tenantId;
        logger_1.default.info(`User authenticated: ${user.email}`, { userId: user.$id, tenantId: req.tenantId, ipAddress });
        next();
    }
    catch (error) {
        if (error instanceof node_appwrite_1.AppwriteException && (error.code === 401 || error.code === 403)) {
            logger_1.default.warn('Appwrite authentication error: Invalid or expired token.', { ipAddress, token, error: error.message });
            return next(new errors_1.AuthenticationError('Invalid or expired token.'));
        }
        logger_1.default.error('Authentication failed due to server error.', { ipAddress, token, error });
        return next(new errors_1.AuthenticationError('Authentication failed due to server error.'));
    }
};
exports.authenticate = authenticate;
/**
 * Middleware to enforce PAIM tier-based authorization.
 * Requires the authenticate middleware to be run first.
 */
const authorizePaimTier = (requiredTier) => {
    return (req, res, next) => {
        const ipAddress = req.ip;
        const userId = req.user?.userId;
        if (!req.user) {
            logger_1.default.warn('Authorization failed (PAIM Tier): Authentication required.', { ipAddress });
            return next(new errors_1.AuthenticationError('Authentication required for authorization.'));
        }
        const userTier = req.user.paimTier;
        const paimTierHierarchy = {
            [db_1.PaimTierEnum.Basic]: 4,
            [db_1.PaimTierEnum.Professional]: 3,
            [db_1.PaimTierEnum.Enterprise]: 2,
            [db_1.PaimTierEnum.Custom]: 1, // Custom is typically the highest or most flexible
        };
        // Check if user's tier level is numerically less than or equal to the required tier level
        // (lower number means higher tier in this hierarchy)
        if (paimTierHierarchy[userTier] > paimTierHierarchy[requiredTier]) {
            logger_1.default.warn(`Authorization failed (PAIM Tier): Insufficient tier. User: ${userId}, Tier: ${userTier}, Required: ${requiredTier}`, { ipAddress });
            return next(new errors_1.AuthorizationError('Forbidden: Insufficient PAIM tier.'));
        }
        logger_1.default.info(`PAIM Tier authorized: User ${userId} with tier ${userTier} for required tier ${requiredTier}`, { ipAddress });
        next();
    };
};
exports.authorizePaimTier = authorizePaimTier;
/**
 * Middleware to enforce role-based authorization.
 * Requires the authenticate middleware to be run first.
 */
const authorizeRoles = (requiredRoles) => {
    return (req, res, next) => {
        const ipAddress = req.ip;
        const userId = req.user?.userId;
        if (!req.user) {
            logger_1.default.warn('Authorization failed (Roles): Authentication required.', { ipAddress });
            return next(new errors_1.AuthenticationError('Authentication required for authorization.'));
        }
        const userRoles = req.user.roles;
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
        if (!hasRequiredRole) {
            logger_1.default.warn(`Authorization failed (Roles): Insufficient roles. User: ${userId}, Roles: ${userRoles}, Required: ${requiredRoles}`, { ipAddress });
            return next(new errors_1.AuthorizationError('Forbidden: Insufficient roles.'));
        }
        logger_1.default.info(`Roles authorized: User ${userId} with roles ${userRoles} for required roles ${requiredRoles}`, { ipAddress });
        next();
    };
};
exports.authorizeRoles = authorizeRoles;
/**
 * Middleware to enforce tenant context for multi-tenant isolation.
 * Ensures that a user can only access resources within their assigned tenant.
 * Requires the authenticate middleware to be run first.
 */
const enforceTenantContext = (req, res, next) => {
    const ipAddress = req.ip;
    const userId = req.user?.userId;
    const userTenantId = req.user?.tenantId;
    if (!req.user || !userTenantId) {
        logger_1.default.warn('Tenant context enforcement failed: Authentication and tenant context required.', { ipAddress });
        return next(new errors_1.AuthenticationError('Authentication and tenant context required.'));
    }
    const requestedTenantId = req.params.tenantId || req.query.tenantId;
    // Custom tier can bypass tenant context for cross-tenant operations (assuming Custom is highest/admin-like)
    if (req.user.paimTier === db_1.PaimTierEnum.Custom) {
        logger_1.default.info(`Tenant context bypassed for Custom tier user: ${userId}`, { ipAddress });
        return next();
    }
    if (requestedTenantId && requestedTenantId !== userTenantId) {
        logger_1.default.warn(`Tenant context enforcement failed: Access to other tenant data not allowed. User: ${userId}, User Tenant: ${userTenantId}, Requested Tenant: ${requestedTenantId}`, { ipAddress });
        return next(new errors_1.AuthorizationError('Forbidden: Access to other tenant data is not allowed.'));
    }
    logger_1.default.info(`Tenant context enforced: User ${userId} in tenant ${userTenantId}`, { ipAddress });
    next();
};
exports.enforceTenantContext = enforceTenantContext;
