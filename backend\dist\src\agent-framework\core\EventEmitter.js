"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventEmitter = void 0;
const logger_1 = __importDefault(require("../../config/logger"));
class EventEmitter {
    events = new Map();
    maxListeners = 10;
    /**
     * Register an event listener
     */
    on(event, callback) {
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        const listeners = this.events.get(event);
        // Check max listeners
        if (listeners.length >= this.maxListeners) {
            logger_1.default.warn(`Maximum listeners (${this.maxListeners}) exceeded for event '${event}'`);
        }
        listeners.push({ callback, once: false });
        logger_1.default.debug(`Event listener added for '${event}'`);
        return this;
    }
    /**
     * Register a one-time event listener
     */
    once(event, callback) {
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        const listeners = this.events.get(event);
        listeners.push({ callback, once: true });
        logger_1.default.debug(`One-time event listener added for '${event}'`);
        return this;
    }
    /**
     * Remove an event listener
     */
    off(event, callback) {
        if (!this.events.has(event)) {
            return this;
        }
        const listeners = this.events.get(event);
        if (!callback) {
            // Remove all listeners for this event
            this.events.delete(event);
            logger_1.default.debug(`All listeners removed for event '${event}'`);
        }
        else {
            // Remove specific listener
            const index = listeners.findIndex(listener => listener.callback === callback);
            if (index !== -1) {
                listeners.splice(index, 1);
                logger_1.default.debug(`Specific listener removed for event '${event}'`);
                // Clean up empty event arrays
                if (listeners.length === 0) {
                    this.events.delete(event);
                }
            }
        }
        return this;
    }
    /**
     * Emit an event
     */
    emit(event, ...args) {
        if (!this.events.has(event)) {
            logger_1.default.debug(`No listeners for event '${event}'`);
            return false;
        }
        const listeners = this.events.get(event);
        const listenersToRemove = [];
        logger_1.default.debug(`Emitting event '${event}' to ${listeners.length} listeners`);
        // Execute all listeners
        listeners.forEach((listener, index) => {
            try {
                listener.callback(...args);
                // Mark one-time listeners for removal
                if (listener.once) {
                    listenersToRemove.push(index);
                }
            }
            catch (error) {
                logger_1.default.error(`Error in event listener for '${event}':`, error);
            }
        });
        // Remove one-time listeners (in reverse order to maintain indices)
        listenersToRemove.reverse().forEach(index => {
            listeners.splice(index, 1);
        });
        // Clean up empty event arrays
        if (listeners.length === 0) {
            this.events.delete(event);
        }
        return true;
    }
    /**
     * Get all event names
     */
    eventNames() {
        return Array.from(this.events.keys());
    }
    /**
     * Get listener count for an event
     */
    listenerCount(event) {
        const listeners = this.events.get(event);
        return listeners ? listeners.length : 0;
    }
    /**
     * Get all listeners for an event
     */
    listeners(event) {
        const listeners = this.events.get(event);
        return listeners ? listeners.map(l => l.callback) : [];
    }
    /**
     * Remove all listeners
     */
    removeAllListeners(event) {
        if (event) {
            this.events.delete(event);
            logger_1.default.debug(`All listeners removed for event '${event}'`);
        }
        else {
            this.events.clear();
            logger_1.default.debug('All event listeners removed');
        }
        return this;
    }
    /**
     * Set maximum number of listeners per event
     */
    setMaxListeners(n) {
        if (n < 0) {
            throw new Error('Maximum listeners count must be non-negative');
        }
        this.maxListeners = n;
        logger_1.default.debug(`Maximum listeners set to ${n}`);
        return this;
    }
    /**
     * Get maximum number of listeners per event
     */
    getMaxListeners() {
        return this.maxListeners;
    }
    /**
     * Prepend a listener to the beginning of the listeners array
     */
    prependListener(event, callback) {
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        const listeners = this.events.get(event);
        listeners.unshift({ callback, once: false });
        logger_1.default.debug(`Event listener prepended for '${event}'`);
        return this;
    }
    /**
     * Prepend a one-time listener to the beginning of the listeners array
     */
    prependOnceListener(event, callback) {
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        const listeners = this.events.get(event);
        listeners.unshift({ callback, once: true });
        logger_1.default.debug(`One-time event listener prepended for '${event}'`);
        return this;
    }
    /**
     * Get raw listeners (including once flag)
     */
    rawListeners(event) {
        const listeners = this.events.get(event);
        return listeners ? [...listeners] : [];
    }
}
exports.EventEmitter = EventEmitter;
