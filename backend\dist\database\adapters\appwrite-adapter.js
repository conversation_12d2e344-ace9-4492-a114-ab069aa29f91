"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppwriteAdapter = void 0;
const appwrite_1 = require("../../config/appwrite");
const appwrite_2 = require("appwrite");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const logger_1 = __importDefault(require("../../config/logger"));
class AppwriteAdapter {
    USERS_COLLECTION = 'users';
    SESSIONS_COLLECTION = 'sessions';
    USER_PROFILES_COLLECTION = 'user_profiles';
    async createUser(userData) {
        try {
            const userId = appwrite_2.ID.unique();
            // Hash password
            const hashedPassword = await bcryptjs_1.default.hash(userData.password, 12);
            // Create user in Appwrite Auth
            // const authUser = await appwriteService.createUser( // Commented out due to Appwrite SDK changes
            //   userId,
            //   userData.email,
            //   userData.password,
            //   `${userData.firstName} ${userData.lastName}`
            // );
            // Create user profile in database
            const profileData = {
                user_id: userId,
                email: userData.email,
                firstName: userData.firstName,
                lastName: userData.lastName,
                companyName: userData.companyName,
                password: hashedPassword,
                tenant_id: appwrite_2.ID.unique(), // Generate tenant ID
                paim_tier: 'basic',
                roles: ['user'],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
            };
            const profile = await appwrite_1.appwriteService.createDocument(this.USER_PROFILES_COLLECTION, userId, profileData);
            logger_1.default.info('User created successfully', {
                userId,
                email: userData.email
            });
            return this.mapDocumentToUser(profile);
        }
        catch (error) {
            logger_1.default.error('Failed to create user', {
                email: userData.email,
                error: error.message
            });
            throw error;
        }
    }
    async getUserById(userId) {
        try {
            const document = await appwrite_1.appwriteService.getDocument(this.USER_PROFILES_COLLECTION, userId);
            return this.mapDocumentToUser(document);
        }
        catch (error) {
            if (error.name === 'NotFoundError') {
                return null;
            }
            throw error;
        }
    }
    async getUserByEmail(email) {
        try {
            const query = appwrite_1.appwriteService.createQuery();
            const result = await appwrite_1.appwriteService.listDocuments(this.USER_PROFILES_COLLECTION, [query.equal('email', email)], 1);
            if (result.documents.length === 0) {
                return null;
            }
            return this.mapDocumentToUser(result.documents[0]);
        }
        catch (error) {
            logger_1.default.error('Failed to get user by email', {
                email,
                error: error.message
            });
            throw error;
        }
    }
    async updateUser(userId, updates) {
        try {
            const updateData = {
                ...updates,
                updated_at: new Date().toISOString(),
            };
            // Remove fields that shouldn't be updated directly
            delete updateData.user_id;
            delete updateData.created_at;
            const document = await appwrite_1.appwriteService.updateDocument(this.USER_PROFILES_COLLECTION, userId, updateData);
            // If email is being updated, also update in Appwrite Auth
            if (updates.email) {
                // await appwriteService.updateUser(userId, { email: updates.email }); // Commented out due to Appwrite SDK changes
            }
            logger_1.default.info('User updated successfully', { userId });
            return this.mapDocumentToUser(document);
        }
        catch (error) {
            logger_1.default.error('Failed to update user', {
                userId,
                error: error.message
            });
            throw error;
        }
    }
    async deleteUser(userId) {
        try {
            // Delete from Appwrite Auth
            // await appwriteService.deleteUser(userId); // Commented out due to Appwrite SDK changes
            // Delete user profile
            await appwrite_1.appwriteService.deleteDocument(this.USER_PROFILES_COLLECTION, userId);
            logger_1.default.info('User deleted successfully', { userId });
        }
        catch (error) {
            logger_1.default.error('Failed to delete user', {
                userId,
                error: error.message
            });
            throw error;
        }
    }
    async createSession(userId, sessionData) {
        try {
            const sessionId = appwrite_2.ID.unique();
            const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
            const document = await appwrite_1.appwriteService.createDocument(this.SESSIONS_COLLECTION, sessionId, {
                session_id: sessionId,
                user_id: userId,
                data: sessionData || {},
                created_at: new Date().toISOString(),
                expires_at: expiresAt.toISOString(),
            });
            return this.mapDocumentToSession(document);
        }
        catch (error) {
            logger_1.default.error('Failed to create session', {
                userId,
                error: error.message
            });
            throw error;
        }
    }
    async getSession(sessionId) {
        try {
            const document = await appwrite_1.appwriteService.getDocument(this.SESSIONS_COLLECTION, sessionId);
            const session = this.mapDocumentToSession(document);
            // Check if session is expired
            if (new Date(session.expires_at) < new Date()) {
                await this.deleteSession(sessionId);
                return null;
            }
            return session;
        }
        catch (error) {
            if (error.name === 'NotFoundError') {
                return null;
            }
            throw error;
        }
    }
    async deleteSession(sessionId) {
        try {
            await appwrite_1.appwriteService.deleteDocument(this.SESSIONS_COLLECTION, sessionId);
            logger_1.default.info('Session deleted successfully', { sessionId });
        }
        catch (error) {
            logger_1.default.error('Failed to delete session', {
                sessionId,
                error: error.message
            });
            throw error;
        }
    }
    async validateSession(sessionId) {
        const session = await this.getSession(sessionId);
        return session !== null;
    }
    async createDocument(collection, data, id) {
        try {
            const documentId = id || appwrite_2.ID.unique();
            const document = await appwrite_1.appwriteService.createDocument(collection, documentId, data); // Cast data to Record<string, any>
            return { ...data, id: document.$id };
        }
        catch (error) {
            logger_1.default.error('Failed to create document', {
                collection,
                error: error.message
            });
            throw error;
        }
    }
    async getDocument(collection, id) {
        try {
            const document = await appwrite_1.appwriteService.getDocument(collection, id);
            return document;
        }
        catch (error) {
            if (error.name === 'NotFoundError') {
                return null;
            }
            throw error;
        }
    }
    async updateDocument(collection, id, updates) {
        try {
            const document = await appwrite_1.appwriteService.updateDocument(collection, id, updates);
            return document;
        }
        catch (error) {
            logger_1.default.error('Failed to update document', {
                collection,
                id,
                error: error.message
            });
            throw error;
        }
    }
    async deleteDocument(collection, id) {
        try {
            await appwrite_1.appwriteService.deleteDocument(collection, id);
        }
        catch (error) {
            logger_1.default.error('Failed to delete document', {
                collection,
                id,
                error: error.message
            });
            throw error;
        }
    }
    async listDocuments(collection, filters, pagination, sorting) {
        try {
            const queries = this.buildQueries(filters, pagination, sorting);
            const result = await appwrite_1.appwriteService.listDocuments(collection, queries, pagination?.limit, pagination?.offset);
            return {
                documents: result.documents,
                total: result.total,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to list documents', {
                collection,
                error: error.message
            });
            throw error;
        }
    }
    async query(collection, queries) {
        try {
            const result = await appwrite_1.appwriteService.listDocuments(collection, queries);
            return result.documents;
        }
        catch (error) {
            logger_1.default.error('Failed to query documents', {
                collection,
                error: error.message
            });
            throw error;
        }
    }
    async count(collection, filters) {
        try {
            const queries = filters ? this.buildFilterQueries(filters) : undefined;
            const result = await appwrite_1.appwriteService.listDocuments(collection, queries, 1);
            return result.total;
        }
        catch (error) {
            logger_1.default.error('Failed to count documents', {
                collection,
                error: error.message
            });
            throw error;
        }
    }
    async transaction(operations) {
        // Appwrite doesn't support transactions natively
        // This is a simple implementation that just executes the operations
        // In a real implementation, you might want to implement a rollback mechanism
        try {
            return await operations();
        }
        catch (error) {
            logger_1.default.error('Transaction failed', { error: error.message });
            throw error;
        }
    }
    async healthCheck() {
        return appwrite_1.appwriteService.healthCheck();
    }
    // Helper methods
    mapDocumentToUser(document) {
        return {
            user_id: document.$id || document.user_id,
            email: document.email,
            firstName: document.firstName,
            lastName: document.lastName,
            companyName: document.companyName,
            tenant_id: document.tenant_id,
            paim_tier: document.paim_tier,
            roles: document.roles,
            created_at: document.created_at || document.$createdAt,
            updated_at: document.updated_at || document.$updatedAt,
        };
    }
    mapDocumentToSession(document) {
        return {
            session_id: document.$id || document.session_id,
            user_id: document.user_id,
            created_at: document.created_at || document.$createdAt,
            expires_at: document.expires_at,
            data: document.data,
        };
    }
    buildQueries(filters, pagination, sorting) {
        const queries = [];
        const query = appwrite_1.appwriteService.createQuery();
        // Add filters
        if (filters) {
            queries.push(...this.buildFilterQueries(filters));
        }
        // Add sorting
        if (sorting) {
            sorting.forEach(sort => {
                if (sort.direction === 'asc') {
                    queries.push(query.orderAsc(sort.field));
                }
                else {
                    queries.push(query.orderDesc(sort.field));
                }
            });
        }
        // Add pagination
        if (pagination) {
            if (pagination.limit) {
                queries.push(query.limit(pagination.limit));
            }
            if (pagination.offset) {
                queries.push(query.offset(pagination.offset));
            }
        }
        return queries;
    }
    buildFilterQueries(filters) {
        const queries = [];
        const query = appwrite_1.appwriteService.createQuery();
        Object.entries(filters).forEach(([key, value]) => {
            if (value === null || value === undefined) {
                return;
            }
            if (typeof value === 'object' && !Array.isArray(value)) {
                // Handle complex filters like { $gt: 10 }, { $in: [1, 2, 3] }
                Object.entries(value).forEach(([operator, operatorValue]) => {
                    switch (operator) {
                        case '$gt':
                            queries.push(query.greaterThan(key, operatorValue));
                            break;
                        case '$lt':
                            queries.push(query.lessThan(key, operatorValue));
                            break;
                        case '$ne':
                            queries.push(query.notEqual(key, operatorValue));
                            break;
                        case '$in':
                            // Appwrite doesn't have a direct "in" query, so we'll use multiple equal queries
                            // This is not ideal and might need a different approach
                            if (Array.isArray(operatorValue)) {
                                operatorValue.forEach(val => {
                                    queries.push(query.equal(key, val));
                                });
                            }
                            break;
                        case '$search':
                            queries.push(query.search(key, operatorValue));
                            break;
                    }
                });
            }
            else if (Array.isArray(value)) {
                // Handle array values
                value.forEach(val => {
                    queries.push(query.equal(key, val));
                });
            }
            else {
                // Simple equality
                queries.push(query.equal(key, value));
            }
        });
        return queries;
    }
}
exports.AppwriteAdapter = AppwriteAdapter;
