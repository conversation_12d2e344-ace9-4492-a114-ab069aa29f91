import logger from '../../config/logger';
import <PERSON><PERSON> from 'joi';

export interface ConfigSchema {
  [key: string]: any;
}

export interface ConfigValidationOptions {
  allowUnknown?: boolean;
  stripUnknown?: boolean;
  abortEarly?: boolean;
}

export class Config {
  private data: Map<string, any> = new Map();
  private schema?: Joi.ObjectSchema;
  private validationOptions: ConfigValidationOptions = {
    allowUnknown: false,
    stripUnknown: true,
    abortEarly: false
  };

  constructor(initialConfig?: ConfigSchema, schema?: Joi.ObjectSchema) {
    if (initialConfig) {
      this.load(initialConfig);
    }
    
    if (schema) {
      this.schema = schema;
    }

    logger.debug('Config instance created');
  }

  /**
   * Set a configuration value
   */
  public set(key: string, value: any): this {
    if (!key || typeof key !== 'string') {
      throw new Error('Configuration key must be a non-empty string');
    }

    // Support nested keys using dot notation
    const keys = key.split('.');
    if (keys.length === 1) {
      this.data.set(key, value);
    } else {
      // Handle nested configuration
      const rootKey = keys[0];
      let current = this.data.get(rootKey) || {};
      
      // Navigate to the parent of the target key
      let target = current;
      for (let i = 1; i < keys.length - 1; i++) {
        if (!target[keys[i]]) {
          target[keys[i]] = {};
        }
        target = target[keys[i]];
      }
      
      // Set the final value
      target[keys[keys.length - 1]] = value;
      this.data.set(rootKey, current);
    }

    logger.debug(`Configuration set: ${key} = ${JSON.stringify(value)}`);
    return this;
  }

  /**
   * Get a configuration value
   */
  public get<T = any>(key: string, defaultValue?: T): T | undefined {
    if (!key || typeof key !== 'string') {
      throw new Error('Configuration key must be a non-empty string');
    }

    // Support nested keys using dot notation
    const keys = key.split('.');
    if (keys.length === 1) {
      const value = this.data.get(key);
      return value !== undefined ? value : defaultValue;
    } else {
      // Handle nested configuration
      const rootKey = keys[0];
      let current = this.data.get(rootKey);
      
      if (!current) {
        return defaultValue;
      }
      
      // Navigate through the nested structure
      for (let i = 1; i < keys.length; i++) {
        if (current && typeof current === 'object' && keys[i] in current) {
          current = current[keys[i]];
        } else {
          return defaultValue;
        }
      }
      
      return current !== undefined ? current : defaultValue;
    }
  }

  /**
   * Check if a configuration key exists
   */
  public has(key: string): boolean {
    if (!key || typeof key !== 'string') {
      return false;
    }

    const keys = key.split('.');
    if (keys.length === 1) {
      return this.data.has(key);
    } else {
      const rootKey = keys[0];
      let current = this.data.get(rootKey);
      
      if (!current) {
        return false;
      }
      
      for (let i = 1; i < keys.length; i++) {
        if (current && typeof current === 'object' && keys[i] in current) {
          current = current[keys[i]];
        } else {
          return false;
        }
      }
      
      return true;
    }
  }

  /**
   * Delete a configuration key
   */
  public delete(key: string): boolean {
    if (!key || typeof key !== 'string') {
      return false;
    }

    const keys = key.split('.');
    if (keys.length === 1) {
      const deleted = this.data.delete(key);
      if (deleted) {
        logger.debug(`Configuration deleted: ${key}`);
      }
      return deleted;
    } else {
      const rootKey = keys[0];
      let current = this.data.get(rootKey);
      
      if (!current) {
        return false;
      }
      
      // Navigate to the parent of the target key
      let target = current;
      for (let i = 1; i < keys.length - 1; i++) {
        if (target && typeof target === 'object' && keys[i] in target) {
          target = target[keys[i]];
        } else {
          return false;
        }
      }
      
      // Delete the final key
      const finalKey = keys[keys.length - 1];
      if (target && typeof target === 'object' && finalKey in target) {
        delete target[finalKey];
        logger.debug(`Configuration deleted: ${key}`);
        return true;
      }
      
      return false;
    }
  }

  /**
   * Load configuration from an object
   */
  public load(config: ConfigSchema): this {
    if (!config || typeof config !== 'object') {
      throw new Error('Configuration must be an object');
    }

    // Validate if schema is provided
    if (this.schema) {
      this.validate(config);
    }

    // Clear existing configuration
    this.data.clear();

    // Load new configuration
    Object.entries(config).forEach(([key, value]) => {
      this.data.set(key, value);
    });

    logger.info(`Configuration loaded with ${Object.keys(config).length} keys`);
    return this;
  }

  /**
   * Get all configuration as a plain object
   */
  public getAll(): ConfigSchema {
    const result: ConfigSchema = {};
    
    this.data.forEach((value, key) => {
      result[key] = value;
    });
    
    return result;
  }

  /**
   * Get all configuration keys
   */
  public keys(): string[] {
    return Array.from(this.data.keys());
  }

  /**
   * Clear all configuration
   */
  public clear(): this {
    this.data.clear();
    logger.debug('Configuration cleared');
    return this;
  }

  /**
   * Set validation schema
   */
  public setSchema(schema: Joi.ObjectSchema): this {
    this.schema = schema;
    logger.debug('Configuration schema set');
    return this;
  }

  /**
   * Set validation options
   */
  public setValidationOptions(options: ConfigValidationOptions): this {
    this.validationOptions = { ...this.validationOptions, ...options };
    logger.debug('Configuration validation options updated');
    return this;
  }

  /**
   * Validate configuration against schema
   */
  public validate(config?: ConfigSchema): this {
    if (!this.schema) {
      logger.warn('No validation schema provided');
      return this;
    }

    const dataToValidate = config || this.getAll();
    
    const { error, value } = this.schema.validate(dataToValidate, this.validationOptions);
    
    if (error) {
      const errorMessage = `Configuration validation failed: ${error.details.map(d => d.message).join(', ')}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    // If validation passed and we're validating current data, update with cleaned values
    if (!config && this.validationOptions.stripUnknown) {
      this.load(value);
    }

    logger.debug('Configuration validation passed');
    return this;
  }

  /**
   * Merge configuration with another config object
   */
  public merge(config: ConfigSchema): this {
    if (!config || typeof config !== 'object') {
      throw new Error('Configuration to merge must be an object');
    }

    Object.entries(config).forEach(([key, value]) => {
      if (this.data.has(key) && typeof this.data.get(key) === 'object' && typeof value === 'object') {
        // Deep merge objects
        const existing = this.data.get(key);
        this.data.set(key, { ...existing, ...value });
      } else {
        this.data.set(key, value);
      }
    });

    logger.debug(`Configuration merged with ${Object.keys(config).length} keys`);
    return this;
  }

  /**
   * Create a copy of the configuration
   */
  public clone(): Config {
    const cloned = new Config();
    cloned.data = new Map(this.data);
    cloned.schema = this.schema;
    cloned.validationOptions = { ...this.validationOptions };
    
    logger.debug('Configuration cloned');
    return cloned;
  }

  /**
   * Load configuration from environment variables
   */
  public static fromEnvironment(prefix: string = ''): Config {
    const config = new Config();
    
    Object.entries(process.env).forEach(([key, value]) => {
      if (!prefix || key.startsWith(prefix)) {
        const configKey = prefix ? key.substring(prefix.length) : key;
        
        // Try to parse JSON values
        let parsedValue: any = value;
        if (value && (value.startsWith('{') || value.startsWith('['))) {
          try {
            parsedValue = JSON.parse(value);
          } catch {
            // Keep as string if JSON parsing fails
          }
        }
        
        config.set(configKey.toLowerCase(), parsedValue);
      }
    });
    
    logger.info(`Configuration loaded from environment with prefix '${prefix}'`);
    return config;
  }
}
