import logger from '../../config/logger';

export type EventCallback = (...args: any[]) => void;

export interface EventListener {
  callback: EventCallback;
  once: boolean;
}

export class EventEmitter {
  private events: Map<string, EventListener[]> = new Map();
  private maxListeners: number = 10;

  /**
   * Register an event listener
   */
  public on(event: string, callback: EventCallback): this {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event)!;
    
    // Check max listeners
    if (listeners.length >= this.maxListeners) {
      logger.warn(`Maximum listeners (${this.maxListeners}) exceeded for event '${event}'`);
    }

    listeners.push({ callback, once: false });
    logger.debug(`Event listener added for '${event}'`);
    
    return this;
  }

  /**
   * Register a one-time event listener
   */
  public once(event: string, callback: EventCallback): this {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event)!;
    listeners.push({ callback, once: true });
    logger.debug(`One-time event listener added for '${event}'`);
    
    return this;
  }

  /**
   * Remove an event listener
   */
  public off(event: string, callback?: EventCallback): this {
    if (!this.events.has(event)) {
      return this;
    }

    const listeners = this.events.get(event)!;

    if (!callback) {
      // Remove all listeners for this event
      this.events.delete(event);
      logger.debug(`All listeners removed for event '${event}'`);
    } else {
      // Remove specific listener
      const index = listeners.findIndex(listener => listener.callback === callback);
      if (index !== -1) {
        listeners.splice(index, 1);
        logger.debug(`Specific listener removed for event '${event}'`);
        
        // Clean up empty event arrays
        if (listeners.length === 0) {
          this.events.delete(event);
        }
      }
    }

    return this;
  }

  /**
   * Emit an event
   */
  public emit(event: string, ...args: any[]): boolean {
    if (!this.events.has(event)) {
      logger.debug(`No listeners for event '${event}'`);
      return false;
    }

    const listeners = this.events.get(event)!;
    const listenersToRemove: number[] = [];

    logger.debug(`Emitting event '${event}' to ${listeners.length} listeners`);

    // Execute all listeners
    listeners.forEach((listener, index) => {
      try {
        listener.callback(...args);
        
        // Mark one-time listeners for removal
        if (listener.once) {
          listenersToRemove.push(index);
        }
      } catch (error) {
        logger.error(`Error in event listener for '${event}':`, error);
      }
    });

    // Remove one-time listeners (in reverse order to maintain indices)
    listenersToRemove.reverse().forEach(index => {
      listeners.splice(index, 1);
    });

    // Clean up empty event arrays
    if (listeners.length === 0) {
      this.events.delete(event);
    }

    return true;
  }

  /**
   * Get all event names
   */
  public eventNames(): string[] {
    return Array.from(this.events.keys());
  }

  /**
   * Get listener count for an event
   */
  public listenerCount(event: string): number {
    const listeners = this.events.get(event);
    return listeners ? listeners.length : 0;
  }

  /**
   * Get all listeners for an event
   */
  public listeners(event: string): EventCallback[] {
    const listeners = this.events.get(event);
    return listeners ? listeners.map(l => l.callback) : [];
  }

  /**
   * Remove all listeners
   */
  public removeAllListeners(event?: string): this {
    if (event) {
      this.events.delete(event);
      logger.debug(`All listeners removed for event '${event}'`);
    } else {
      this.events.clear();
      logger.debug('All event listeners removed');
    }
    
    return this;
  }

  /**
   * Set maximum number of listeners per event
   */
  public setMaxListeners(n: number): this {
    if (n < 0) {
      throw new Error('Maximum listeners count must be non-negative');
    }
    
    this.maxListeners = n;
    logger.debug(`Maximum listeners set to ${n}`);
    
    return this;
  }

  /**
   * Get maximum number of listeners per event
   */
  public getMaxListeners(): number {
    return this.maxListeners;
  }

  /**
   * Prepend a listener to the beginning of the listeners array
   */
  public prependListener(event: string, callback: EventCallback): this {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event)!;
    listeners.unshift({ callback, once: false });
    logger.debug(`Event listener prepended for '${event}'`);
    
    return this;
  }

  /**
   * Prepend a one-time listener to the beginning of the listeners array
   */
  public prependOnceListener(event: string, callback: EventCallback): this {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event)!;
    listeners.unshift({ callback, once: true });
    logger.debug(`One-time event listener prepended for '${event}'`);
    
    return this;
  }

  /**
   * Get raw listeners (including once flag)
   */
  public rawListeners(event: string): EventListener[] {
    const listeners = this.events.get(event);
    return listeners ? [...listeners] : [];
  }
}
