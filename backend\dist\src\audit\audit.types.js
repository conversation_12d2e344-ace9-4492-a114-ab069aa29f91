"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditEventSeverity = exports.AuditEventCategory = void 0;
var AuditEventCategory;
(function (AuditEventCategory) {
    AuditEventCategory["PAIM_OPERATION"] = "PAIM_OPERATION";
    AuditEventCategory["AGENT_OPERATION"] = "AGENT_OPERATION";
    AuditEventCategory["AI_OPERATION"] = "AI_OPERATION";
    AuditEventCategory["AUTH_OPERATION"] = "AUTH_OPERATION";
    AuditEventCategory["SYSTEM_OPERATION"] = "SYSTEM_OPERATION";
    AuditEventCategory["SECURITY_EVENT"] = "SECURITY_EVENT";
    AuditEventCategory["COMPLIANCE_EVENT"] = "COMPLIANCE_EVENT";
    AuditEventCategory["DATA_ACCESS"] = "DATA_ACCESS";
    AuditEventCategory["CONFIGURATION_CHANGE"] = "CONFIGURATION_CHANGE";
    AuditEventCategory["PERFORMANCE_METRIC"] = "PERFORMANCE_METRIC";
    AuditEventCategory["USER_ACTIVITY"] = "USER_ACTIVITY";
    AuditEventCategory["MONITORING"] = "MONITORING";
    AuditEventCategory["BILLING"] = "BILLING";
})(AuditEventCategory || (exports.AuditEventCategory = AuditEventCategory = {}));
var AuditEventSeverity;
(function (AuditEventSeverity) {
    AuditEventSeverity["CRITICAL"] = "CRITICAL";
    AuditEventSeverity["HIGH"] = "HIGH";
    AuditEventSeverity["MEDIUM"] = "MEDIUM";
    AuditEventSeverity["LOW"] = "LOW";
    AuditEventSeverity["INFO"] = "INFO";
})(AuditEventSeverity || (exports.AuditEventSeverity = AuditEventSeverity = {}));
