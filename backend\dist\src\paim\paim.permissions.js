"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PAIM_TIER_PERMISSIONS = exports.PAIMTier = void 0;
exports.tierIncludes = tierIncludes;
// backend/src/paim/paim.permissions.ts
const permissions_1 = require("../auth/permissions");
const db_1 = require("../types/db"); // Import the canonical PaimTierEnum
Object.defineProperty(exports, "PAIMTier", { enumerable: true, get: function () { return db_1.PaimTierEnum; } });
const basicPermissions = [
    permissions_1.PERMISSIONS.PAIM_READ,
    permissions_1.PERMISSIONS.POWER_OPS_READ,
    permissions_1.PERMISSIONS.WORKFLOW_READ,
    permissions_1.PERMISSIONS.AGENT_READ,
];
const professionalPermissions = [
    ...basicPermissions,
    permissions_1.PERMISSIONS.PAIM_CREATE,
    permissions_1.PERMISSIONS.PAIM_UPDATE,
    permissions_1.PERMISSIONS.POWER_OPS_CREATE,
    permissions_1.PERMISSIONS.POWER_OPS_UPDATE,
    permissions_1.PERMISSIONS.WORKFLOW_CREATE,
    permissions_1.PERMISSIONS.WORKFLOW_UPDATE,
];
const enterprisePermissions = [
    ...professionalPermissions,
    permissions_1.PERMISSIONS.PAIM_DELETE,
    permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS, // Enterprise can manage their own tiers (e.g., downgrade/upgrade)
    permissions_1.PERMISSIONS.POWER_OPS_DELETE,
    permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL,
    permissions_1.PERMISSIONS.WORKFLOW_DELETE,
    permissions_1.PERMISSIONS.WORKFLOW_MANAGE_ALL,
    permissions_1.PERMISSIONS.AGENT_CREATE,
    permissions_1.PERMISSIONS.AGENT_UPDATE,
    permissions_1.PERMISSIONS.AGENT_DELETE,
    permissions_1.PERMISSIONS.AGENT_MANAGE_ALL,
    permissions_1.PERMISSIONS.ORG_MANAGE_MEMBERS,
    permissions_1.PERMISSIONS.ORG_MANAGE_ROLES,
    permissions_1.PERMISSIONS.BILLING_VIEW,
];
exports.PAIM_TIER_PERMISSIONS = {
    [db_1.PaimTierEnum.Basic]: basicPermissions,
    [db_1.PaimTierEnum.Professional]: professionalPermissions,
    [db_1.PaimTierEnum.Enterprise]: enterprisePermissions,
    [db_1.PaimTierEnum.Custom]: [
        // Custom tiers will have permissions defined dynamically,
        // but for a baseline, they might inherit Enterprise or have specific ones.
        // For now, we'll give them all permissions for demonstration.
        ...Object.values(permissions_1.PERMISSIONS),
    ],
};
// Helper function to check if a tier includes another tier's permissions
function tierIncludes(tier, requiredTier) {
    const tierOrder = [db_1.PaimTierEnum.Basic, db_1.PaimTierEnum.Professional, db_1.PaimTierEnum.Enterprise, db_1.PaimTierEnum.Custom];
    const tierIndex = tierOrder.indexOf(tier);
    const requiredTierIndex = tierOrder.indexOf(requiredTier);
    return tierIndex >= requiredTierIndex;
}
// TODO: Implement cross-PAIM permission validation for multi-tenant scenarios.
// This would involve checking if a user from one PAIM can access resources of another PAIM.
// This typically requires checking the organizationId or tenantId associated with the resource and the user.
// Example:
// public canAccessCrossPAIMResource(
//   userPaimTier: PAIMTier,
//   userOrganizationId: string,
//   resourceOrganizationId: string,
//   requiredPermission: Permission
// ): boolean {
//   if (userOrganizationId === resourceOrganizationId) {
//     return authorizationService.hasPermission([], userPaimTier, requiredPermission); // Check within same PAIM
//   }
//   // Logic for cross-PAIM access, e.g., SuperAdmin can access all, or specific agreements
//   return false;
// }
