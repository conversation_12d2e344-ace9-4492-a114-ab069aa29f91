# Phase 1: Core Infrastructure Fixes - Task Specification

## Objective
Get basic Express server running with clean TypeScript compilation

## Priority
Critical - Blocking all other phases

## Estimated Time
2-3 hours

## Prerequisites
- Node.js and npm installed
- Project dependencies installed
- Access to backend source code

## Detailed Tasks

### Task 1.1: TypeScript Configuration Adjustment
**Objective**: Make TypeScript configuration less strict to allow initial compilation

**Actions Required**:
1. Modify `tsconfig.json` to temporarily relax strict settings:
   - Set `exactOptionalPropertyTypes: false`
   - Set `strictNullChecks: false` temporarily
   - Add `skipLibCheck: true` if not present
   - Ensure `noImplicitAny: false` for initial compatibility

2. Create backup of original strict configuration for later restoration

3. Test compilation with `npm run build`

**Expected Outcome**: TypeScript compilation succeeds with minimal errors

### Task 1.2: Dependency Injection Resolution
**Objective**: Fix constructor parameter mismatches in core services

**Actions Required**:
1. **PowerOpsController**: Fix constructor to accept required `NotificationService`
2. **WorkflowCollaborationService**: Add missing `CollaborationEvents` parameter
3. **PaimService**: Resolve constructor parameter count mismatch
4. **MonitoringService**: Fix service instantiation parameters
5. **AgentService**: Resolve PaimService dependency injection

**Files to Modify**:
- `src/powerops/powerops.controller.ts`
- `src/workflow-collaboration/workflow-collaboration.service.ts`
- `src/paim/paim.service.ts`
- `src/monitoring/monitoring.controller.ts`
- `src/agent/agent.service.ts`
- `src/app.ts`

**Expected Outcome**: All services instantiate without constructor errors

### Task 1.3: Code Cleanup and Deduplication
**Objective**: Remove duplicate code and fix malformed sections

**Actions Required**:
1. **PowerOps Service Cleanup**:
   - Remove duplicate class definitions in `powerops.service.ts`
   - Consolidate imports and exports
   - Fix duplicate identifier errors

2. **Import/Export Fixes**:
   - Fix missing default export in `paim.controller.ts`
   - Resolve module import issues in `app.ts`
   - Clean up circular import dependencies

3. **Malformed Code Removal**:
   - Remove any remaining merge artifacts (`:start_line:`, `-------`)
   - Fix syntax errors in affected files
   - Ensure proper code formatting

**Expected Outcome**: Clean, compilable code without duplicates or syntax errors

### Task 1.4: Basic Express Server Setup
**Objective**: Ensure Express server can start successfully

**Actions Required**:
1. **App Configuration**:
   - Verify `app.ts` imports and middleware setup
   - Ensure proper route registration
   - Add basic error handling middleware

2. **Server Entry Point**:
   - Verify `src/index.ts` server startup logic
   - Add proper error handling for server startup
   - Ensure graceful shutdown handling

3. **Environment Configuration**:
   - Verify environment variable loading
   - Ensure required environment variables are documented
   - Add fallback values for development

**Expected Outcome**: Express server starts without errors and responds to basic requests

### Task 1.5: Basic Health Check Implementation
**Objective**: Add simple health check endpoint for testing

**Actions Required**:
1. Create basic health check route in `app.ts`:
   ```typescript
   app.get('/health', (req, res) => {
     res.status(200).json({ 
       status: 'ok', 
       timestamp: new Date().toISOString(),
       service: 'theaigency-backend'
     });
   });
   ```

2. Test health check endpoint responds correctly

**Expected Outcome**: Health check endpoint returns 200 OK with status information

## Quality Control Checklist

### Build Verification
- [ ] `npm run build` completes without errors
- [ ] TypeScript compilation produces clean output
- [ ] No critical syntax errors remain
- [ ] All imports resolve correctly

### Runtime Testing
- [ ] `npm start` launches server successfully
- [ ] Server listens on configured port
- [ ] Health check endpoint responds with 200 OK
- [ ] No critical runtime errors in console

### Code Quality
- [ ] No duplicate class definitions
- [ ] All imports/exports properly structured
- [ ] Constructor parameters match service definitions
- [ ] Code formatting is consistent

### Documentation
- [ ] Update any configuration changes in documentation
- [ ] Document any temporary workarounds implemented
- [ ] Note any deferred strict type checking for Phase 4

## Success Criteria
1. **Build Success**: TypeScript compilation completes without errors
2. **Server Startup**: Express server starts and listens successfully
3. **Basic Functionality**: Health check endpoint responds correctly
4. **Clean Code**: No duplicate code or syntax errors
5. **Dependency Resolution**: All core services instantiate properly

## Handoff to Phase 2
Upon successful completion:
1. Provide build verification report
2. Document any temporary configurations that need restoration
3. Confirm server startup and basic endpoint functionality
4. Hand off clean, running codebase to Phase 2 agent

## Risk Mitigation
- Keep backup of original configurations
- Document all temporary changes for later restoration
- Test incrementally after each major change
- Maintain rollback capability at each step

---

**Phase**: 1 of 4
**Dependencies**: None (Initial phase)
**Next Phase**: Database and Authentication Setup
**Estimated Completion**: 2-3 hours from start
