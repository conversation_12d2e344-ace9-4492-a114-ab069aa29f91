"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdapterFactory = void 0;
const appwrite_adapter_1 = require("./appwrite-adapter");
class AdapterFactory {
    static getAdapter(type) {
        switch (type) {
            case 'appwrite':
                return new appwrite_adapter_1.AppwriteAdapter();
            default:
                throw new Error(`Unsupported database adapter type: ${type}`);
        }
    }
}
exports.AdapterFactory = AdapterFactory;
