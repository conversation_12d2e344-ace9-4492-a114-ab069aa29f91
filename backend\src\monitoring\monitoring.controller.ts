import * as express from 'express'; // Import express for types
import { Request, Response, NextFunction } from 'express'; // Explicitly import Request, Response, NextFunction
import { MonitoringService } from './monitoring.service';
import { MonitoringRepository } from './monitoring.repository'; // For constructor injection
import { AuditTrailService } from '../audit/audit.service'; // For constructor injection
import { PaimService } from '../paim/paim.service'; // For constructor injection
import { PowerOpsService } from '../powerops/powerops.service'; // For constructor injection
import { CulturalSensitivityService } from '../cultural-sensitivity/cultural-sensitivity.service'; // For constructor injection
import db from '../database/db'; // For CulturalSensitivityService constructor
import { CustomError } from '../utils/errors';
import logger from '../config/logger';
import { CreativeApiService, ProviderHealthStatus } from '../creative-api/creative-api.service'; // Import CreativeApiService
import { defaultHttpClient } from '../utils/http-client.service'; // Import defaultHttpClient
import { requireParam } from '../utils/type-guards';
import {
  SystemHealth,
  PerformanceMetrics,
  MonitoringAlert,
  CoveEscalation,
} from './monitoring.types';

export class MonitoringController {
  public router: express.Router; // Declare router
  private monitoringService: MonitoringService;
  private creativeApiService: CreativeApiService; // Declare CreativeApiService

  constructor() {
    const monitoringRepository = new MonitoringRepository();
    const auditTrailService = new AuditTrailService();
    const paimService = new PaimService(
      null as any,
      auditTrailService,
      {} as any, // NotificationService placeholder
      {} as any  // WebSocketService placeholder
    );
    const powerOpsService = new PowerOpsService();
    const culturalSensitivityService = new CulturalSensitivityService(db);
    this.creativeApiService = new CreativeApiService(auditTrailService); // Initialize CreativeApiService with auditTrailService

    this.monitoringService = new MonitoringService(
      monitoringRepository,
      auditTrailService,
      paimService,
      powerOpsService,
      culturalSensitivityService
    );
    this.router = express.Router(); // Initialize router
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.get('/health', this.getHealthStatus.bind(this));
    this.router.get('/ready', this.getReadinessStatus.bind(this));
    this.router.post('/system-health', this.recordSystemHealth.bind(this));
    this.router.post('/performance-metrics', this.recordPerformanceMetrics.bind(this));
    this.router.get('/system-health/:serviceName', this.getLatestSystemHealth.bind(this));
    this.router.get('/alerts', this.getActiveAlerts.bind(this));
    this.router.get('/escalations', this.getCoveEscalations.bind(this));
    this.router.post('/alerts/trigger', this.triggerManualAlert.bind(this));
    this.router.put('/escalations/:escalationId/resolve', this.resolveCoveEscalation.bind(this));
    this.router.get('/metrics/http-client', this.getHttpClientMetrics.bind(this)); // New HTTP client metrics endpoint
  }

  // Health check endpoint
  getHealthStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const dbStatus = await db.raw('SELECT 1'); // Check database connectivity
      const creativeApiHealth = await this.creativeApiService.checkProviderHealth(); // Check external API health
      // Basic check for defaultHttpClient
      const httpClientStatus = defaultHttpClient ? 'initialized' : 'uninitialized';

      const overallHealth = dbStatus && creativeApiHealth.every(p => p.status === 'healthy') && httpClientStatus === 'initialized' ? 'healthy' : 'degraded';
      const uptime = process.uptime(); // Node.js process uptime

      (res as express.Response).status(200).json({
        status: overallHealth,
        database: dbStatus ? 'connected' : 'disconnected',
        creativeApis: creativeApiHealth,
        httpClient: httpClientStatus, // Add HTTP client status
        uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
        version: process.env.APP_VERSION || '1.0.0', // Assuming APP_VERSION env var
      });
    } catch (error) {
      console.error('Health check failed:', error);
      (res as express.Response).status(500).json({ status: 'unhealthy', error: (error as Error).message });
    }
  };

  // Readiness check endpoint
  getReadinessStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // For readiness, we might check if all critical services are ready to accept traffic
      // This could involve checking database connections, message queues, etc.
      const dbReady = await db.raw('SELECT 1').then(() => true).catch(() => false);
      const creativeApiReady = await this.creativeApiService.checkProviderHealth().then(statuses => statuses.every(p => p.status === 'healthy')).catch(() => false);
      const httpClientReady = defaultHttpClient ? true : false; // Check if http client is ready

      const isReady = dbReady && creativeApiReady && httpClientReady;

      (res as express.Response).status(isReady ? 200 : 503).json({
        status: isReady ? 'ready' : 'not_ready',
        database: dbReady ? 'ready' : 'not_ready',
        creativeApis: creativeApiReady ? 'ready' : 'not_ready',
        httpClient: httpClientReady ? 'ready' : 'not_ready', // Add HTTP client readiness
      });
    } catch (error) {
      console.error('Readiness check failed:', error);
      (res as express.Response).status(503).json({ status: 'not_ready', error: (error as Error).message });
    }
  };

  // New endpoint for HTTP client metrics
  getHttpClientMetrics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const metrics = {
        totalRequests: defaultHttpClient.getMetrics().length,
        averageResponseTime: defaultHttpClient.getAverageResponseTime(),
        errorRate: defaultHttpClient.getErrorRate(),
        // You can add more specific metrics here if needed, e.g., per-endpoint metrics
        // For now, we'll return the raw metrics as well for detailed analysis
        rawMetrics: defaultHttpClient.getMetrics(),
      };
      (res as express.Response).status(200).json(metrics);
    } catch (error) {
      logger.error('Failed to retrieve HTTP client metrics:', error);
      next(error);
    }
  };

  // Endpoint to record system health (e.g., from a health check agent)
  recordSystemHealth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const healthData: Omit<SystemHealth, 'timestamp'> = (req as express.Request).body as Omit<SystemHealth, 'timestamp'>;
      const recordedHealth = await this.monitoringService.recordSystemHealth(healthData);
      (res as express.Response).status(201).json(recordedHealth);
    } catch (error) {
      next(error);
    }
  };

  // Endpoint to record performance metrics
  recordPerformanceMetrics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { serviceName, metrics }: { serviceName: string; metrics: PerformanceMetrics } = (req as express.Request).body as { serviceName: string; metrics: PerformanceMetrics };
      const recordedMetrics = await this.monitoringService.recordPerformanceMetrics(serviceName, metrics);
      (res as express.Response).status(201).json(recordedMetrics);
    } catch (error) {
      next(error);
    }
  };

  // Endpoint to get latest system health for a service
  getLatestSystemHealth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { serviceName } = (req as express.Request).params;
      const health = await this.monitoringService['repository'].getLatestSystemHealth(requireParam(serviceName, 'serviceName'));
      if (!health) {
        throw new CustomError(`System health for service ${serviceName} not found.`, { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
      }
      (res as express.Response).status(200).json(health);
    } catch (error) {
      next(error);
    }
  };

  // Endpoint to get active alerts
  getActiveAlerts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const alerts = await this.monitoringService['repository'].getActiveMonitoringAlerts();
      (res as express.Response).status(200).json(alerts);
    } catch (error) {
      next(error);
    }
  };

  // Endpoint to get Cove escalations
  getCoveEscalations = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const escalations = await this.monitoringService['repository'].getAuditLogs('COVE_ESCALATION_INITIATED', 'CoveEscalation');
      (res as express.Response).status(200).json(escalations);
    } catch (error) {
      next(error);
    }
  };

  // Endpoint to manually trigger an alert (for testing/admin)
  triggerManualAlert = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const alertData: MonitoringAlert = (req as express.Request).body as MonitoringAlert;
      const triggeredAlert = await this.monitoringService.triggerAlert(alertData);
      (res as express.Response).status(201).json(triggeredAlert);
    } catch (error) {
      next(error);
    }
  };

  // Endpoint to resolve a Cove escalation (e.g., from Cove UI)
  resolveCoveEscalation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { escalationId } = (req as express.Request).params;
      const { resolvedBy, details } = (req as express.Request).body as { resolvedBy: string; details?: string };
      if (!resolvedBy) {
        throw new CustomError('Resolved by user is required.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
      }
      const resolvedEscalation = await this.monitoringService.resolveCoveEscalation(requireParam(escalationId, 'escalationId'), resolvedBy, details);
      if (!resolvedEscalation) {
        throw new CustomError(`Escalation with ID ${escalationId} not found.`, { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
      }
      (res as express.Response).status(200).json(resolvedEscalation);
    } catch (error) {
      next(error);
    }
  };
}