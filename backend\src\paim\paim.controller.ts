import { Response, NextFunction, Router } from 'express';
import { PaimRequest } from './paim.types';
import { PaimService } from './paim.service';
import { PaimRepository } from './paim.repository';
import { AuditTrailService } from '../audit/audit.service';
import { NotificationService } from '../notifications/notification.service'; // Import NotificationService
import { WebSocketService } from '../websocket/websocket.service'; // Import WebSocketService
import {
  CreatePaimInstanceRequest,
  UpdatePaimInstanceRequest,
  PaimTierChangeRequest,
  CrossPaimCommunicationRequest,
  PaimHierarchyUpdate,
  PaginationMetadata,
} from './paim.types';
import { asyncHandler } from '../utils/asyncHandler'; // Named import
import { validate } from '../utils/validation'; // Import validate middleware
import { CustomError } from '../utils/errors';
import logger from '../config/logger'; // Default import
import { requireParam, getParam } from '../utils/type-guards';
import {
  createPaimInstanceSchema,
  updatePaimInstanceSchema,
  paimTierChangeRequestSchema,
  crossPaimCommunicationRequestSchema,
  paimHierarchyUpdateSchema,
  getAllPaimInstancesQuerySchema,
} from './paim.validation'; // Import Joi schemas
import { authorize } from '../middleware/authorization'; // Import authorization middleware
import { PERMISSIONS } from '../auth/permissions'; // Import permissions

export class PaimController {
  public router: Router;
  private paimService: PaimService;
  private paimRepository: PaimRepository;
  private auditTrailService: AuditTrailService;

  constructor(notificationService: NotificationService, wsService: WebSocketService) {
    this.auditTrailService = new AuditTrailService();
    this.paimRepository = new PaimRepository(this.auditTrailService);
    this.paimService = new PaimService(this.paimRepository, this.auditTrailService, notificationService, wsService);
    this.router = Router();
    this.initializeRoutes();
  }

  private initializeRoutes() {
    // PAIM Instance Management
    this.router.get(
      '/',
      validate(getAllPaimInstancesQuerySchema), // Validate query parameters
      authorize([PERMISSIONS.PAIM_READ]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { status, page, size, sort } = req.query;
        const filters = { status: getParam(status as string | undefined) };
        const pagination = { page: parseInt(getParam(page as string | undefined, '1')), size: parseInt(getParam(size as string | undefined, '10')), sort: getParam(sort as string | undefined, 'createdAt,desc') };

        // Ensure req.user is available from a preceding authentication middleware
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }

        const { data, totalElements } = await this.paimService.getAllPaimInstances(
          req.user, // Pass the entire user object
          filters,
          pagination
        );

        const paginationMetadata: PaginationMetadata = {
          totalElements,
          totalPages: Math.ceil(totalElements / pagination.size),
          currentPage: pagination.page,
          pageSize: pagination.size,
        };

        res.status(200).json({ data, pagination: paginationMetadata });
      })
    );

    this.router.post(
      '/',
      validate(createPaimInstanceSchema), // Validate request body
      authorize([PERMISSIONS.PAIM_CREATE]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const paimInstance = await this.paimService.createPaimInstance(req.body as CreatePaimInstanceRequest, req.user);
        res.status(201).json(paimInstance);
      })
    );

    this.router.get(
      '/:paimInstanceId',
      authorize([PERMISSIONS.PAIM_READ]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { paimInstanceId } = req.params;
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const paimInstance = await this.paimService.getPaimInstanceById(requireParam(paimInstanceId, 'paimInstanceId'), req.user);
        res.status(200).json(paimInstance);
      })
    );

    this.router.put(
      '/:paimInstanceId',
      validate(updatePaimInstanceSchema), // Validate request body
      authorize([PERMISSIONS.PAIM_UPDATE]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { paimInstanceId } = req.params;
        const updateRequest: UpdatePaimInstanceRequest = req.body;
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const updatedPaimInstance = await this.paimService.updatePaimInstance(
          requireParam(paimInstanceId, 'paimInstanceId'),
          req.user, // Pass the entire user object
          updateRequest
        );
        res.status(200).json(updatedPaimInstance);
      })
    );

    this.router.delete(
      '/:paimInstanceId',
      authorize([PERMISSIONS.PAIM_DELETE]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { paimInstanceId } = req.params;
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        await this.paimService.deletePaimInstance(requireParam(paimInstanceId, 'paimInstanceId'), req.user);
        res.status(204).send();
      })
    );

    // PAIM Tier Management
    this.router.post(
      '/:paimInstanceId/tier-change-requests',
      validate(paimTierChangeRequestSchema), // Validate request body
      authorize([PERMISSIONS.PAIM_MANAGE_TIERS]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { paimInstanceId } = req.params;
        const tierChangeRequest: PaimTierChangeRequest = req.body;
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const status = await this.paimService.requestPaimTierChange(
          requireParam(paimInstanceId, 'paimInstanceId'),
          req.user, // Pass the entire user object
          tierChangeRequest
        );
        res.status(202).json(status);
      })
    );

    // PAIM Hierarchy Management
    this.router.get(
      '/:paimInstanceId/hierarchy',
      authorize([PERMISSIONS.PAIM_READ]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { paimInstanceId } = req.params;
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const hierarchy = await this.paimService.getPaimHierarchy(requireParam(paimInstanceId, 'paimInstanceId'), req.user);
        res.status(200).json(hierarchy);
      })
    );

    this.router.put(
      '/:paimInstanceId/hierarchy',
      validate(paimHierarchyUpdateSchema), // Validate request body
      authorize([PERMISSIONS.PAIM_MANAGE_TIERS]), // Add authorization middleware
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { paimInstanceId } = req.params;
        const hierarchyUpdate: PaimHierarchyUpdate = req.body;
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const updatedHierarchy = await this.paimService.updatePaimHierarchy(
          requireParam(paimInstanceId, 'paimInstanceId'),
          req.user, // Pass the entire user object
          hierarchyUpdate
        );
        res.status(200).json(updatedHierarchy);
      })
    );

    // Cross-PAIM Communication
    this.router.post(
      '/:paimInstanceId/communicate',
      validate(crossPaimCommunicationRequestSchema), // Validate request body
      authorize([PERMISSIONS.PAIM_VIEW_ALL]), // Example permission for cross-PAIM communication
      asyncHandler(async (req: PaimRequest, res: Response) => {
        const { paimInstanceId } = req.params;
        const communicationRequest: CrossPaimCommunicationRequest = req.body;
        if (!req.user) {
          throw new CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const response = await this.paimService.communicateWithPaim(
          requireParam(paimInstanceId, 'paimInstanceId'),
          req.user, // Pass the entire user object
          communicationRequest.targetPaimInstanceId,
          communicationRequest.message,
          communicationRequest.messageType || 'text'
        );
        res.status(200).json(response);
      })
    );
  }
}

export default PaimController;