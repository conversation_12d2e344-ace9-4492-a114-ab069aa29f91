"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const db_1 = __importDefault(require("./database/db"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const migrationsDir = path.resolve(__dirname, '../migrations');
async function runMigrations() {
    try {
        await db_1.default.schema.createTableIfNotExists('migrations', (table) => {
            table.increments('id').primary();
            table.string('name').notNullable().unique();
            table.timestamp('applied_at').defaultTo(db_1.default.fn.now());
        });
        console.log('Migrations table ensured.');
        const files = fs.readdirSync(migrationsDir).sort();
        for (const file of files) {
            if (file.endsWith('.ts')) {
                const migrationName = file.replace('.ts', '');
                const [timestamp, name] = migrationName.split('_'); // Assuming format like YYYYMMDDHHMMSS_migration_name
                const appliedMigration = await (0, db_1.default)('migrations').where({ name: migrationName }).first();
                if (!appliedMigration) {
                    console.log(`Applying migration: ${migrationName}`);
                    const migration = require(path.join(migrationsDir, file));
                    await db_1.default.transaction(async (trx) => {
                        await migration.up(trx);
                        await trx('migrations').insert({ name: migrationName });
                    });
                    console.log(`Migration ${migrationName} applied successfully.`);
                }
                else {
                    console.log(`Migration ${migrationName} already applied.`);
                }
            }
        }
        console.log('All migrations processed.');
    }
    catch (error) {
        console.error('Error running migrations:', error);
        process.exit(1);
    }
    finally {
        // Knex manages its own connection pool, no explicit end needed here
    }
}
// To allow running from command line
if (require.main === module) {
    runMigrations();
}
