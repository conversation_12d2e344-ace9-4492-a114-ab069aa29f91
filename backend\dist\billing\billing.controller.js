"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingController = void 0;
const express_1 = require("express");
const asyncHandler_1 = require("@utils/asyncHandler");
const validation_1 = require("@utils/validation");
const joi_1 = __importDefault(require("joi"));
const auth_middleware_1 = require("../auth/auth.middleware"); // Import authenticate middleware
// Define schema for incoming webhook data
const PowerOpsUsageSchema = joi_1.default.object({
    org_id: joi_1.default.string().guid().required(),
    powerops_used: joi_1.default.number().positive().required(),
});
class BillingController {
    router;
    billingService;
    constructor(billingService) {
        this.billingService = billingService;
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.post('/usage', auth_middleware_1.authenticate, // Apply JWT validation
        (0, validation_1.validate)(PowerOpsUsageSchema), // Validate incoming data
        (0, asyncHandler_1.asyncHandler)(this.trackUsageWebhook.bind(this)));
    }
    async trackUsageWebhook(req, res) {
        const { org_id, powerops_used } = req.body;
        const record = await this.billingService.trackPowerOpsUsage(org_id, powerops_used);
        res.status(200).json({ message: 'PowerOps usage tracked successfully', record });
    }
}
exports.BillingController = BillingController;
