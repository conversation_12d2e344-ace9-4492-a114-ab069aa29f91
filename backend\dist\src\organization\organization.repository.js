"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationRepository = void 0;
class OrganizationRepository {
    knex;
    constructor(knex) {
        this.knex = knex;
    }
    async updateOrganizationSeats(orgId, seatsAllowed) {
        const [updatedRecord] = await this.knex('Tenants')
            .where({ tenant_id: orgId })
            .update({ org_seats_allowed: seatsAllowed, updated_at: this.knex.fn.now() })
            .returning('*'); // Return all columns for the full Organization type
        return updatedRecord || null;
    }
    async updateOrganizationSeatsUsed(orgId, seatsUsed) {
        const [updatedRecord] = await this.knex('Tenants')
            .where({ tenant_id: orgId })
            .update({ org_seats_used: seatsUsed, updated_at: this.knex.fn.now() })
            .returning('*'); // Return all columns for the full Organization type
        return updatedRecord || null;
    }
    async getOrganizationById(orgId) {
        const record = await this.knex('Tenants')
            .where({ tenant_id: orgId })
            .first();
        return record || null;
    }
    async updateFounderClubStatus(orgId, founderClubFlag, promoStartDate = null, promoEndDate = null) {
        const [updatedRecord] = await this.knex('Tenants')
            .where({ tenant_id: orgId })
            .update({
            founder_club_flag: founderClubFlag,
            promo_start_date: promoStartDate,
            promo_end_date: promoEndDate,
            updated_at: this.knex.fn.now(),
        })
            .returning('*');
        return updatedRecord || null;
    }
    async getOrganizationsByFounderClubFlag(flag) {
        return this.knex('Tenants')
            .where({ founder_club_flag: flag });
    }
    // Keeping this for backward compatibility if needed, but getOrganizationById is preferred
    async getOrganizationSeats(orgId) {
        const record = await this.knex('Tenants')
            .select('tenant_id as org_id', 'org_seats_allowed', 'org_seats_used')
            .where({ tenant_id: orgId })
            .first();
        return record || null;
    }
}
exports.OrganizationRepository = OrganizationRepository;
