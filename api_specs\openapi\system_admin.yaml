openapi: 3.0.0
info:
  title: The AIgency & System Administration API
  version: 1.0.0
  description: API endpoints for system health monitoring, auto-healing, escalation handling, and audit log access. Primarily for System Admin users.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: System Health
    description: Monitoring overall system health and performance
  - name: Auto-Healing
    description: Managing and triggering auto-healing processes
  - name: Escalations
    description: Handling and tracking system escalations
  - name: Audit Logs
    description: Accessing and filtering system audit logs

paths:
  /system/health:
    get:
      summary: Get overall system health status
      operationId: getSystemHealth
      tags:
        - System Health
      security:
        - bearerAuth: []
      responses:
        '200':
          description: System health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemHealthStatus'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /system/metrics:
    get:
      summary: Get detailed system performance metrics
      operationId: getSystemMetrics
      tags:
        - System Health
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: metricType
          schema:
            type: string
            enum: [cpu, memory, disk, network, database]
          description: Type of metric to retrieve.
        - in: query
          name: duration
          schema:
            type: string
            enum: [1h, 24h, 7d, 30d]
          description: Duration for which to retrieve metrics.
      responses:
        '200':
          description: System performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemMetrics'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /auto-healing/trigger:
    post:
      summary: Manually trigger an auto-healing process
      operationId: triggerAutoHealing
      tags:
        - Auto-Healing
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TriggerAutoHealingRequest'
      responses:
        '202':
          description: Auto-healing process initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoHealingStatus'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /auto-healing/status/{processId}:
    get:
      summary: Get status of an auto-healing process
      operationId: getAutoHealingStatus
      tags:
        - Auto-Healing
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: processId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the auto-healing process.
      responses:
        '200':
          description: Auto-healing process status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoHealingStatus'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /escalations:
    get:
      summary: Get all active escalations
      operationId: getAllEscalations
      tags:
        - Escalations
      security:
        - bearerAuth: []
      parameters:
        - $ref: '../paim_management.yaml#/components/parameters/PageParam'
        - $ref: '../paim_management.yaml#/components/parameters/SizeParam'
        - $ref: '../paim_management.yaml#/components/parameters/SortParam'
        - in: query
          name: status
          schema:
            type: string
            enum: [open, in_progress, resolved, closed]
          description: Filter escalations by status.
        - in: query
          name: severity
          schema:
            type: string
            enum: [low, medium, high, critical]
          description: Filter escalations by severity.
      responses:
        '200':
          description: List of escalations
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Escalation'
                  pagination:
                    $ref: '../paim_management.yaml#/components/schemas/PaginationMetadata'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Create a new escalation
      operationId: createEscalation
      tags:
        - Escalations
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEscalationRequest'
      responses:
        '201':
          description: Escalation created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Escalation'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /escalations/{escalationId}:
    put:
      summary: Update an existing escalation
      operationId: updateEscalation
      tags:
        - Escalations
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: escalationId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the escalation to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEscalationRequest'
      responses:
        '200':
          description: Escalation updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Escalation'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /audit-logs:
    get:
      summary: Get system audit logs
      operationId: getAuditLogs
      tags:
        - Audit Logs
      security:
        - bearerAuth: []
      parameters:
        - $ref: '../paim_management.yaml#/components/parameters/PageParam'
        - $ref: '../paim_management.yaml#/components/parameters/SizeParam'
        - $ref: '../paim_management.yaml#/components/parameters/SortParam'
        - in: query
          name: userId
          schema:
            type: string
            format: uuid
          description: Filter logs by user ID.
        - in: query
          name: eventType
          schema:
            type: string
          description: Filter logs by event type (e.g., "LOGIN", "RESOURCE_CREATE").
        - in: query
          name: startDate
          schema:
            type: string
            format: date-time
          description: Start date-time for logs (ISO 8601).
        - in: query
          name: endDate
          schema:
            type: string
            format: date-time
          description: End date-time for logs (ISO 8601).
      responses:
        '200':
          description: List of audit log entries
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/AuditLogEntry'
                  pagination:
                    $ref: '../paim_management.yaml#/components/schemas/PaginationMetadata'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    SystemHealthStatus:
      type: object
      properties:
        overallStatus:
          type: string
          enum: [operational, degraded, critical]
          example: operational
        components:
          type: object
          description: Status of individual system components.
          example:
            database:
              status: operational
              message: All good.
            api_gateway:
              status: operational
              message: All good.
            agent_service:
              status: degraded
              message: High latency detected.
        lastChecked:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    SystemMetrics:
      type: object
      properties:
        metricType:
          type: string
          enum: [cpu, memory, disk, network, database]
          example: cpu
        unit:
          type: string
          example: percent
        dataPoints:
          type: array
          items:
            type: object
            properties:
              timestamp:
                type: string
                format: date-time
              value:
                type: number
          example:
            - timestamp: '2023-10-27T09:55:00Z'
              value: 45.2
            - timestamp: '2023-10-27T10:00:00Z'
              value: 50.1
        average:
          type: number
          example: 47.65
        min:
          type: number
          example: 40.0
        max:
          type: number
          example: 60.5

    TriggerAutoHealingRequest:
      type: object
      required:
        - component
        - reason
      properties:
        component:
          type: string
          description: The system component to apply auto-healing to.
          example: agent_service
        reason:
          type: string
          description: Reason for triggering auto-healing.
          example: High latency detected in agent service.
        parameters:
          type: object
          nullable: true
          description: Optional parameters for the auto-healing process.

    AutoHealingStatus:
      type: object
      properties:
        processId:
          type: string
          format: uuid
          example: a1b2c3d4-e5f6-7890-1234-567890abcdef
        component:
          type: string
          example: agent_service
        status:
          type: string
          enum: [initiated, in_progress, completed, failed, cancelled]
          example: initiated
        startTime:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        endTime:
          type: string
          format: date-time
          nullable: true
        message:
          type: string
          nullable: true
          example: Auto-healing process started for agent service.

    Escalation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: e1s2c3a4-l5a6-7890-1234-567890abcdef
        title:
          type: string
          example: Critical Database Performance Issue
        description:
          type: string
          nullable: true
          example: Database response times are consistently above threshold.
        severity:
          type: string
          enum: [low, medium, high, critical]
          example: critical
        status:
          type: string
          enum: [open, in_progress, resolved, closed]
          example: open
        assignedTo:
          type: string
          nullable: true
          description: User ID or team responsible for handling the escalation.
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        resolvedAt:
          type: string
          format: date-time
          nullable: true

    CreateEscalationRequest:
      type: object
      required:
        - title
        - severity
      properties:
        title:
          type: string
          example: High CPU Usage on API Gateway
        description:
          type: string
          nullable: true
          example: API Gateway showing sustained high CPU usage.
        severity:
          type: string
          enum: [low, medium, high, critical]
          example: high
        assignedTo:
          type: string
          nullable: true

    UpdateEscalationRequest:
      type: object
      properties:
        title:
          type: string
          example: Resolved Database Performance Issue
        description:
          type: string
          nullable: true
          example: Database performance restored to normal.
        severity:
          type: string
          enum: [low, medium, high, critical]
          example: critical
        status:
          type: string
          enum: [open, in_progress, resolved, closed]
          example: resolved
        assignedTo:
          type: string
          nullable: true
        resolutionNotes:
          type: string
          nullable: true
          description: Notes on how the escalation was resolved.

    AuditLogEntry:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: a1u2d3i4-t5l6-7890-1234-567890abcdef
        timestamp:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        userId:
          type: string
          format: uuid
          nullable: true
          description: ID of the user who performed the action.
        paimInstanceId:
          type: string
          format: uuid
          nullable: true
          description: ID of the PAIM instance related to the action.
        eventType:
          type: string
          example: USER_LOGIN_SUCCESS
        resourceType:
          type: string
          nullable: true
          example: User
        resourceId:
          type: string
          format: uuid
          nullable: true
          example: d290f1ee-6c54-4b01-90e6-d701748f0851
        details:
          type: object
          description: Additional details about the event in JSON format.
          example:
            ipAddress: "***********"
            userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            action: "login"
            status: "success"