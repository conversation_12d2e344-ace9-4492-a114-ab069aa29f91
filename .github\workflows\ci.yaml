name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install backend dependencies
        run: npm install --prefix backend

      - name: Run backend tests
        run: npm test --prefix backend

      - name: Run HTTP Client Validation Tests
        run: npm test backend/src/utils/http-client.validation.test.ts

      - name: Run HTTP Client Performance Benchmarks
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        run: node scripts/benchmark-http-client.js --duration 30 --concurrency 5 --target-url ${{ secrets.PROD_API_URL }}

      # Add steps for frontend if applicable
      # - name: Set up Node.js for frontend
      #   uses: actions/setup-node@v3
      #   with:
      #     node-version: '18'
      # - name: Install frontend dependencies
      #   run: npm install --prefix frontend # Adjust path if your frontend is in a different directory
      # - name: Run frontend tests
      #   run: npm test --prefix frontend # Adjust path

      - name: Lint backend code
        run: npm run lint --prefix backend

      - name: Format backend code
        run: npm run prettier --prefix backend

      - name: Run Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          publishToken: ${{ secrets.SEMGREP_APP_TOKEN }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: backend-build
          path: backend/dist # Adjust path to your build output

      - name: Deploy to staging
        run: |
          echo "Deploying backend to staging environment..."
          # Add your actual deployment commands here, e.g.:
          # ssh user@your-staging-server "cd /path/to/app && git pull && npm install && npm run build && pm2 restart app"

  notify-on-failure:
    runs-on: ubuntu-latest
    needs: build-and-test # This job depends on the build-and-test job
    if: failure() # Only run if the previous job failed
    steps:
      - name: Send Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} # Replace with your Slack webhook URL
          SLACK_MESSAGE: "CI/CD Pipeline Failed! Check workflow run: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          SLACK_COLOR: "danger"

  pull-request-automation:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Label PR based on title
        uses: actions/labeler@v4
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          sync-labels: true
          configuration-path: .github/labeler.yml # Define your labeling rules here