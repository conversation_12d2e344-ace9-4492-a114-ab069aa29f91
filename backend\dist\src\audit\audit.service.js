"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditTrailService = void 0;
const audit_repository_1 = require("./audit.repository");
const audit_types_1 = require("./audit.types");
const authorization_service_1 = require("../auth/authorization.service");
const permissions_1 = require("../auth/permissions");
const errors_1 = require("../utils/errors");
class AuditTrailService {
    auditRepository;
    constructor() {
        this.auditRepository = new audit_repository_1.AuditRepository();
    }
    /**
     * Logs an audit event to the audit trail.
     * @param event The audit event to log.
     */
    async logEvent(event) {
        try {
            // Ensure timestamp is set if not provided
            if (!event.timestamp) {
                event.timestamp = new Date();
            }
            await this.auditRepository.saveAuditEvent(event);
            console.log(`Audit Logged: Category: ${event.category}, Operation: ${event.operationType}, Severity: ${event.severity}, Tenant: ${event.tenantId}, User: ${event.userId || 'N/A'}`);
        }
        catch (error) {
            console.error('Failed to log audit event:', error);
            // Depending on policy, might re-throw or handle more gracefully
        }
    }
    /**
     * Retrieves paginated audit events based on filters.
     * @param filters Filters to apply to the audit event search.
     * @param page The page number for pagination.
     * @param limit The number of events per page.
     * @returns A paginated list of audit events.
     */
    async getAuditLogs(user, filters, page = 1, limit = 20) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_VIEW)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view audit logs.', 403);
        }
        // Ensure filters are scoped to the user's tenant unless they have system-wide view permissions
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.SYSTEM_VIEW_LOGS)) {
            filters.tenantId = user.tenantId;
        }
        return this.auditRepository.getAuditEvents(filters, page, limit);
    }
    /**
     * Generates a compliance report for a given tenant and compliance standard.
     * @param tenantId The ID of the tenant.
     * @param complianceStandard The compliance standard (e.g., 'GDPR', 'SOX').
     * @param startDate The start date for the report.
     * @param endDate The end date for the report.
     * @returns A list of audit events relevant to the compliance standard.
     */
    async generateComplianceReport(user, complianceStandard, startDate, endDate) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_VIEW)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to generate compliance report.', 403);
        }
        return this.auditRepository.getComplianceReport(user.tenantId, complianceStandard, startDate, endDate);
    }
    /**
     * Retrieves audit analytics for a given tenant within a date range.
     * @param tenantId The ID of the tenant.
     * @param startDate The start date for analytics.
     * @param endDate The end date for analytics.
     * @returns Audit analytics data.
     */
    async getAuditAnalytics(user, startDate, endDate) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_VIEW)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view audit analytics.', 403);
        }
        return this.auditRepository.getAuditAnalytics(user.tenantId, startDate, endDate);
    }
    // Placeholder for data retention and archival policies.
    // In a real-world scenario, these would likely be implemented as scheduled jobs
    // that call methods within this service or the repository.
    async applyDataRetentionPolicy(user, retentionDays) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_MANAGE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to apply data retention policy.', 403);
        }
        console.log(`Applying data retention policy for tenant ${user.tenantId}: retaining data for ${retentionDays} days.`);
        // Example: Delete old data
        // await this.auditRepository.deleteEventsOlderThan(user.tenantId, retentionDays);
        await this.logEvent({
            tenantId: user.tenantId,
            category: audit_types_1.AuditEventCategory.COMPLIANCE_EVENT,
            operationType: 'DATA_RETENTION_POLICY_APPLIED',
            description: `Data older than ${retentionDays} days marked for archival/deletion.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { retentionDays },
        });
    }
    async archiveOldData(tenantId, archiveBeforeDate) {
        console.log(`Archiving data for tenant ${tenantId} before ${archiveBeforeDate.toISOString()}.`);
        // Example: Move old data to an archival storage
        // await this.auditRepository.archiveEventsBefore(tenantId, archiveBeforeDate);
        await this.logEvent({
            tenantId: tenantId,
            category: audit_types_1.AuditEventCategory.COMPLIANCE_EVENT,
            operationType: 'DATA_ARCHIVAL_INITIATED',
            description: `Data archival initiated for tenant ${tenantId}. Data before ${archiveBeforeDate.toISOString()} is being archived.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { archiveBeforeDate },
        });
    }
}
exports.AuditTrailService = AuditTrailService;
