import { Response, NextFunction } from 'express';
import { PaimRequest } from '../paim/paim.types';
import { PowerOpsService } from './powerops.service';
import { EntityType, CostCategory } from './powerops.types';

export class PowerOpsMiddleware {
  private powerOpsService: PowerOpsService;

  constructor() {
    this.powerOpsService = new PowerOpsService();
  }

  // Middleware to log PowerOps usage for API calls
  logApiUsage = async (req: PaimRequest, res: Response, next: NextFunction) => {
    const startTime = process.hrtime.bigint();

    res.on('finish', async () => {
      const endTime = process.hrtime.bigint();
      const durationMs = Number(endTime - startTime) / 1_000_000; // Convert nanoseconds to milliseconds

      // Determine entityId and entityType based on authentication or request context
      // For now, using a placeholder. This needs to be integrated with actual auth.
      const entityId = req.user?.userId || 'anonymous'; // Assuming req.user is populated by auth middleware
      const entityType = req.user?.userId ? EntityType.User : EntityType.PaimInstance; // Placeholder logic

      // Estimate usage units based on request size, response size, and duration
      // This is a simplified example. Real usage tracking would be more sophisticated.
      const requestSize = req.socket.bytesRead || 0;
      const responseSize = res.socket?.bytesWritten || 0;
      const usageUnits = (requestSize + responseSize) / 1024 + (durationMs / 100); // KBs + duration factor

      try {
        // Only log usage if user is authenticated
        if ((req as any).user) {
          await this.powerOpsService.logUsage((req as any).user, {
            entityId,
            entityType,
            usageUnits,
            costCategory: CostCategory.Compute, // Defaulting to compute for API calls
            description: `API Call: ${req.method} ${req.originalUrl}`,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (error) {
        console.error('Error logging PowerOps usage:', error);
        // Decide how to handle logging errors: fail silently or re-throw
      }
    });

    next();
  };

  // Middleware for other PowerOps related tasks can be added here
  // e.g., check resource limits, apply budget alerts, etc.
}