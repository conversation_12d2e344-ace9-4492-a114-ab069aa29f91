import { Router, Request, Response, NextFunction } from 'express';
import { AgentService } from './agent.service';
import {
  CreateAgentRequest,
  UpdateAgentRequest,
  AgentAssignmentRequest,
  WorkflowExecutionRequest,
  AgentQueryOptions,
} from './agent.types';
import { asyncHandler } from '../utils/asyncHandler'; // Assuming asyncHandler utility exists
import { CustomError } from '../utils/errors'; // Assuming CustomError class exists
import logger from '../config/logger'; // Assuming logger utility exists
import { requireParam, getParam } from '../utils/type-guards';
import { authorize } from '../middleware/authorization'; // Import authorization middleware
import { PERMISSIONS } from '../auth/permissions'; // Import permissions
import AgentFramework from '../agent-framework'; // Import the new agent framework
import { JwtPayload } from '../auth/auth.types'; // Import JwtPayload

export class AgentController {
  public router: Router;
  private agentService: AgentService;
  private framework: AgentFramework;

  constructor() {
    this.router = Router();
    this.agentService = new AgentService();
    this.framework = AgentFramework.getInstance();
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.get('/', authorize([PERMISSIONS.AGENT_READ]), asyncHandler(this.getAllAgents));
    this.router.post('/', authorize([PERMISSIONS.AGENT_CREATE]), asyncHandler(this.createAgent));
    this.router.get('/:agentId', authorize([PERMISSIONS.AGENT_READ]), asyncHandler(this.getAgentById));
    this.router.put('/:agentId', authorize([PERMISSIONS.AGENT_UPDATE]), asyncHandler(this.updateAgent));
    this.router.delete('/:agentId', authorize([PERMISSIONS.AGENT_DELETE]), asyncHandler(this.deleteAgent));
    this.router.post('/:agentId/assign', authorize([PERMISSIONS.AGENT_UPDATE]), asyncHandler(this.assignAgent)); // Assuming assign is an update operation
    this.router.post('/workflows/:workflowId/execute', authorize([PERMISSIONS.WORKFLOW_CREATE]), asyncHandler(this.executeWorkflow)); // Assuming execute workflow requires workflow creation permission
    this.router.get('/:agentId/performance', authorize([PERMISSIONS.AGENT_READ]), asyncHandler(this.getAgentPerformance));

    // New framework-specific routes
    this.router.get('/framework/status', authorize([PERMISSIONS.AGENT_READ]), asyncHandler(this.getFrameworkStatus));
    this.router.post('/framework/execute', authorize([PERMISSIONS.AGENT_UPDATE]), asyncHandler(this.executeFrameworkTask));
    this.router.get('/framework/agents', authorize([PERMISSIONS.AGENT_READ]), asyncHandler(this.getFrameworkAgents));
    this.router.get('/framework/agents/:agentName', authorize([PERMISSIONS.AGENT_READ]), asyncHandler(this.getFrameworkAgent));
    this.router.post('/framework/agents/:agentName/execute', authorize([PERMISSIONS.AGENT_UPDATE]), asyncHandler(this.executeAgentTask));
  }

  // GET /agents
  private async getAllAgents(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const options: AgentQueryOptions = {
      page: parseInt(getParam(req.query.page as string | undefined, '1')),
      size: parseInt(getParam(req.query.size as string | undefined, '10')),
      sort: getParam(req.query.sort as string | undefined),
      persona: getParam(req.query.persona as string | undefined),
      status: getParam(req.query.status as string | undefined) as 'active' | 'inactive' | 'busy' | 'available',
    };
    const { agents, pagination } = await this.agentService.getAllAgents(req.user, options);
    res.status(200).json({ data: agents, pagination });
  }

  // POST /agents
  private async createAgent(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const createRequest: CreateAgentRequest = req.body;
    const newAgent = await this.agentService.createAgent(req.user, createRequest);
    res.status(201).json(newAgent);
  }

  // GET /agents/:agentId
  private async getAgentById(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { agentId } = req.params;
    const agent = await this.agentService.getAgentById(req.user, requireParam(agentId, 'agentId'));
    if (!agent) {
      throw new CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
    }
    res.status(200).json(agent);
  }

  // PUT /agents/:agentId
  private async updateAgent(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { agentId } = req.params;
    const updateRequest: UpdateAgentRequest = req.body;
    const updatedAgent = await this.agentService.updateAgent(req.user, requireParam(agentId, 'agentId'), updateRequest);
    if (!updatedAgent) {
      throw new CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
    }
    res.status(200).json(updatedAgent);
  }

  // DELETE /agents/:agentId
  private async deleteAgent(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { agentId } = req.params;
    const deleted = await this.agentService.deleteAgent(req.user, requireParam(agentId, 'agentId'));
    if (!deleted) {
      throw new CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
    }
    res.status(204).send(); // No content for successful deletion
  }

  // POST /agents/:agentId/assign
  private async assignAgent(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { agentId } = req.params;
    const assignmentRequest: AgentAssignmentRequest = req.body;
    const assignment = await this.agentService.assignAgent(req.user, { ...assignmentRequest, agentId: requireParam(agentId, 'agentId') });
    res.status(200).json(assignment);
  }

  // POST /workflows/:workflowId/execute
  private async executeWorkflow(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { workflowId } = req.params;
    const executionRequest: WorkflowExecutionRequest = req.body;
    const executionStatus = await this.agentService.executeWorkflow(req.user, requireParam(workflowId, 'workflowId'), executionRequest);
    res.status(200).json(executionStatus);
  }

  // GET /agents/:agentId/performance
  private async getAgentPerformance(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { agentId } = req.params;
    const { startDate, endDate } = req.query;
    const performanceMetrics = await this.agentService.getAgentPerformance(
      req.user,
      requireParam(agentId, 'agentId'),
      getParam(startDate as string | undefined),
      getParam(endDate as string | undefined),
    );
    if (!performanceMetrics) {
      throw new CustomError(`Performance metrics for agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
    }
    res.status(200).json(performanceMetrics);
  }

  // Framework-specific endpoints

  // GET /agents/framework/status
  private async getFrameworkStatus(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }

    try {
      const status = this.framework.getStatus();
      res.status(200).json({
        success: true,
        data: status,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get framework status:', error);
      throw new CustomError('Failed to get framework status', { originalStatusCode: 500 });
    }
  }

  // POST /agents/framework/execute
  private async executeFrameworkTask(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }

    const { taskType, payload, agentName } = req.body;

    if (!taskType) {
      throw new CustomError('Task type is required', { originalStatusCode: 400 });
    }

    try {
      const result = await this.framework.executeTask(taskType, payload, agentName);
      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Framework task execution failed:', error);
      throw new CustomError(
        error instanceof Error ? error.message : 'Task execution failed',
        { originalStatusCode: 500 }
      );
    }
  }

  // GET /agents/framework/agents
  private async getFrameworkAgents(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }

    try {
      const predefinedAgents = this.framework.getPredefinedAgents();
      const agentList = Array.from(predefinedAgents.entries()).map(([name, agent]) => ({
        name,
        id: agent.id,
        type: agent.type,
        active: agent.getIsActive(),
        capabilities: agent.getCapabilities(),
        metrics: agent.getMetrics()
      }));

      res.status(200).json({
        success: true,
        data: agentList,
        count: agentList.length,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get framework agents:', error);
      throw new CustomError('Failed to get framework agents', { originalStatusCode: 500 });
    }
  }

  // GET /agents/framework/agents/:agentName
  private async getFrameworkAgent(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }

    const { agentName } = req.params;

    try {
      const agent = this.framework.getAgent(requireParam(agentName, 'agentName'));

      if (!agent) {
        throw new CustomError(`Framework agent '${agentName}' not found`, { originalStatusCode: 404 });
      }

      res.status(200).json({
        success: true,
        data: {
          name: agent.name,
          id: agent.id,
          type: agent.type,
          active: agent.getIsActive(),
          capabilities: agent.getCapabilities(),
          metrics: agent.getMetrics(),
          config: agent.getConfig().getAll()
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error(`Failed to get framework agent '${agentName}':`, error);

      if (error instanceof CustomError) {
        throw error;
      }

      throw new CustomError('Failed to get framework agent', { originalStatusCode: 500 });
    }
  }

  // POST /agents/framework/agents/:agentName/execute
  private async executeAgentTask(req: Request, res: Response) {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }

    const { agentName } = req.params;
    const { taskType, payload } = req.body;

    if (!taskType) {
      throw new CustomError('Task type is required', { originalStatusCode: 400 });
    }

    try {
      const agent = this.framework.getAgent(requireParam(agentName, 'agentName'));

      if (!agent) {
        throw new CustomError(`Framework agent '${agentName}' not found`, { originalStatusCode: 404 });
      }

      const task = {
        id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        type: taskType,
        payload: payload || {},
        timestamp: new Date()
      };

      const result = await agent.execute(task);

      res.status(200).json({
        success: true,
        data: result,
        agent: {
          name: agent.name,
          id: agent.id,
          type: agent.type
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error(`Failed to execute task on agent '${agentName}':`, error);

      if (error instanceof CustomError) {
        throw error;
      }

      throw new CustomError('Failed to execute agent task', { originalStatusCode: 500 });
    }
  }
}