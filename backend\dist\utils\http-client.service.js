"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mcpClient = exports.creativeApiClient = exports.defaultHttpClient = exports.HttpClientService = void 0;
const axios_1 = __importStar(require("axios"));
const logger_1 = __importDefault(require("../config/logger"));
// Define Circuit Breaker States
var CircuitState;
(function (CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["OPEN"] = "OPEN";
    CircuitState["HALF_OPEN"] = "HALF_OPEN";
})(CircuitState || (CircuitState = {}));
class HttpClientService {
    client;
    requestQueue = [];
    isProcessingQueue = false;
    rateLimitPerSecond;
    lastRequestTime = 0;
    requestMetrics = [];
    maxMetricsHistory = 1000;
    cache = new Map();
    pendingRequests = new Map();
    cacheEnabled;
    cacheTtl;
    deduplicationEnabled;
    circuitBreakerEnabled;
    failureThreshold;
    resetTimeout;
    successThreshold;
    circuitState;
    failureCount;
    successCount;
    lastFailureTime;
    constructor(config = {}) {
        this.rateLimitPerSecond = config.rateLimitPerSecond || 10;
        this.cacheEnabled = config.cache?.enabled || false;
        this.cacheTtl = config.cache?.ttl || 60000; // Default TTL 60 seconds
        this.deduplicationEnabled = config.deduplication || false;
        this.circuitBreakerEnabled = config.circuitBreaker?.enabled || false;
        this.failureThreshold = config.circuitBreaker?.failureThreshold || 5;
        this.resetTimeout = config.circuitBreaker?.resetTimeout || 30000; // 30 seconds
        this.successThreshold = config.circuitBreaker?.successThreshold || 3;
        this.circuitState = CircuitState.CLOSED;
        this.failureCount = 0;
        this.successCount = 0;
        this.lastFailureTime = 0;
        // Validate required environment variables
        this.validateEnvironment();
        this.client = axios_1.default.create({
            baseURL: config.baseURL ?? '', // Provide a default empty string if undefined
            timeout: config.timeout || 30000, // 30 seconds default
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'TheAIgency-Backend/1.0.0',
                ...config.headers,
            },
            // Security configurations
            maxRedirects: 5,
            maxContentLength: 50 * 1024 * 1024, // 50MB max
            maxBodyLength: 50 * 1024 * 1024,
            validateStatus: (status) => status < 500, // Don't throw on 4xx errors
        });
        this.setupInterceptors(config.retries || 3, config.retryDelay || 1000, config.maxRetries || 3, // Default maxRetries to 3
        config.retryStrategy || 'exponential');
    }
    validateEnvironment() {
        const requiredEnvVars = [
            'RUNWAYML_API_KEY',
            'ADOBE_FIREFLY_API_KEY',
        ];
        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
        if (missingVars.length > 0) {
            logger_1.default.warn(`Missing environment variables: ${missingVars.join(', ')}`);
        }
    }
    setupInterceptors(retries, retryDelay, maxRetries = 3, retryStrategy = 'exponential') {
        // Request interceptor for logging and security
        this.client.interceptors.request.use((config) => {
            if (this.circuitBreakerEnabled) {
                this.checkCircuitState();
                if (this.circuitState === CircuitState.OPEN) {
                    logger_1.default.warn('Circuit breaker is OPEN, request blocked.');
                    return Promise.reject(new axios_1.AxiosError('Circuit breaker is open', 'CIRCUIT_OPEN'));
                }
            }
            const startTime = Date.now();
            config.metadata = { startTime };
            // Security: Sanitize sensitive data from logs
            const sanitizedConfig = this.sanitizeConfigForLogging(config);
            logger_1.default.debug('HTTP Request', {
                method: config.method?.toUpperCase(),
                url: config.url,
                headers: sanitizedConfig.headers,
            });
            // Rate limiting
            return this.enforceRateLimit().then(() => config);
        }, (error) => {
            logger_1.default.error('HTTP Request Error', { error: error.message });
            return Promise.reject(error);
        });
        // Response interceptor for logging and metrics
        this.client.interceptors.response.use((response) => {
            const extendedConfig = response.config;
            const duration = Date.now() - (extendedConfig.metadata?.startTime || Date.now());
            // Record metrics
            this.recordMetrics({
                url: response.config.url || '',
                method: response.config.method?.toUpperCase() || '',
                duration,
                status: response.status,
                timestamp: new Date(),
            });
            logger_1.default.debug('HTTP Response', {
                method: response.config.method?.toUpperCase(),
                url: response.config.url,
                status: response.status,
                duration: `${duration}ms`,
            });
            // Circuit breaker: On success
            if (this.circuitBreakerEnabled) {
                this.onSuccess();
            }
            return response;
        }, async (error) => {
            const config = error.config;
            const duration = config?.metadata?.startTime ? Date.now() - config.metadata.startTime : 0;
            // Record failed request metrics
            if (config) {
                this.recordMetrics({
                    url: config.url || '',
                    method: config.method?.toUpperCase() || '',
                    duration,
                    status: error.response?.status || 0,
                    timestamp: new Date(),
                });
            }
            // Circuit breaker: On failure
            if (this.circuitBreakerEnabled) {
                this.onFailure();
            }
            // Retry logic
            if (this.shouldRetry(error, config, maxRetries, retryStrategy)) {
                const retryCount = config.__retryCount || 0;
                config.__retryCount = retryCount + 1;
                logger_1.default.warn(`Retrying request (${retryCount + 1}/${maxRetries})`, {
                    url: config?.url,
                    error: error.message,
                });
                let delayMs = 0;
                if (retryStrategy === 'exponential') {
                    delayMs = retryDelay * Math.pow(2, retryCount) + Math.random() * 100; // Jittered exponential backoff
                }
                else if (retryStrategy === 'linear') {
                    delayMs = retryDelay * (retryCount + 1) + Math.random() * 100; // Jittered linear backoff
                }
                await this.delay(delayMs);
                return this.client(config);
            }
            logger_1.default.error('HTTP Response Error', {
                method: config?.method?.toUpperCase(),
                url: config?.url,
                status: error.response?.status,
                message: error.message,
                duration: `${duration}ms`,
            });
            return Promise.reject(error);
        });
    }
    sanitizeConfigForLogging(config) {
        const sanitized = { ...config };
        // Remove sensitive headers (case-insensitive)
        if (sanitized.headers && typeof sanitized.headers === 'object') {
            const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie'];
            const headerKeys = Object.keys(sanitized.headers);
            sensitiveHeaders.forEach(sensitiveHeader => {
                headerKeys.forEach(headerKey => {
                    if (headerKey.toLowerCase() === sensitiveHeader.toLowerCase() && sanitized.headers) {
                        sanitized.headers[headerKey] = '[REDACTED]';
                    }
                });
            });
        }
        return sanitized;
    }
    shouldRetry(error, config, maxRetries, retryStrategy) {
        if (retryStrategy === 'none')
            return false;
        const retryCount = config?.__retryCount || 0;
        if (retryCount >= maxRetries)
            return false;
        // Retry on network errors (no response)
        if (!error.response) {
            logger_1.default.debug(`Retrying due to network error: ${error.message}`);
            return true;
        }
        // Retry on specific status codes
        const status = error.response.status;
        const retryableStatuses = [408, 429, 500, 502, 503, 504];
        if (retryableStatuses.includes(status)) {
            logger_1.default.debug(`Retrying due to retryable status code: ${status}`);
            return true;
        }
        // Optionally, retry on specific error codes from Axios
        if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
            logger_1.default.debug(`Retrying due to Axios error code: ${error.code}`);
            return true;
        }
        return false;
    }
    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = 1000 / this.rateLimitPerSecond;
        if (timeSinceLastRequest < minInterval) {
            await this.delay(minInterval - timeSinceLastRequest);
        }
        this.lastRequestTime = Date.now();
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    recordMetrics(metrics) {
        this.requestMetrics.push(metrics);
        // Keep only recent metrics to prevent memory leaks
        if (this.requestMetrics.length > this.maxMetricsHistory) {
            this.requestMetrics = this.requestMetrics.slice(-this.maxMetricsHistory);
        }
    }
    // Public methods
    async get(url, config) {
        const requestKey = this.getRequestKey(url, config);
        // Deduplication
        if (this.deduplicationEnabled && this.pendingRequests.has(requestKey)) {
            logger_1.default.debug(`Deduplicating request for ${url}`);
            return this.pendingRequests.get(requestKey);
        }
        // Caching
        if (this.cacheEnabled) {
            const cachedResponse = this.getCache(requestKey);
            if (cachedResponse) {
                logger_1.default.debug(`Returning cached response for ${url}`);
                return Promise.resolve(cachedResponse);
            }
        }
        const requestPromise = this.client.get(url, config);
        if (this.deduplicationEnabled) {
            this.pendingRequests.set(requestKey, requestPromise);
        }
        try {
            const response = await requestPromise;
            if (this.cacheEnabled) {
                this.setCache(requestKey, response);
            }
            return response;
        }
        finally {
            if (this.deduplicationEnabled) {
                this.pendingRequests.delete(requestKey);
            }
        }
    }
    async post(url, data, config) {
        return this.client.post(url, data, config);
    }
    async put(url, data, config) {
        return this.client.put(url, data, config);
    }
    async delete(url, config) {
        return this.client.delete(url, config);
    }
    async patch(url, data, config) {
        return this.client.patch(url, data, config);
    }
    // Utility methods
    getMetrics() {
        return [...this.requestMetrics];
    }
    getAverageResponseTime(minutes = 5) {
        const cutoff = new Date(Date.now() - minutes * 60 * 1000);
        const recentMetrics = this.requestMetrics.filter(m => m.timestamp > cutoff);
        if (recentMetrics.length === 0)
            return 0;
        const totalDuration = recentMetrics.reduce((sum, m) => sum + m.duration, 0);
        return totalDuration / recentMetrics.length;
    }
    getErrorRate(minutes = 5) {
        const cutoff = new Date(Date.now() - minutes * 60 * 1000);
        const recentMetrics = this.requestMetrics.filter(m => m.timestamp > cutoff);
        if (recentMetrics.length === 0)
            return 0;
        const errorCount = recentMetrics.filter(m => m.status >= 400).length;
        return (errorCount / recentMetrics.length) * 100;
    }
    // Cleanup method
    destroy() {
        this.requestMetrics = [];
        this.requestQueue = [];
        this.cache.clear();
        this.pendingRequests.clear();
    }
    getRequestKey(url, config) {
        // Create a consistent key for caching and deduplication
        return JSON.stringify({
            url,
            params: config?.params,
            headers: config?.headers, // Include headers in key if they affect the response
        });
    }
    getCache(key) {
        const entry = this.cache.get(key);
        if (entry && (Date.now() - entry.timestamp < this.cacheTtl)) {
            return entry.data;
        }
        this.cache.delete(key); // Invalidate expired cache
        return undefined;
    }
    setCache(key, response) {
        this.cache.set(key, { data: response, timestamp: Date.now() });
    }
    // Circuit Breaker Logic
    checkCircuitState() {
        if (this.circuitState === CircuitState.HALF_OPEN && (Date.now() - this.lastFailureTime > this.resetTimeout)) {
            logger_1.default.info('Circuit breaker transitioning to HALF_OPEN.');
            this.circuitState = CircuitState.HALF_OPEN;
            this.successCount = 0; // Reset success count for HALF_OPEN
        }
        else if (this.circuitState === CircuitState.OPEN && (Date.now() - this.lastFailureTime > this.resetTimeout)) {
            logger_1.default.info('Circuit breaker transitioning to HALF_OPEN after timeout.');
            this.circuitState = CircuitState.HALF_OPEN;
            this.successCount = 0; // Reset success count for HALF_OPEN
        }
    }
    onSuccess() {
        if (this.circuitState === CircuitState.CLOSED) {
            this.failureCount = 0; // Reset failure count on success
        }
        else if (this.circuitState === CircuitState.HALF_OPEN) {
            this.successCount++;
            if (this.successCount >= this.successThreshold) {
                logger_1.default.info('Circuit breaker transitioning to CLOSED due to consecutive successes.');
                this.circuitState = CircuitState.CLOSED;
                this.failureCount = 0;
                this.successCount = 0;
            }
        }
    }
    onFailure() {
        if (this.circuitState === CircuitState.CLOSED) {
            this.failureCount++;
            this.lastFailureTime = Date.now();
            if (this.failureCount >= this.failureThreshold) {
                logger_1.default.warn('Circuit breaker transitioning to OPEN due to consecutive failures.');
                this.circuitState = CircuitState.OPEN;
            }
        }
        else if (this.circuitState === CircuitState.HALF_OPEN) {
            logger_1.default.warn('Circuit breaker transitioning back to OPEN due to failure in HALF_OPEN state.');
            this.circuitState = CircuitState.OPEN;
            this.lastFailureTime = Date.now();
        }
    }
}
exports.HttpClientService = HttpClientService;
// Singleton instances for different use cases
exports.defaultHttpClient = new HttpClientService({
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
    rateLimitPerSecond: 10,
});
exports.creativeApiClient = new HttpClientService({
    timeout: 60000, // Longer timeout for creative APIs
    retries: 2,
    retryDelay: 2000,
    rateLimitPerSecond: 5, // More conservative for expensive APIs
    headers: {
        'Accept': 'application/json',
    },
});
exports.mcpClient = new HttpClientService({
    timeout: 15000,
    retries: 3,
    retryDelay: 1000,
    rateLimitPerSecond: 20,
    headers: {
        'Accept-Language': 'ar, en;q=0.9',
    },
});
