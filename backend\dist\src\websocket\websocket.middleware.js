"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizeWebSocket = exports.authenticateWebSocket = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const authenticateWebSocket = (ws, req, next) => {
    const token = req.headers['sec-websocket-protocol']; // Or from query params, depending on client
    if (!token) {
        ws.close(1008, 'Authentication token missing');
        return;
    }
    try {
        // Replace 'YOUR_JWT_SECRET' with your actual JWT secret from config
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'YOUR_JWT_SECRET');
        ws.userId = decoded.userId;
        ws.isAuthenticated = true;
        next();
    }
    catch (error) {
        console.error('WebSocket authentication failed:', error);
        ws.close(1008, 'Authentication failed');
    }
};
exports.authenticateWebSocket = authenticateWebSocket;
const authorizeWebSocket = (requiredRoles) => {
    return (ws, req, next) => {
        // This is a placeholder. In a real app, you'd fetch user roles from DB
        // based on ws.userId and check against requiredRoles.
        // For now, we'll just assume authenticated users are authorized.
        if (ws.isAuthenticated) {
            console.log(`User ${ws.userId} is authorized for WebSocket connection.`);
            next();
        }
        else {
            ws.close(1008, 'Authorization failed: User not authenticated');
        }
    };
};
exports.authorizeWebSocket = authorizeWebSocket;
