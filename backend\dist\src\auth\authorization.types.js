"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityType = exports.PERMISSIONS = void 0;
// backend/src/auth/authorization.types.ts
var permissions_1 = require("./permissions");
Object.defineProperty(exports, "PERMISSIONS", { enumerable: true, get: function () { return permissions_1.PERMISSIONS; } });
var powerops_types_1 = require("../powerops/powerops.types");
Object.defineProperty(exports, "EntityType", { enumerable: true, get: function () { return powerops_types_1.EntityType; } });
