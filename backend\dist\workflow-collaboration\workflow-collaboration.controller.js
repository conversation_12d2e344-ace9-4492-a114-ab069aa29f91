"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowCollaborationController = void 0;
const asyncHandler_1 = require("../utils/asyncHandler");
const type_guards_1 = require("../utils/type-guards");
class WorkflowCollaborationController {
    workflowCollaborationService;
    constructor(workflowCollaborationService) {
        this.workflowCollaborationService = workflowCollaborationService;
    }
    // Workflows
    getAllWorkflows = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { status, page, size, sort } = req.query;
        const workflows = await this.workflowCollaborationService.getAllWorkflows((0, type_guards_1.getParam)(status), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
        res.status(200).json(workflows);
    });
    createWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const workflowData = req.body;
        const newWorkflow = await this.workflowCollaborationService.createWorkflow(workflowData);
        res.status(201).json(newWorkflow);
    });
    getWorkflowById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { workflowId } = req.params;
        const workflow = await this.workflowCollaborationService.getWorkflowById((0, type_guards_1.requireParam)(workflowId, 'workflowId'));
        res.status(200).json(workflow);
    });
    updateWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { workflowId } = req.params;
        const workflowData = req.body;
        const updatedWorkflow = await this.workflowCollaborationService.updateWorkflow((0, type_guards_1.requireParam)(workflowId, 'workflowId'), workflowData);
        res.status(200).json(updatedWorkflow);
    });
    deleteWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { workflowId } = req.params;
        await this.workflowCollaborationService.deleteWorkflow((0, type_guards_1.requireParam)(workflowId, 'workflowId'));
        res.status(204).send();
    });
    // Tasks
    getAllTasks = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { status, assignedTo, page, size, sort } = req.query;
        const tasks = await this.workflowCollaborationService.getAllTasks((0, type_guards_1.getParam)(status), (0, type_guards_1.getParam)(assignedTo), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
        res.status(200).json(tasks);
    });
    createTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const taskData = req.body;
        const newTask = await this.workflowCollaborationService.createTask(taskData);
        res.status(201).json(newTask);
    });
    getTaskById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { taskId } = req.params;
        const task = await this.workflowCollaborationService.getTaskById((0, type_guards_1.requireParam)(taskId, 'taskId'));
        res.status(200).json(task);
    });
    updateTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { taskId } = req.params;
        const taskData = req.body;
        const updatedTask = await this.workflowCollaborationService.updateTask((0, type_guards_1.requireParam)(taskId, 'taskId'), taskData);
        res.status(200).json(updatedTask);
    });
    deleteTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { taskId } = req.params;
        await this.workflowCollaborationService.deleteTask((0, type_guards_1.requireParam)(taskId, 'taskId'));
        res.status(204).send();
    });
    // Collaboration Sessions
    startCollaborationSession = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const sessionData = req.body;
        const newSession = await this.workflowCollaborationService.startCollaborationSession(sessionData);
        res.status(201).json(newSession);
    });
    joinCollaborationSession = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { sessionId } = req.params;
        const { userId } = req.body;
        const updatedSession = await this.workflowCollaborationService.joinCollaborationSession((0, type_guards_1.requireParam)(sessionId, 'sessionId'), (0, type_guards_1.requireParam)(userId, 'userId'));
        res.status(200).json(updatedSession);
    });
    leaveCollaborationSession = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { sessionId } = req.params;
        const { userId } = req.body;
        await this.workflowCollaborationService.leaveCollaborationSession((0, type_guards_1.requireParam)(sessionId, 'sessionId'), (0, type_guards_1.requireParam)(userId, 'userId'));
        res.status(204).send();
    });
    // Cross-Tenant Communication
    sendCrossTenantMessage = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const messageData = req.body;
        const result = await this.workflowCollaborationService.sendCrossTenantMessage(messageData);
        res.status(200).json(result);
    });
    // Notifications
    getAllNotifications = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { userId, read, page, size, sort } = req.query;
        const notifications = await this.workflowCollaborationService.getAllNotifications((0, type_guards_1.getParam)(userId), (0, type_guards_1.getParam)(read), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
        res.status(200).json(notifications);
    });
    markNotificationAsRead = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { notificationId } = req.params;
        await this.workflowCollaborationService.markNotificationAsRead((0, type_guards_1.requireParam)(notificationId, 'notificationId'));
        res.status(204).send();
    });
    // Workflow Sharing
    shareWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const shareData = req.body;
        const result = await this.workflowCollaborationService.shareWorkflow(shareData);
        res.status(200).json(result);
    });
    deleteWorkflowShare = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { workflowId, permissionId } = req.params;
        await this.workflowCollaborationService.deleteWorkflowShare((0, type_guards_1.requireParam)(workflowId, 'workflowId'), (0, type_guards_1.requireParam)(permissionId, 'permissionId'));
        res.status(204).send();
    });
    // Task Delegation
    delegateTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { taskId } = req.params;
        const delegateData = req.body;
        const result = await this.workflowCollaborationService.delegateTask((0, type_guards_1.requireParam)(taskId, 'taskId'), delegateData);
        res.status(200).json(result);
    });
    // Collaborative Workspace
    getAllWorkspaces = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { ownerId, paimInstanceId, page, size, sort } = req.query;
        const workspaces = await this.workflowCollaborationService.getAllWorkspaces((0, type_guards_1.getParam)(ownerId), (0, type_guards_1.getParam)(paimInstanceId), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
        res.status(200).json(workspaces);
    });
    createWorkspace = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const workspaceData = req.body;
        const newWorkspace = await this.workflowCollaborationService.createWorkspace(workspaceData);
        res.status(201).json(newWorkspace);
    });
    getWorkspaceById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { workspaceId } = req.params;
        const workspace = await this.workflowCollaborationService.getWorkspaceById((0, type_guards_1.requireParam)(workspaceId, 'workspaceId'));
        res.status(200).json(workspace);
    });
    updateWorkspace = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { workspaceId } = req.params;
        const workspaceData = req.body;
        const updatedWorkspace = await this.workflowCollaborationService.updateWorkspace((0, type_guards_1.requireParam)(workspaceId, 'workspaceId'), workspaceData);
        res.status(200).json(updatedWorkspace);
    });
    deleteWorkspace = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { workspaceId } = req.params;
        await this.workflowCollaborationService.deleteWorkspace((0, type_guards_1.requireParam)(workspaceId, 'workspaceId'));
        res.status(204).send();
    });
    // Team Coordination
    getAllTeams = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { paimInstanceId, page, size, sort } = req.query;
        const teams = await this.workflowCollaborationService.getAllTeams((0, type_guards_1.getParam)(paimInstanceId), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
        res.status(200).json(teams);
    });
    createTeam = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const teamData = req.body;
        const newTeam = await this.workflowCollaborationService.createTeam(teamData);
        res.status(201).json(newTeam);
    });
    getTeamById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { teamId } = req.params;
        const team = await this.workflowCollaborationService.getTeamById((0, type_guards_1.requireParam)(teamId, 'teamId'));
        res.status(200).json(team);
    });
    updateTeam = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { teamId } = req.params;
        const teamData = req.body;
        const updatedTeam = await this.workflowCollaborationService.updateTeam((0, type_guards_1.requireParam)(teamId, 'teamId'), teamData);
        res.status(200).json(updatedTeam);
    });
    deleteTeam = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
        const { teamId } = req.params;
        await this.workflowCollaborationService.deleteTeam((0, type_guards_1.requireParam)(teamId, 'teamId'));
        res.status(204).send();
    });
}
exports.WorkflowCollaborationController = WorkflowCollaborationController;
