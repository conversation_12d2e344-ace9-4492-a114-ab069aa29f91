"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CostCategory = exports.EntityType = void 0;
var EntityType;
(function (EntityType) {
    EntityType["User"] = "user";
    EntityType["PaimInstance"] = "paim_instance";
    EntityType["Organization"] = "organization";
    EntityType["Team"] = "team";
})(EntityType || (exports.EntityType = EntityType = {}));
var CostCategory;
(function (CostCategory) {
    CostCategory["Compute"] = "compute";
    CostCategory["Storage"] = "storage";
    CostCategory["Bandwidth"] = "bandwidth";
    CostCategory["AIModel"] = "ai_model";
    CostCategory["Other"] = "other";
})(CostCategory || (exports.CostCategory = CostCategory = {}));
