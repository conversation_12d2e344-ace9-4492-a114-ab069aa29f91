"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const appwrite_adapter_1 = require("../database/adapters/appwrite-adapter");
const auth_utils_1 = require("./auth.utils");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const logger_1 = __importDefault(require("../config/logger"));
const errors_1 = require("../utils/errors");
class AuthService {
    db;
    constructor(databaseAdapter) {
        this.db = databaseAdapter || new appwrite_adapter_1.AppwriteAdapter();
    }
    async register(userData) {
        try {
            // Validate input
            if (!userData.email || !userData.password) {
                throw new errors_1.ValidationError('Email and password are required');
            }
            // Check if user already exists
            const existingUser = await this.db.getUserByEmail(userData.email);
            if (existingUser) {
                throw new errors_1.ValidationError('User already exists with this email');
            }
            // Create user
            const user = await this.db.createUser(userData);
            // Generate tokens
            const tokens = await this.generateTokens(user);
            // Create session
            await this.db.createSession(user.user_id, {
                loginTime: new Date().toISOString(),
                userAgent: 'backend-service'
            });
            logger_1.default.info('User registered successfully', {
                userId: user.user_id,
                email: user.email
            });
            return { user: this.sanitizeUser(user), tokens };
        }
        catch (error) {
            logger_1.default.error('User registration failed', {
                email: userData.email,
                error: error.message
            });
            throw error;
        }
    }
    async login(credentials) {
        try {
            // Validate input
            if (!credentials.email || !credentials.password) {
                throw new errors_1.ValidationError('Email and password are required');
            }
            // Get user by email
            const user = await this.db.getUserByEmail(credentials.email);
            if (!user) {
                throw new errors_1.AuthenticationError('Invalid credentials');
            }
            // Verify password
            const isValidPassword = await bcryptjs_1.default.compare(credentials.password, user.password_hash || ''); // Use any to bypass type check
            if (!isValidPassword) {
                throw new errors_1.AuthenticationError('Invalid credentials');
            }
            // Generate tokens
            const tokens = await this.generateTokens(user);
            // Create session
            await this.db.createSession(user.user_id, {
                loginTime: new Date().toISOString(),
                userAgent: 'backend-service'
            });
            logger_1.default.info('User logged in successfully', {
                userId: user.user_id,
                email: user.email
            });
            return { user: this.sanitizeUser(user), tokens };
        }
        catch (error) {
            logger_1.default.error('User login failed', {
                email: credentials.email,
                error: error.message
            });
            throw error;
        }
    }
    async verifyToken(token) {
        try {
            const payload = (0, auth_utils_1.verifyToken)(token);
            // Verify user still exists
            const user = await this.db.getUserById(payload.userId);
            if (!user) {
                throw new errors_1.AuthenticationError('User not found');
            }
            return payload;
        }
        catch (error) {
            logger_1.default.error('Token verification failed', { error: error.message });
            throw new errors_1.AuthenticationError('Invalid token');
        }
    }
    async refreshToken(refreshToken) {
        try {
            const payload = (0, auth_utils_1.verifyToken)(refreshToken);
            // Verify user still exists
            const user = await this.db.getUserById(payload.userId);
            if (!user) {
                throw new errors_1.AuthenticationError('User not found');
            }
            // Generate new tokens
            return this.generateTokens(user);
        }
        catch (error) {
            logger_1.default.error('Token refresh failed', { error: error.message });
            throw new errors_1.AuthenticationError('Invalid refresh token');
        }
    }
    async logout(userId, sessionId) {
        try {
            if (sessionId) {
                await this.db.deleteSession(sessionId);
            }
            logger_1.default.info('User logged out successfully', { userId });
        }
        catch (error) {
            logger_1.default.error('Logout failed', { userId, error: error.message });
            throw error;
        }
    }
    async healthCheck() {
        return this.db.healthCheck();
    }
    async generateTokens(user) {
        const payload = {
            userId: user.user_id,
            tenantId: user.tenant_id || '',
            email: user.email,
            paimTier: user.paim_tier || 'basic',
            roles: user.roles || ['user'],
        };
        // Call generateTokens from auth.utils with correct arguments
        const { accessToken, refreshToken, expiresIn } = (0, auth_utils_1.generateTokens)(user, // Pass the user object
        payload.tenantId, // Pass tenantId from payload
        payload.roles // Pass roles from payload
        );
        return {
            accessToken,
            refreshToken,
            expiresIn, // Use expiresIn from the returned AuthToken
        };
    }
    sanitizeUser(user) {
        const { password, ...sanitizedUser } = user;
        return sanitizedUser;
    }
}
exports.AuthService = AuthService;
