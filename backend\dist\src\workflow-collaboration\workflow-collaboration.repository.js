"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowCollaborationRepository = void 0;
const db_1 = __importDefault(require("../database/db")); // Assuming db instance is exported from here
const uuid_1 = require("uuid"); // Import uuid for generating IDs
class WorkflowCollaborationRepository {
    workflowTableName = 'Workflows'; // Assuming a table named Workflows
    taskTableName = 'Tasks'; // Assuming a table named Tasks
    collaborationSessionTableName = 'CollaborationSessions'; // Assuming a table named CollaborationSessions
    crossTenantMessageTableName = 'CrossTenantMessages'; // Assuming a table named CrossTenantMessages
    notificationTableName = 'Notifications'; // Assuming a table named Notifications
    workflowShareTableName = 'WorkflowShares'; // Assuming a table named WorkflowShares
    workspaceTableName = 'Workspaces'; // Assuming a table named Workspaces
    teamTableName = 'Teams'; // Assuming a table named Teams
    // Workflows
    async getWorkflows(options) {
        let query = (0, db_1.default)(this.workflowTableName);
        if (options.status) {
            query = query.where({ status: options.status });
        }
        const totalResult = await query.clone().count('* as count').first();
        const total = parseInt(totalResult.count, 10);
        const page = options.page || 1;
        const size = options.size || 10;
        const offset = (page - 1) * size;
        const [sortBy, sortDirection] = options.sort ? options.sort.split(',') : ['created_at', 'desc']; // Use 'created_at' as default
        const workflowsDB = await query
            .select('*')
            .offset(offset)
            .limit(size)
            .orderBy(sortBy, sortDirection); // Explicitly cast to string
        const workflows = workflowsDB.map(this.mapDbToWorkflow);
        return { workflows, total }; // Changed return format
    }
    async createWorkflow(workflowData) {
        const newWorkflow = {
            id: (0, uuid_1.v4)(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            name: workflowData.name,
            description: workflowData.description || undefined, // Explicitly set to undefined if not present
            ownerId: workflowData.ownerId,
            paimInstanceId: workflowData.paimInstanceId,
            status: workflowData.status || 'draft',
            definition: workflowData.definition,
        };
        await (0, db_1.default)(this.workflowTableName).insert({
            workflow_id: newWorkflow.id,
            name: newWorkflow.name,
            description: newWorkflow.description,
            owner_id: newWorkflow.ownerId,
            paim_instance_id: newWorkflow.paimInstanceId, // Added paim_instance_id
            status: newWorkflow.status,
            definition: JSON.stringify(newWorkflow.definition), // Store definition as JSON
            created_at: newWorkflow.createdAt,
            updated_at: newWorkflow.updatedAt,
        });
        return newWorkflow;
    }
    async getWorkflowById(workflowId) {
        const workflowDB = await (0, db_1.default)(this.workflowTableName)
            .where({ workflow_id: workflowId })
            .select('*')
            .first();
        if (!workflowDB) {
            return undefined;
        }
        return this.mapDbToWorkflow(workflowDB);
    }
    async updateWorkflow(workflowId, workflowData) {
        const updateData = {
            updated_at: new Date().toISOString(),
        };
        if (workflowData.name)
            updateData.name = workflowData.name;
        if (workflowData.description)
            updateData.description = workflowData.description;
        if (workflowData.status)
            updateData.status = workflowData.status;
        if (workflowData.definition)
            updateData.definition = JSON.stringify(workflowData.definition); // Update definition as JSON
        const updatedRows = await (0, db_1.default)(this.workflowTableName)
            .where({ workflow_id: workflowId })
            .update(updateData);
        if (updatedRows === 0) {
            return undefined;
        }
        return this.getWorkflowById(workflowId);
    }
    async deleteWorkflow(workflowId) {
        const deletedRows = await (0, db_1.default)(this.workflowTableName)
            .where({ workflow_id: workflowId })
            .del();
        return deletedRows > 0;
    }
    // Tasks
    async getTasks(options) {
        let query = (0, db_1.default)(this.taskTableName);
        if (options.status) {
            query = query.where({ status: options.status });
        }
        if (options.assignedTo) {
            query = query.where({ assigned_to: options.assignedTo });
        }
        const totalResult = await query.clone().count('* as count').first();
        const total = parseInt(totalResult.count, 10);
        const page = options.page || 1;
        const size = options.size || 10;
        const offset = (page - 1) * size;
        const [sortBy, sortDirection] = options.sort ? options.sort.split(',') : ['created_at', 'desc']; // Use 'created_at' as default
        const tasksDB = await query
            .select('*')
            .offset(offset)
            .limit(size)
            .orderBy(sortBy, sortDirection); // Explicitly cast to string
        const tasks = tasksDB.map(this.mapDbToTask);
        return { tasks, total }; // Changed return format
    }
    async createTask(taskData) {
        const newTask = {
            id: (0, uuid_1.v4)(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            title: taskData.title,
            description: taskData.description || undefined, // Explicitly set to undefined if not present
            status: taskData.status || 'pending',
            priority: taskData.priority,
            assignedToId: taskData.assignedToId,
            assignedToType: taskData.assignedToType,
            dueDate: taskData.dueDate,
            workflowId: taskData.workflowId,
        };
        await (0, db_1.default)(this.taskTableName).insert({
            task_id: newTask.id,
            workflow_id: newTask.workflowId,
            title: newTask.title, // Changed from name to title
            description: newTask.description,
            assigned_to_id: newTask.assignedToId, // Changed from assigned_to to assigned_to_id
            assigned_to_type: newTask.assignedToType, // Added assigned_to_type
            status: newTask.status,
            priority: newTask.priority, // Added priority
            due_date: newTask.dueDate,
            created_at: newTask.createdAt,
            updated_at: newTask.updatedAt,
        });
        return newTask;
    }
    async getTaskById(taskId) {
        const taskDB = await (0, db_1.default)(this.taskTableName)
            .where({ task_id: taskId })
            .select('*')
            .first();
        if (!taskDB) {
            return undefined;
        }
        return this.mapDbToTask(taskDB);
    }
    async updateTask(taskId, taskData) {
        const updateData = {
            updated_at: new Date().toISOString(),
        };
        if (taskData.title)
            updateData.title = taskData.title; // Changed from name to title
        if (taskData.description)
            updateData.description = taskData.description;
        if (taskData.status)
            updateData.status = taskData.status;
        if (taskData.priority)
            updateData.priority = taskData.priority; // Added priority
        if (taskData.assignedToId)
            updateData.assigned_to_id = taskData.assignedToId; // Changed from assigned_to to assigned_to_id
        if (taskData.assignedToType)
            updateData.assigned_to_type = taskData.assignedToType; // Added assigned_to_type
        if (taskData.dueDate)
            updateData.due_date = taskData.dueDate;
        const updatedRows = await (0, db_1.default)(this.taskTableName)
            .where({ task_id: taskId })
            .update(updateData);
        if (updatedRows === 0) {
            return undefined;
        }
        return this.getTaskById(taskId);
    }
    async deleteTask(taskId) {
        const deletedRows = await (0, db_1.default)(this.taskTableName)
            .where({ task_id: taskId })
            .del();
        return deletedRows > 0;
    }
    // Collaboration Sessions
    async createCollaborationSession(sessionData) {
        const newSession = {
            id: (0, uuid_1.v4)(),
            createdAt: new Date().toISOString(),
            name: sessionData.name,
            description: sessionData.description || undefined, // Explicitly set to undefined if not present
            ownerId: sessionData.ownerId,
            paimInstanceId: sessionData.paimInstanceId,
            participants: sessionData.initialParticipants ? sessionData.initialParticipants.map(userId => ({ userId, joinedAt: new Date().toISOString() })) : [],
            status: 'active',
            workflowId: sessionData.workflowId || undefined, // Added workflowId
            sessionType: sessionData.sessionType || undefined, // Added sessionType
        };
        await (0, db_1.default)(this.collaborationSessionTableName).insert({
            session_id: newSession.id,
            name: newSession.name,
            description: newSession.description,
            owner_id: newSession.ownerId,
            paim_instance_id: newSession.paimInstanceId,
            status: newSession.status,
            created_at: newSession.createdAt,
            workflow_id: newSession.workflowId, // Added workflow_id
            session_type: newSession.sessionType, // Added session_type
            initial_participants: JSON.stringify(newSession.participants.map(p => p.userId)),
        });
        return newSession;
    }
    async addParticipantToSession(sessionId, userId) {
        const session = await this.getCollaborationSessionById(sessionId);
        if (!session) {
            return undefined;
        }
        const updatedParticipants = [...session.participants, { userId, joinedAt: new Date().toISOString() }];
        await (0, db_1.default)(this.collaborationSessionTableName)
            .where({ session_id: sessionId })
            .update({ participants: JSON.stringify(updatedParticipants) });
        return this.getCollaborationSessionById(sessionId);
    }
    async removeParticipantFromSession(sessionId, userId) {
        const session = await this.getCollaborationSessionById(sessionId);
        if (!session) {
            return false;
        }
        const updatedParticipants = session.participants.filter(p => p.userId !== userId);
        const updatedRows = await (0, db_1.default)(this.collaborationSessionTableName)
            .where({ session_id: sessionId })
            .update({ participants: JSON.stringify(updatedParticipants) });
        return updatedRows > 0;
    }
    async getCollaborationSessionById(sessionId) {
        const sessionDB = await (0, db_1.default)(this.collaborationSessionTableName)
            .where({ session_id: sessionId })
            .select('*')
            .first();
        if (!sessionDB) {
            return undefined;
        }
        return this.mapDbToCollaborationSession(sessionDB);
    }
    // Cross-Tenant Communication
    async createCrossTenantMessage(messageData) {
        const newMessageId = (0, uuid_1.v4)();
        await (0, db_1.default)(this.crossTenantMessageTableName).insert({
            message_id: newMessageId,
            sender_paim_instance_id: messageData.senderPaimInstanceId, // Corrected property name
            recipient_paim_instance_id: messageData.recipientPaimInstanceId, // Corrected property name
            message_content: messageData.messageContent,
            sent_at: new Date().toISOString(),
            status: 'sent',
        });
        return { messageId: newMessageId, status: 'sent' };
    }
    // Notifications
    async getNotifications(options) {
        let query = (0, db_1.default)(this.notificationTableName);
        if (options.userId) {
            query = query.where({ user_id: options.userId });
        }
        if (options.read !== undefined) {
            query = query.where({ read: options.read });
        }
        const totalResult = await query.clone().count('* as count').first();
        const total = parseInt(totalResult.count, 10);
        const page = options.page || 1;
        const size = options.size || 10;
        const offset = (page - 1) * size;
        const [sortBy, sortDirection] = options.sort ? options.sort.split(',') : ['created_at', 'desc']; // Use 'created_at' as default
        const notificationsDB = await query
            .select('*')
            .offset(offset)
            .limit(size)
            .orderBy(sortBy, sortDirection); // Explicitly cast to string
        const notifications = notificationsDB.map(this.mapDbToNotification);
        return { notifications, total }; // Changed return format
    }
    async createNotification(notificationData) {
        const newNotification = {
            id: (0, uuid_1.v4)(),
            createdAt: new Date().toISOString(),
            read: false,
            userId: notificationData.userId,
            type: notificationData.type,
            message: notificationData.message,
        };
        await (0, db_1.default)(this.notificationTableName).insert({
            notification_id: newNotification.id,
            user_id: newNotification.userId,
            type: newNotification.type,
            message: newNotification.message,
            read: newNotification.read,
            created_at: newNotification.createdAt,
        });
        return newNotification;
    }
    async markNotificationAsRead(notificationId) {
        const updatedRows = await (0, db_1.default)(this.notificationTableName)
            .where({ notification_id: notificationId })
            .update({ read: true, updated_at: new Date().toISOString() });
        return updatedRows > 0;
    }
    // Workflow Sharing
    async shareWorkflow(shareData) {
        const newShareId = (0, uuid_1.v4)();
        await (0, db_1.default)(this.workflowShareTableName).insert({
            share_id: newShareId,
            workflow_id: shareData.workflowId,
            shared_with_id: shareData.sharedWithId, // Corrected property name
            shared_with_entity_type: shareData.sharedWithEntityType, // Added shared_with_entity_type
            permission_level: shareData.permissionLevel,
            shared_at: new Date().toISOString(),
        });
        return { success: true, shareId: newShareId };
    }
    async deleteWorkflowShare(workflowId, sharedWithId) {
        const deletedRows = await (0, db_1.default)(this.workflowShareTableName)
            .where({ workflow_id: workflowId, shared_with_id: sharedWithId }) // Changed column name
            .del();
        return deletedRows > 0;
    }
    // Task Delegation
    async delegateTask(taskId, delegateData) {
        const updatedRows = await (0, db_1.default)(this.taskTableName)
            .where({ task_id: taskId })
            .update({
            assigned_to_id: delegateData.delegateToId, // Corrected property name
            assigned_to_type: delegateData.delegateToEntityType, // Added assigned_to_type
            updated_at: new Date().toISOString(),
        });
        return updatedRows > 0;
    }
    // Collaborative Workspace
    async getWorkspaces(options) {
        let query = (0, db_1.default)(this.workspaceTableName);
        if (options.ownerId) {
            query = query.where({ owner_id: options.ownerId });
        }
        if (options.paimInstanceId) {
            query = query.where({ paim_instance_id: options.paimInstanceId });
        }
        const totalResult = await query.clone().count('* as count').first();
        const total = parseInt(totalResult.count, 10);
        const page = options.page || 1;
        const size = options.size || 10;
        const offset = (page - 1) * size;
        const [sortBy, sortDirection] = options.sort ? options.sort.split(',') : ['created_at', 'desc']; // Use 'created_at' as default
        const workspacesDB = await query
            .select('*')
            .offset(offset)
            .limit(size)
            .orderBy(sortBy, sortDirection); // Explicitly cast to string
        const workspaces = workspacesDB.map(this.mapDbToWorkspace);
        return { workspaces, total }; // Changed return format
    }
    async createWorkspace(workspaceData) {
        const newWorkspace = {
            id: (0, uuid_1.v4)(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            name: workspaceData.name,
            description: workspaceData.description || undefined, // Explicitly set to undefined if not present
            ownerId: workspaceData.ownerId,
            paimInstanceId: workspaceData.paimInstanceId,
            members: workspaceData.initialMembers || [],
        };
        await (0, db_1.default)(this.workspaceTableName).insert({
            workspace_id: newWorkspace.id,
            name: newWorkspace.name,
            description: newWorkspace.description,
            owner_id: newWorkspace.ownerId,
            paim_instance_id: newWorkspace.paimInstanceId,
            created_at: newWorkspace.createdAt,
            updated_at: newWorkspace.updatedAt,
            initial_members: JSON.stringify(newWorkspace.members),
        });
        return newWorkspace;
    }
    async getWorkspaceById(workspaceId) {
        const workspaceDB = await (0, db_1.default)(this.workspaceTableName)
            .where({ workspace_id: workspaceId })
            .select('*')
            .first();
        if (!workspaceDB) {
            return undefined;
        }
        return this.mapDbToWorkspace(workspaceDB);
    }
    async updateWorkspace(workspaceId, workspaceData) {
        const updateData = {
            updated_at: new Date().toISOString(),
        };
        if (workspaceData.name)
            updateData.name = workspaceData.name;
        if (workspaceData.description)
            updateData.description = workspaceData.description;
        if (workspaceData.members)
            updateData.members = JSON.stringify(workspaceData.members); // Update members as JSON
        const updatedRows = await (0, db_1.default)(this.workspaceTableName)
            .where({ workspace_id: workspaceId })
            .update(updateData);
        if (updatedRows === 0) {
            return undefined;
        }
        return this.getWorkspaceById(workspaceId);
    }
    async deleteWorkspace(workspaceId) {
        const deletedRows = await (0, db_1.default)(this.workspaceTableName)
            .where({ workspace_id: workspaceId })
            .del();
        return deletedRows > 0;
    }
    // Team Coordination
    async getTeams(options) {
        let query = (0, db_1.default)(this.teamTableName);
        if (options.paimInstanceId) {
            query = query.where({ paim_instance_id: options.paimInstanceId });
        }
        const totalResult = await query.clone().count('* as count').first();
        const total = parseInt(totalResult.count, 10);
        const page = options.page || 1;
        const size = options.size || 10;
        const offset = (page - 1) * size;
        const [sortBy, sortDirection] = options.sort ? options.sort.split(',') : ['created_at', 'desc']; // Use 'created_at' as default
        const teamsDB = await query
            .select('*')
            .offset(offset)
            .limit(size)
            .orderBy(sortBy, sortDirection); // Explicitly cast to string
        const teams = teamsDB.map(this.mapDbToTeam);
        return { teams, total }; // Changed return format
    }
    async createTeam(teamData) {
        const newTeam = {
            id: (0, uuid_1.v4)(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            name: teamData.name,
            description: teamData.description || undefined, // Explicitly set to undefined if not present
            paimInstanceId: teamData.paimInstanceId,
            members: teamData.initialMembers || [],
        };
        await (0, db_1.default)(this.teamTableName).insert({
            team_id: newTeam.id,
            name: newTeam.name,
            description: newTeam.description,
            paim_instance_id: newTeam.paimInstanceId,
            created_at: newTeam.createdAt,
            updated_at: newTeam.updatedAt,
            initial_members: JSON.stringify(newTeam.members),
        });
        return newTeam;
    }
    async getTeamById(teamId) {
        const teamDB = await (0, db_1.default)(this.teamTableName)
            .where({ team_id: teamId })
            .select('*')
            .first();
        if (!teamDB) {
            return undefined;
        }
        return this.mapDbToTeam(teamDB);
    }
    async updateTeam(teamId, teamData) {
        const updateData = {
            updated_at: new Date().toISOString(),
        };
        if (teamData.name)
            updateData.name = teamData.name;
        if (teamData.description)
            updateData.description = teamData.description;
        if (teamData.members)
            updateData.members = JSON.stringify(teamData.members); // Update members as JSON
        const updatedRows = await (0, db_1.default)(this.teamTableName)
            .where({ team_id: teamId })
            .update(updateData);
        if (updatedRows === 0) {
            return undefined;
        }
        return this.getTeamById(teamId);
    }
    async deleteTeam(teamId) {
        const deletedRows = await (0, db_1.default)(this.teamTableName)
            .where({ team_id: teamId })
            .del();
        return deletedRows > 0;
    }
    // Helper functions to map database rows to types
    mapDbToWorkflow(dbRow) {
        return {
            id: dbRow.workflow_id,
            name: dbRow.name,
            description: dbRow.description,
            ownerId: dbRow.owner_id,
            paimInstanceId: dbRow.paim_instance_id, // Added paimInstanceId
            status: dbRow.status,
            definition: dbRow.definition ? JSON.parse(dbRow.definition) : {}, // Parse definition from JSON
            createdAt: dbRow.created_at,
            updatedAt: dbRow.updated_at,
        };
    }
    mapDbToTask(dbRow) {
        return {
            id: dbRow.task_id,
            workflowId: dbRow.workflow_id,
            title: dbRow.title, // Changed from name to title
            description: dbRow.description,
            status: dbRow.status,
            priority: dbRow.priority, // Added priority
            assignedToId: dbRow.assigned_to_id, // Changed from assigned_to to assignedToId
            assignedToType: dbRow.assigned_to_type, // Added assignedToType
            dueDate: dbRow.due_date,
            createdAt: dbRow.created_at,
            updatedAt: dbRow.updated_at,
        };
    }
    mapDbToCollaborationSession(dbRow) {
        return {
            id: dbRow.session_id,
            name: dbRow.name,
            description: dbRow.description,
            ownerId: dbRow.owner_id,
            paimInstanceId: dbRow.paim_instance_id,
            participants: dbRow.initial_participants ? JSON.parse(dbRow.initial_participants).map((userId) => ({ userId, joinedAt: dbRow.created_at })) : [],
            status: dbRow.status,
            createdAt: dbRow.created_at,
            endedAt: dbRow.ended_at,
            workflowId: dbRow.workflow_id, // Mapped workflowId
            sessionType: dbRow.session_type, // Mapped sessionType
        };
    }
    mapDbToNotification(dbRow) {
        return {
            id: dbRow.notification_id,
            userId: dbRow.user_id,
            type: dbRow.type,
            message: dbRow.message,
            read: dbRow.read,
            createdAt: dbRow.created_at,
        };
    }
    mapDbToWorkspace(dbRow) {
        return {
            id: dbRow.workspace_id,
            name: dbRow.name,
            description: dbRow.description,
            ownerId: dbRow.owner_id,
            paimInstanceId: dbRow.paim_instance_id,
            createdAt: dbRow.created_at,
            updatedAt: dbRow.updated_at,
            members: dbRow.initial_members ? JSON.parse(dbRow.initial_members) : [],
        };
    }
    mapDbToTeam(dbRow) {
        return {
            id: dbRow.team_id,
            name: dbRow.name,
            description: dbRow.description,
            paimInstanceId: dbRow.paim_instance_id,
            createdAt: dbRow.created_at,
            updatedAt: dbRow.updated_at,
            members: dbRow.initial_members ? JSON.parse(dbRow.initial_members) : [],
        };
    }
}
exports.WorkflowCollaborationRepository = WorkflowCollaborationRepository;
