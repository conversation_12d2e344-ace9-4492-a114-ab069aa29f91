import { PaginationMetadata } from '../paim/paim.types'; // Assuming Paim.types has PaginationMetadata

export interface Agent {
  id: string;
  name: string;
  persona: string;
  description?: string;
  status: 'active' | 'inactive' | 'busy' | 'available';
  paimInstanceId: string;
  capabilities: string[];
  ownerId: string; // ID of the user who owns this agent
  tenantId: string; // ID of the tenant/organization this agent belongs to
  teamId?: string; // Optional: ID of the team this agent belongs to
  createdAt: string;
  updatedAt: string;
}

export interface CreateAgentRequest {
  name: string;
  persona: string;
  description?: string;
  paimInstanceId: string;
  ownerId?: string;
  capabilities?: string[];
}

export interface UpdateAgentRequest {
  name?: string;
  persona?: string;
  description?: string;
  status?: 'active' | 'inactive' | 'busy' | 'available';
  capabilities?: string[];
}

export interface AgentAssignmentRequest {
  assignedToType: 'user' | 'task' | 'workflow';
  assignedToId: string;
  assignmentType: 'primary' | 'secondary' | 'observer';
  notes?: string;
}

export interface AgentAssignment {
  id: string;
  agentId: string;
  assignedToType: 'user' | 'task' | 'workflow';
  assignedToId: string;
  assignmentType: 'primary' | 'secondary' | 'observer';
  assignedAt: string;
}

export interface WorkflowExecutionRequest {
  inputData: Record<string, any>;
  agentIds: string[];
  callbackUrl?: string;
}

export interface WorkflowExecutionStatus {
  executionId: string;
  workflowId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  outputData?: Record<string, any>;
  error?: string;
}

export interface AgentPerformanceMetrics {
  agentId: string;
  totalTasksCompleted: number;
  averageTaskDuration: number; // in seconds
  errorRate: number; // percentage
  uptime: number; // percentage
  metrics: Record<string, any>; // Additional custom metrics
}

export interface AgentListResponse {
  data: Agent[];
  pagination: PaginationMetadata;
}

export interface AgentQueryOptions {
  page?: number;
  size?: number;
  sort?: string;
  persona?: string;
  status?: 'active' | 'inactive' | 'busy' | 'available';
}