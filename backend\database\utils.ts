import { db } from './db';
import { Knex } from 'knex';

// Utility function for transaction management
export async function withTransaction<T>(
  callback: (trx: Knex.Transaction) => Promise<T>
): Promise<T> {
  return db.transaction(callback);
}

// Placeholder for multi-tenant context setting
// In a real application, this would likely involve setting a session variable
// or using a connection pool per tenant.
export async function setTenantContext(tenantId: string, client: any) {
  // Example: SET app.tenant_id = 'tenantId';
  // This requires a custom configuration in PostgreSQL, e.g., using RLS (Row Level Security)
  // For now, this is a placeholder to indicate where tenant context would be applied.
  console.log(`Setting tenant context for: ${tenantId}`);
  // await client.query(`SET app.tenant_id = $1`, [tenantId]);
}

// Generic query function with error handling
export async function query(sql: string, params: any[] = []) {
  try {
    const result = await db.raw(sql, params);
    return result.rows; // Knex raw query returns an object with a 'rows' property
  } catch (error) {
    console.error('Database query error:', error);
    throw error; // Re-throw to be handled by calling function
  }
}