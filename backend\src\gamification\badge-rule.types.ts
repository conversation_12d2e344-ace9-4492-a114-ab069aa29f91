export interface GamificationBadge {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  xpReward: number;
  criteria: BadgeCriteria;
  createdAt: Date;
  awardedAt?: string; // For compatibility with PowerOps Badge
  [key: string]: any; // Allow additional properties
}

export interface BadgeCriteria {
  requiredXp?: number;
  requiredBadgeIds?: string[];
  requiredAchievementIds?: string[];
  requiredStreakDays?: number;
  requiredSequence?: number;
  [key: string]: any; // Allow additional properties
}