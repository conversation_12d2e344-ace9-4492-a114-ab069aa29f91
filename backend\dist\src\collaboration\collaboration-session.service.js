"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollaborationSessionService = void 0;
class CollaborationSessionService {
    wsService;
    collaborationEvents;
    activeSessions = new Map(); // sessionId -> CollaborationSession
    sessionParticipants = new Map(); // sessionId -> Set<userId>
    workflowLocks = new Map(); // workflowId -> userId (for exclusive editing)
    constructor(wsService, collaborationEvents) {
        this.wsService = wsService;
        this.collaborationEvents = collaborationEvents;
    }
    async startSession(sessionData) {
        // In a real app, you'd persist this session to a database.
        const newSession = {
            id: `session-${Date.now()}`,
            createdAt: new Date().toISOString(),
            status: 'active',
            participants: [], // Initialize as empty array of correct type
            name: sessionData.name,
            ownerId: sessionData.ownerId,
            paimInstanceId: sessionData.paimInstanceId,
            description: sessionData.description, // Explicitly assign optional description
            workflowId: sessionData.workflowId,
            sessionType: sessionData.sessionType,
        };
        this.activeSessions.set(newSession.id, newSession);
        this.sessionParticipants.set(newSession.id, new Set());
        // Add initial participants
        if (sessionData.initialParticipants) {
            sessionData.initialParticipants.forEach(userId => this.joinSession(newSession.id, userId));
        }
        console.log(`Collaboration session ${newSession.id} started for workflow ${newSession.workflowId}`);
        // Emit an event that a new session has started (optional, for broader awareness)
        return newSession;
    }
    async joinSession(sessionId, userId) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new Error('Collaboration session not found');
        }
        if (!this.sessionParticipants.has(sessionId)) {
            this.sessionParticipants.set(sessionId, new Set());
        }
        this.sessionParticipants.get(sessionId)?.add(userId);
        // Update session participants list (in-memory for now)
        if (!session.participants.some(p => p.userId === userId)) {
            session.participants.push({ userId, joinedAt: new Date().toISOString() });
        }
        this.collaborationEvents.emitPresenceUpdate({
            workflowId: session.workflowId || '', // Assuming workflowId is always present for a session
            userId,
            status: 'online',
        });
        console.log(`User ${userId} joined session ${sessionId}`);
        return session;
    }
    async leaveSession(sessionId, userId) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            console.warn(`Attempted to leave non-existent session ${sessionId}`);
            return;
        }
        this.sessionParticipants.get(sessionId)?.delete(userId);
        session.participants = session.participants.filter(p => p.userId !== userId);
        this.collaborationEvents.emitPresenceUpdate({
            workflowId: session.workflowId || '',
            userId,
            status: 'offline',
        });
        console.log(`User ${userId} left session ${sessionId}`);
        // If no participants left, end the session (optional logic)
        if (this.sessionParticipants.get(sessionId)?.size === 0) {
            this.endSession(sessionId);
        }
    }
    async endSession(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (session) {
            session.status = 'ended';
            session.endedAt = new Date().toISOString();
            this.activeSessions.delete(sessionId);
            this.sessionParticipants.delete(sessionId);
            console.log(`Collaboration session ${sessionId} ended.`);
            // Emit an event that a session has ended (optional)
        }
    }
    getSessionParticipants(sessionId) {
        return Array.from(this.sessionParticipants.get(sessionId) || []);
    }
    handleCursorUpdate(payload) {
        // Re-broadcast the cursor update to all participants in the same workflow/session
        // For now, CollaborationEvents already broadcasts, so this is more for internal logic if needed.
        this.collaborationEvents.emitCursorUpdate(payload);
    }
    handleEditUpdate(payload) {
        // This is where conflict resolution logic would go.
        // For now, we'll just re-broadcast the edit.
        this.collaborationEvents.emitEditUpdate(payload);
    }
    // Real-time synchronization for workflow state changes
    async synchronizeWorkflowState(workflowId, newState) {
        // This method would be called by WorkflowCollaborationService when a workflow changes.
        // It would then broadcast the changes to all relevant session participants.
        this.collaborationEvents.emitWorkflowUpdate({
            workflowId: workflowId,
            // Include relevant parts of newState in the payload
            // e.g., status: newState.status, description: newState.description
        });
        console.log(`Synchronizing workflow ${workflowId} state.`);
    }
    // Conflict resolution for simultaneous edits (placeholder)
    resolveConflict(workflowId, field, oldContent, newContent, userId) {
        console.log(`Resolving conflict for workflow ${workflowId}, field ${field} by user ${userId}`);
        // Simple last-write-wins for now. In a real system, this would be more complex (e.g., Operational Transformation, Conflict-free Replicated Data Types)
        return newContent;
    }
    // Basic locking mechanism for exclusive editing (optional)
    acquireLock(workflowId, userId) {
        if (this.workflowLocks.has(workflowId) && this.workflowLocks.get(workflowId) !== userId) {
            console.log(`Workflow ${workflowId} is already locked by ${this.workflowLocks.get(workflowId)}`);
            return false;
        }
        this.workflowLocks.set(workflowId, userId);
        console.log(`User ${userId} acquired lock for workflow ${workflowId}`);
        return true;
    }
    releaseLock(workflowId, userId) {
        if (this.workflowLocks.get(workflowId) === userId) {
            this.workflowLocks.delete(workflowId);
            console.log(`User ${userId} released lock for workflow ${workflowId}`);
            return true;
        }
        console.log(`User ${userId} does not hold lock for workflow ${workflowId}`);
        return false;
    }
}
exports.CollaborationSessionService = CollaborationSessionService;
