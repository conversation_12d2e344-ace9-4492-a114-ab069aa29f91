"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsService = void 0;
const powerops_repository_1 = require("./powerops.repository");
const authorization_service_1 = require("../auth/authorization.service");
const notification_service_1 = require("../notifications/notification.service");
const authorization_types_1 = require("../auth/authorization.types");
const errors_1 = require("../utils/errors");
const database_1 = require("../database/database");
const uuid_1 = require("uuid");
const logger_1 = __importDefault(require("../config/logger"));
const powerops_types_1 = require("./powerops.types");
class PowerOpsService {
    powerOpsRepository;
    authorizationService;
    notificationService;
    constructor(powerOpsRepository, authorizationService, notificationService) {
        this.powerOpsRepository = powerOpsRepository || new powerops_repository_1.PowerOpsRepository(database_1.db);
        this.authorizationService = authorizationService || new authorization_service_1.AuthorizationService();
        this.notificationService = notificationService || new notification_service_1.NotificationService({}, {});
    }
    async scaleServiceResources(serviceName, direction) {
        console.log(`Simulating scaling ${direction} for service: ${serviceName}`);
        // In a real scenario, this would interact with infrastructure APIs
    }
    // PowerOps Usage & Cost Management
    async logUsage(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_CREATE);
        const usageEntry = await this.powerOpsRepository.logPowerOpsUsage(data);
        return {
            entityId: usageEntry.entityId,
            entityType: usageEntry.entityType,
            totalUsageUnits: usageEntry.usageUnits,
            estimatedCost: usageEntry.estimatedCost,
        };
    }
    async getUsage(user, entityId, entityType, startDate, endDate) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === authorization_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        const usageEntries = await this.powerOpsRepository.getPowerOpsUsage(entityId, entityType, startDate, endDate);
        const aggregatedUsage = {};
        usageEntries.forEach(entry => {
            const key = `${entry.entityId}-${entry.entityType}`;
            if (!aggregatedUsage[key]) {
                aggregatedUsage[key] = {
                    entityId: entry.entityId,
                    entityType: entry.entityType,
                    totalUsageUnits: 0,
                    estimatedCost: 0,
                };
            }
            aggregatedUsage[key].totalUsageUnits += entry.usageUnits;
            aggregatedUsage[key].estimatedCost += entry.estimatedCost;
        });
        return Object.values(aggregatedUsage);
    }
    async getBudgets(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === authorization_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW_ALL);
        }
        return this.powerOpsRepository.getBudgets(entityId, entityType);
    }
    async createBudget(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.createBudget(data);
    }
    async updateBudget(user, budgetId, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
        if (!existingBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        this.authorizationService.canAccessResource(user, {
            ownerId: existingBudget.entityType === authorization_types_1.EntityType.User ? existingBudget.entityId : undefined,
            organizationId: existingBudget.entityType === authorization_types_1.EntityType.Organization ? existingBudget.entityId : user.organizationId,
            teamId: existingBudget.entityType === authorization_types_1.EntityType.Team ? existingBudget.entityId : user.teamId,
        }, 'update');
        const updatedBudget = await this.powerOpsRepository.updateBudget(budgetId, data);
        if (!updatedBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return updatedBudget;
    }
    async deleteBudget(user, budgetId) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
        if (!existingBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        this.authorizationService.canAccessResource(user, {
            ownerId: existingBudget.entityType === authorization_types_1.EntityType.User ? existingBudget.entityId : undefined,
            organizationId: existingBudget.entityType === authorization_types_1.EntityType.Organization ? existingBudget.entityId : user.organizationId,
            teamId: existingBudget.entityType === authorization_types_1.EntityType.Team ? existingBudget.entityId : user.teamId,
        }, 'delete');
        const deleted = await this.powerOpsRepository.deleteBudget(budgetId);
        if (!deleted) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return deleted;
    }
    async getCostOptimizationRecommendations(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW);
        return this.powerOpsRepository.getCostOptimizationRecommendations(entityId, entityType);
    }
    async getResourceUsageLimits(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW);
        return this.powerOpsRepository.getResourceUsageLimits(entityId, entityType);
    }
    async setResourceUsageLimit(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.setResourceUsageLimit(data);
    }
    // Gamification (XP, Badges, Achievements, Streaks)
    async getXp(user, id, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        const xp = await this.powerOpsRepository.getXp(id, entityType);
        if (!xp) {
            return { entityId: id, entityType, currentXp: 0, level: 1 };
        }
        return xp;
    }
    async awardXp(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        const updatedXp = await this.powerOpsRepository.awardXp(data);
        // Check for level-up and send notification
        if (updatedXp.level > (await this.powerOpsRepository.getXp(data.entityId, data.entityType))?.level || 0) {
            const notification = await this.notificationService.createNotification({
                userId: data.entityId,
                type: 'success',
                message: `Congratulations! You've reached PowerOps Level ${updatedXp.level}!`,
            });
            // The createNotification method already calls emitNotification internally
            logger_1.default.info(`Level up notification sent for user ${data.entityId}: Level ${updatedXp.level}`);
        }
        return updatedXp;
    }
    async getAllBadges(user) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        return this.powerOpsRepository.getAllBadges();
    }
    async getBadges(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        return this.powerOpsRepository.getBadgesForEntity(entityId, entityType);
    }
    async awardBadge(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.awardBadge(data);
    }
    // Achievement methods
    async getAchievements(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        logger_1.default.info(`Getting achievements for ${entityType} ${entityId} by user ${user.userId}`);
        // Placeholder implementation
        return [
            {
                id: (0, uuid_1.v4)(),
                name: 'First Steps',
                description: 'Complete your first task',
                entityId,
                entityType,
                unlockedAt: new Date().toISOString(),
                progress: 100
            }
        ];
    }
    async grantAchievement(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        logger_1.default.info(`Granting achievement ${data.achievementId} to ${data.entityType} ${data.entityId} by user ${user.userId}`);
        // Placeholder implementation
        return {
            id: (0, uuid_1.v4)(),
            achievementId: data.achievementId,
            entityId: data.entityId,
            entityType: data.entityType,
            grantedAt: new Date().toISOString(),
            grantedBy: user.userId
        };
    }
    // Streak methods
    async getStreaks(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        logger_1.default.info(`Getting streaks for ${entityType} ${entityId} by user ${user.userId}`);
        // Placeholder implementation
        return [
            {
                id: (0, uuid_1.v4)(),
                type: 'daily_login',
                currentStreak: 5,
                longestStreak: 12,
                lastActivity: new Date().toISOString(),
                entityId,
                entityType
            }
        ];
    }
    // Invoice methods
    async getInvoices(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW);
        logger_1.default.info(`Getting invoices for ${entityType} ${entityId} by user ${user.userId}`);
        // Placeholder implementation
        return [
            {
                id: (0, uuid_1.v4)(),
                entityId,
                entityType,
                amount: 250.00,
                currency: 'USD',
                status: 'paid',
                billingPeriodStart: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                billingPeriodEnd: new Date().toISOString(),
                createdAt: new Date().toISOString()
            }
        ];
    }
    async createInvoice(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        logger_1.default.info(`Creating invoice for ${data.entityType} ${data.entityId} by user ${user.userId}`);
        // Placeholder implementation
        return {
            id: (0, uuid_1.v4)(),
            ...data,
            status: 'pending',
            createdAt: new Date().toISOString(),
            createdBy: user.userId
        };
    }
    // Payment methods
    async processPayment(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        logger_1.default.info(`Processing payment for invoice ${data.invoiceId} by user ${user.userId}`);
        // Placeholder implementation
        return {
            id: (0, uuid_1.v4)(),
            invoiceId: data.invoiceId,
            amount: data.amount,
            currency: data.currency,
            status: 'completed',
            processedAt: new Date().toISOString(),
            processedBy: user.userId
        };
    }
    // Leaderboard methods
    async getLeaderboard(user, metric, limit) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        logger_1.default.info(`Getting leaderboard for metric ${metric} by user ${user.userId}`);
        // Placeholder implementation
        return [
            {
                rank: 1,
                entityId: 'user-1',
                entityType: authorization_types_1.EntityType.User,
                value: 1500,
                metric
            },
            {
                rank: 2,
                entityId: 'user-2',
                entityType: authorization_types_1.EntityType.User,
                value: 1200,
                metric
            }
        ].slice(0, limit || 10);
    }
    // Notification methods
    async getNotifications(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        logger_1.default.info(`Getting notifications for ${entityType} ${entityId} by user ${user.userId}`);
        // Placeholder implementation
        return [
            {
                id: (0, uuid_1.v4)(),
                entityId,
                entityType,
                type: 'budget_alert',
                message: 'Budget threshold reached',
                timestamp: new Date().toISOString(),
                read: false
            }
        ];
    }
    async createNotification(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_CREATE);
        logger_1.default.info(`Creating notification for ${data.entityType} ${data.entityId} by user ${user.userId}`);
        // Placeholder implementation
        const notification = {
            id: (0, uuid_1.v4)(),
            ...data,
            timestamp: new Date().toISOString(),
            read: false
        };
        return notification;
    }
    // Helper for calculating usage cost
    calculateUsageCost(usageUnits, costCategory) {
        switch (costCategory) {
            case powerops_types_1.CostCategory.Compute: return usageUnits * 0.01;
            case powerops_types_1.CostCategory.Storage: return usageUnits * 0.001;
            case powerops_types_1.CostCategory.Bandwidth: return usageUnits * 0.005;
            case powerops_types_1.CostCategory.AIModel: return usageUnits * 0.05;
            default: return usageUnits * 0.01;
        }
    }
}
exports.PowerOpsService = PowerOpsService;
