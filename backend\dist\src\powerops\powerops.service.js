"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsService = void 0;
const powerops_repository_1 = require("./powerops.repository");
const authorization_service_1 = require("../auth/authorization.service");
const notification_service_1 = require("../notifications/notification.service");
const authorization_types_1 = require("../auth/authorization.types");
const errors_1 = require("../utils/errors");
const database_1 = require("../database/database");
const powerops_types_1 = require("./powerops.types");
class PowerOpsService {
    powerOpsRepository;
    authorizationService;
    notificationService;
    constructor(powerOpsRepository, authorizationService, notificationService) {
        this.powerOpsRepository = powerOpsRepository || new powerops_repository_1.PowerOpsRepository(database_1.db);
        this.authorizationService = authorizationService || new authorization_service_1.AuthorizationService();
        this.notificationService = notificationService || new notification_service_1.NotificationService({}, {});
    }
    async scaleServiceResources(serviceName, direction) {
        console.log(`Simulating scaling ${direction} for service: ${serviceName}`);
        // In a real scenario, this would interact with infrastructure APIs
    }
    // PowerOps Usage & Cost Management
    async logUsage(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_CREATE);
        const usageEntry = await this.powerOpsRepository.logPowerOpsUsage(data);
        return {
            entityId: usageEntry.entityId,
            entityType: usageEntry.entityType,
            totalUsageUnits: usageEntry.usageUnits,
            estimatedCost: usageEntry.estimatedCost,
        };
    }
    async getUsage(user, entityId, entityType, startDate, endDate) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === authorization_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        const usageEntries = await this.powerOpsRepository.getPowerOpsUsage(entityId, entityType, startDate, endDate);
        const aggregatedUsage = {};
        usageEntries.forEach(entry => {
            const key = `${entry.entityId}-${entry.entityType}`;
            if (!aggregatedUsage[key]) {
                aggregatedUsage[key] = {
                    entityId: entry.entityId,
                    entityType: entry.entityType,
                    totalUsageUnits: 0,
                    estimatedCost: 0,
                };
            }
            aggregatedUsage[key].totalUsageUnits += entry.usageUnits;
            aggregatedUsage[key].estimatedCost += entry.estimatedCost;
        });
        return Object.values(aggregatedUsage);
    }
    async getBudgets(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === authorization_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === authorization_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW_ALL);
        }
        return this.powerOpsRepository.getBudgets(entityId, entityType);
    }
    async createBudget(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.createBudget(data);
    }
    async updateBudget(user, budgetId, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
        if (!existingBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        this.authorizationService.canAccessResource(user, {
            ownerId: existingBudget.entityType === authorization_types_1.EntityType.User ? existingBudget.entityId : undefined,
            organizationId: existingBudget.entityType === authorization_types_1.EntityType.Organization ? existingBudget.entityId : user.organizationId,
            teamId: existingBudget.entityType === authorization_types_1.EntityType.Team ? existingBudget.entityId : user.teamId,
        }, 'update');
        const updatedBudget = await this.powerOpsRepository.updateBudget(budgetId, data);
        if (!updatedBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return updatedBudget;
    }
    async deleteBudget(user, budgetId) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
        if (!existingBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        this.authorizationService.canAccessResource(user, {
            ownerId: existingBudget.entityType === authorization_types_1.EntityType.User ? existingBudget.entityId : undefined,
            organizationId: existingBudget.entityType === authorization_types_1.EntityType.Organization ? existingBudget.entityId : user.organizationId,
            teamId: existingBudget.entityType === authorization_types_1.EntityType.Team ? existingBudget.entityId : user.teamId,
        }, 'delete');
        const deleted = await this.powerOpsRepository.deleteBudget(budgetId);
        if (!deleted) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return deleted;
    }
    async getCostOptimizationRecommendations(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW);
        return this.powerOpsRepository.getCostOptimizationRecommendations(entityId, entityType);
    }
    async getResourceUsageLimits(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_VIEW);
        return this.powerOpsRepository.getResourceUsageLimits(entityId, entityType);
    }
    async setResourceUsageLimit(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.setResourceUsageLimit(data);
    }
    // Gamification (XP, Badges, Achievements, Streaks)
    async getXp(user, id, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        const xp = await this.powerOpsRepository.getXp(id, entityType);
        if (!xp) {
            return { entityId: id, entityType, currentXp: 0, level: 1 };
        }
        return xp;
    }
    async awardXp(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        const updatedXp = await this.powerOpsRepository.awardXp(data);
        // Check for level-up and send notification
        if (updatedXp.level > (await this.powerOpsRepository.getXp(data.entityId, data.entityType))?.level || 0) {
            await this.notificationService.createNotification({
                userId: data.entityId,
                type: 'success',
                message: `Congratulations! You've reached PowerOps Level ${updatedXp.level}!`,
            });
            this.notificationService.wsService.broadcast(JSON.stringify({
                event: 'powerOpsAchievement',
                payload: {
                    userId: data.entityId,
                    achievementName: `Level Up: ${updatedXp.level}`,
                    levelUp: true,
                },
            }));
        }
        return updatedXp;
    }
    async getAllBadges(user) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        return this.powerOpsRepository.getAllBadges();
    }
    async getBadges(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_READ);
        return this.powerOpsRepository.getBadgesForEntity(entityId, entityType);
    }
    async awardBadge(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, authorization_types_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === authorization_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === authorization_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === authorization_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.awardBadge(data);
    }
    // Helper for calculating usage cost
    calculateUsageCost(usageUnits, costCategory) {
        switch (costCategory) {
            case powerops_types_1.CostCategory.Compute: return usageUnits * 0.01;
            case powerops_types_1.CostCategory.Storage: return usageUnits * 0.001;
            case powerops_types_1.CostCategory.Bandwidth: return usageUnits * 0.005;
            case powerops_types_1.CostCategory.AIModel: return usageUnits * 0.05;
            default: return usageUnits * 0.01;
        }
    }
}
exports.PowerOpsService = PowerOpsService;
