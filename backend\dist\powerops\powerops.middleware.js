"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsMiddleware = void 0;
const powerops_service_1 = require("./powerops.service");
const powerops_types_1 = require("./powerops.types");
class PowerOpsMiddleware {
    powerOpsService;
    constructor() {
        this.powerOpsService = new powerops_service_1.PowerOpsService();
    }
    // Middleware to log PowerOps usage for API calls
    logApiUsage = async (req, res, next) => {
        const startTime = process.hrtime.bigint();
        res.on('finish', async () => {
            const endTime = process.hrtime.bigint();
            const durationMs = Number(endTime - startTime) / 1_000_000; // Convert nanoseconds to milliseconds
            // Determine entityId and entityType based on authentication or request context
            // For now, using a placeholder. This needs to be integrated with actual auth.
            const entityId = req.user?.userId || 'anonymous'; // Assuming req.user is populated by auth middleware
            const entityType = req.user?.userId ? powerops_types_1.EntityType.User : powerops_types_1.EntityType.PaimInstance; // Placeholder logic
            // Estimate usage units based on request size, response size, and duration
            // This is a simplified example. Real usage tracking would be more sophisticated.
            const requestSize = req.socket.bytesRead || 0;
            const responseSize = res.socket?.bytesWritten || 0;
            const usageUnits = (requestSize + responseSize) / 1024 + (durationMs / 100); // KBs + duration factor
            try {
                await this.powerOpsService.logUsage({
                    entityId,
                    entityType,
                    usageUnits,
                    costCategory: powerops_types_1.CostCategory.Compute, // Defaulting to compute for API calls
                    description: `API Call: ${req.method} ${req.originalUrl}`,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                console.error('Error logging PowerOps usage:', error);
                // Decide how to handle logging errors: fail silently or re-throw
            }
        });
        next();
    };
}
exports.PowerOpsMiddleware = PowerOpsMiddleware;
