"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PluginRegistry = exports.Plugin = void 0;
const Config_1 = require("./Config");
const logger_1 = __importDefault(require("../../config/logger"));
class Plugin {
    metadata;
    config;
    hooks = {};
    isInstalled = false;
    installedAgents = new Set();
    constructor(metadata, config) {
        this.metadata = metadata;
        this.config = config || new Config_1.Config();
        logger_1.default.debug(`Plugin ${this.metadata.name} v${this.metadata.version} created`);
    }
    /**
     * Get plugin name
     */
    get name() {
        return this.metadata.name;
    }
    /**
     * Get plugin version
     */
    get version() {
        return this.metadata.version;
    }
    /**
     * Check if plugin is installed
     */
    get installed() {
        return this.isInstalled;
    }
    /**
     * Get list of agents this plugin is installed on
     */
    getInstalledAgents() {
        return Array.from(this.installedAgents);
    }
    /**
     * Install the plugin on an agent
     */
    async install(agent) {
        if (this.installedAgents.has(agent.id)) {
            throw new Error(`Plugin ${this.name} is already installed on agent ${agent.name}`);
        }
        try {
            // Run before install hook
            if (this.hooks.beforeInstall) {
                await this.hooks.beforeInstall(agent);
            }
            // Check dependencies
            await this.checkDependencies(agent);
            // Perform plugin-specific installation
            await this.onInstall(agent);
            // Register event listeners
            this.registerEventListeners(agent);
            // Mark as installed
            this.installedAgents.add(agent.id);
            this.isInstalled = true;
            // Run after install hook
            if (this.hooks.afterInstall) {
                await this.hooks.afterInstall(agent);
            }
            logger_1.default.info(`Plugin ${this.name} installed on agent ${agent.name}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to install plugin ${this.name} on agent ${agent.name}:`, error);
            throw error;
        }
    }
    /**
     * Uninstall the plugin from an agent
     */
    async uninstall(agent) {
        if (!this.installedAgents.has(agent.id)) {
            throw new Error(`Plugin ${this.name} is not installed on agent ${agent.name}`);
        }
        try {
            // Run before uninstall hook
            if (this.hooks.beforeUninstall) {
                await this.hooks.beforeUninstall(agent);
            }
            // Unregister event listeners
            this.unregisterEventListeners(agent);
            // Perform plugin-specific uninstallation
            await this.onUninstall(agent);
            // Mark as uninstalled
            this.installedAgents.delete(agent.id);
            if (this.installedAgents.size === 0) {
                this.isInstalled = false;
            }
            // Run after uninstall hook
            if (this.hooks.afterUninstall) {
                await this.hooks.afterUninstall(agent);
            }
            logger_1.default.info(`Plugin ${this.name} uninstalled from agent ${agent.name}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to uninstall plugin ${this.name} from agent ${agent.name}:`, error);
            throw error;
        }
    }
    /**
     * Configure the plugin
     */
    configure(config) {
        if (config instanceof Config_1.Config) {
            this.config = config;
        }
        else {
            this.config.load(config);
        }
        logger_1.default.debug(`Plugin ${this.name} configured`);
        return this;
    }
    /**
     * Get plugin configuration
     */
    getConfig() {
        return this.config;
    }
    /**
     * Set plugin hooks
     */
    setHooks(hooks) {
        this.hooks = { ...this.hooks, ...hooks };
        logger_1.default.debug(`Plugin ${this.name} hooks updated`);
        return this;
    }
    /**
     * Check if plugin is compatible with agent
     */
    async isCompatible(agent) {
        try {
            await this.checkCompatibility(agent);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get plugin status
     */
    getStatus() {
        return {
            installed: this.isInstalled,
            agentCount: this.installedAgents.size,
            metadata: { ...this.metadata }
        };
    }
    /**
     * Check plugin compatibility with agent
     */
    async checkCompatibility(agent) {
        // Default implementation - can be overridden
        logger_1.default.debug(`Checking compatibility for plugin ${this.name} with agent ${agent.name}`);
    }
    /**
     * Check plugin dependencies
     */
    async checkDependencies(agent) {
        if (!this.metadata.dependencies || this.metadata.dependencies.length === 0) {
            return;
        }
        // Check if required capabilities are available
        const agentCapabilities = agent.getCapabilities().map(c => c.name);
        for (const dependency of this.metadata.dependencies) {
            if (!agentCapabilities.includes(dependency)) {
                throw new Error(`Plugin ${this.name} requires capability '${dependency}' which is not available on agent ${agent.name}`);
            }
        }
        logger_1.default.debug(`All dependencies satisfied for plugin ${this.name}`);
    }
    /**
     * Register event listeners on the agent
     */
    registerEventListeners(agent) {
        if (this.hooks.onAgentStart) {
            agent.on('agent:start', this.hooks.onAgentStart);
        }
        if (this.hooks.onAgentStop) {
            agent.on('agent:stop', this.hooks.onAgentStop);
        }
        if (this.hooks.onTaskExecute) {
            agent.on('task:start', this.hooks.onTaskExecute);
        }
        logger_1.default.debug(`Event listeners registered for plugin ${this.name}`);
    }
    /**
     * Unregister event listeners from the agent
     */
    unregisterEventListeners(agent) {
        if (this.hooks.onAgentStart) {
            agent.off('agent:start', this.hooks.onAgentStart);
        }
        if (this.hooks.onAgentStop) {
            agent.off('agent:stop', this.hooks.onAgentStop);
        }
        if (this.hooks.onTaskExecute) {
            agent.off('task:start', this.hooks.onTaskExecute);
        }
        logger_1.default.debug(`Event listeners unregistered for plugin ${this.name}`);
    }
}
exports.Plugin = Plugin;
/**
 * Plugin Registry for managing plugins
 */
class PluginRegistry {
    static instance;
    plugins = new Map();
    constructor() { }
    static getInstance() {
        if (!PluginRegistry.instance) {
            PluginRegistry.instance = new PluginRegistry();
        }
        return PluginRegistry.instance;
    }
    /**
     * Register a plugin
     */
    register(plugin) {
        if (this.plugins.has(plugin.name)) {
            throw new Error(`Plugin ${plugin.name} is already registered`);
        }
        this.plugins.set(plugin.name, plugin);
        logger_1.default.info(`Plugin ${plugin.name} registered in registry`);
    }
    /**
     * Unregister a plugin
     */
    unregister(pluginName) {
        const removed = this.plugins.delete(pluginName);
        if (removed) {
            logger_1.default.info(`Plugin ${pluginName} unregistered from registry`);
        }
        return removed;
    }
    /**
     * Get a plugin by name
     */
    get(pluginName) {
        return this.plugins.get(pluginName);
    }
    /**
     * Get all registered plugins
     */
    getAll() {
        return Array.from(this.plugins.values());
    }
    /**
     * Get plugins by capability
     */
    getByCapability(capability) {
        return Array.from(this.plugins.values()).filter(plugin => plugin.metadata.capabilities?.includes(capability));
    }
    /**
     * Clear all plugins
     */
    clear() {
        this.plugins.clear();
        logger_1.default.info('Plugin registry cleared');
    }
}
exports.PluginRegistry = PluginRegistry;
