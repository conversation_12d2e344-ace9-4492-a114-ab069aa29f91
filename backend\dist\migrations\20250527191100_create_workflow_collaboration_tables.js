"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.createTable('workflows', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('tenant_id').notNullable();
        table.string('name').notNullable();
        table.text('description');
        table.uuid('owner_id').notNullable();
        table.uuid('paim_instance_id').notNullable();
        table.enum('status', ['active', 'inactive', 'draft']).notNullable().defaultTo('draft');
        table.jsonb('definition').notNullable();
        table.timestamps(true, true);
        table.timestamp('deleted_at').nullable();
        table.index('tenant_id');
        table.index('owner_id');
        table.index('paim_instance_id');
    });
    await knex.schema.createTable('tasks', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('tenant_id').notNullable();
        table.string('title').notNullable();
        table.text('description');
        table.enum('status', ['pending', 'in_progress', 'completed', 'cancelled']).notNullable().defaultTo('pending');
        table.enum('priority', ['low', 'medium', 'high', 'critical']).notNullable().defaultTo('medium');
        table.uuid('assigned_to_id').nullable();
        table.enum('assigned_to_type', ['user', 'agent']).nullable();
        table.timestamp('due_date').nullable();
        table.uuid('workflow_id').nullable();
        table.timestamps(true, true);
        table.timestamp('deleted_at').nullable();
        table.foreign('workflow_id').references('id').inTable('workflows').onDelete('SET NULL');
        table.index('tenant_id');
        table.index('assigned_to_id');
        table.index('workflow_id');
    });
    await knex.schema.createTable('collaboration_sessions', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('tenant_id').notNullable();
        table.string('name').notNullable();
        table.text('description');
        table.uuid('owner_id').notNullable();
        table.uuid('paim_instance_id').notNullable();
        table.enum('status', ['active', 'ended']).notNullable().defaultTo('active');
        table.timestamps(true, true);
        table.timestamp('ended_at').nullable();
        table.timestamp('deleted_at').nullable();
        table.index('tenant_id');
        table.index('owner_id');
        table.index('paim_instance_id');
    });
    await knex.schema.createTable('collaboration_session_participants', (table) => {
        table.uuid('session_id').notNullable();
        table.uuid('user_id').notNullable();
        table.timestamp('joined_at').notNullable().defaultTo(knex.fn.now());
        table.primary(['session_id', 'user_id']);
        table.foreign('session_id').references('id').inTable('collaboration_sessions').onDelete('CASCADE');
        table.index('user_id');
    });
    await knex.schema.createTable('cross_tenant_messages', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('sender_paim_instance_id').notNullable();
        table.uuid('recipient_paim_instance_id').notNullable();
        table.text('message_content').notNullable();
        table.enum('message_type', ['text', 'notification', 'data_request']).notNullable().defaultTo('text');
        table.uuid('related_task_id').nullable();
        table.timestamps(true, true);
        table.index('sender_paim_instance_id');
        table.index('recipient_paim_instance_id');
        table.index('related_task_id');
    });
    await knex.schema.createTable('notifications', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('user_id').notNullable();
        table.string('type').notNullable();
        table.text('message').notNullable();
        table.boolean('is_read').notNullable().defaultTo(false);
        table.timestamps(true, true);
        table.index('user_id');
        table.index('is_read');
    });
    await knex.schema.createTable('workflow_shares', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('workflow_id').notNullable();
        table.uuid('shared_with_id').notNullable();
        table.enum('shared_with_entity_type', ['user', 'team', 'paim_instance']).notNullable();
        table.enum('permission_level', ['view', 'edit', 'manage']).notNullable();
        table.timestamps(true, true);
        table.foreign('workflow_id').references('id').inTable('workflows').onDelete('CASCADE');
        table.index('workflow_id');
        table.index('shared_with_id');
    });
    await knex.schema.createTable('workspaces', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('tenant_id').notNullable();
        table.string('name').notNullable();
        table.text('description');
        table.uuid('owner_id').notNullable();
        table.uuid('paim_instance_id').notNullable();
        table.timestamps(true, true);
        table.timestamp('deleted_at').nullable();
        table.index('tenant_id');
        table.index('owner_id');
        table.index('paim_instance_id');
    });
    await knex.schema.createTable('workspace_members', (table) => {
        table.uuid('workspace_id').notNullable();
        table.uuid('user_id').notNullable();
        table.string('role').notNullable();
        table.timestamp('joined_at').notNullable().defaultTo(knex.fn.now());
        table.primary(['workspace_id', 'user_id']);
        table.foreign('workspace_id').references('id').inTable('workspaces').onDelete('CASCADE');
        table.index('user_id');
    });
    await knex.schema.createTable('teams', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('tenant_id').notNullable();
        table.string('name').notNullable();
        table.text('description');
        table.uuid('paim_instance_id').notNullable();
        table.timestamps(true, true);
        table.timestamp('deleted_at').nullable();
        table.index('tenant_id');
        table.index('paim_instance_id');
    });
    await knex.schema.createTable('team_members', (table) => {
        table.uuid('team_id').notNullable();
        table.uuid('user_id').notNullable();
        table.string('role').notNullable();
        table.timestamp('joined_at').notNullable().defaultTo(knex.fn.now());
        table.primary(['team_id', 'user_id']);
        table.foreign('team_id').references('id').inTable('teams').onDelete('CASCADE');
        table.index('user_id');
    });
}
async function down(knex) {
    await knex.schema.dropTableIfExists('team_members');
    await knex.schema.dropTableIfExists('teams');
    await knex.schema.dropTableIfExists('workspace_members');
    await knex.schema.dropTableIfExists('workspaces');
    await knex.schema.dropTableIfExists('workflow_shares');
    await knex.schema.dropTableIfExists('notifications');
    await knex.schema.dropTableIfExists('cross_tenant_messages');
    await knex.schema.dropTableIfExists('collaboration_session_participants');
    await knex.schema.dropTableIfExists('collaboration_sessions');
    await knex.schema.dropTableIfExists('tasks');
    await knex.schema.dropTableIfExists('workflows');
}
