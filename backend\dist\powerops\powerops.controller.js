"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsController = void 0;
const express = __importStar(require("express"));
const powerops_service_1 = require("./powerops.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const errors_1 = require("../utils/errors");
const express_rate_limit_1 = __importDefault(require("express-rate-limit")); // Import rateLimit
const type_guards_1 = require("../utils/type-guards");
const powerops_types_1 = require("./powerops.types");
// Rate limiting for XP endpoints to prevent farming
const xpLimiter = (0, express_rate_limit_1.default)({
    windowMs: 60 * 1000, // 1 minute
    max: 5, // Limit each IP to 5 requests per minute
    message: 'Too many XP requests from this IP, please try again after a minute.',
    keyGenerator: (req) => {
        // Use a combination of IP and user ID (if authenticated) for more granular limiting
        return req.ip + (req.user ? `-${req.user.userId}` : '');
    },
});
class PowerOpsController {
    router;
    powerOpsService;
    constructor() {
        this.powerOpsService = new powerops_service_1.PowerOpsService();
        this.router = express.Router();
        this.initializeRoutes(); // Add this line to initialize routes
    }
    // PowerOps Usage & Cost Management
    initializeRoutes() {
        this.router.post('/usage', (0, asyncHandler_1.asyncHandler)(this.logPowerOpsUsage.bind(this)));
        this.router.get('/usage/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getPowerOpsUsage.bind(this)));
        this.router.post('/xp/add', xpLimiter, (0, asyncHandler_1.asyncHandler)(this.awardXp.bind(this))); // Apply XP rate limit
        this.router.get('/xp/user/:id', (0, asyncHandler_1.asyncHandler)(this.getXpByUser.bind(this))); // New endpoint for user XP
        this.router.get('/xp/org/:id', (0, asyncHandler_1.asyncHandler)(this.getXpByOrg.bind(this))); // New endpoint for organization XP
        this.router.post('/badges/award', (0, asyncHandler_1.asyncHandler)(this.awardBadge.bind(this))); // Changed to /badges/award
        this.router.get('/badges', (0, asyncHandler_1.asyncHandler)(this.getAllBadges.bind(this))); // New endpoint for all badges
        this.router.get('/badges/user/:id', (0, asyncHandler_1.asyncHandler)(this.getBadgesByUser.bind(this))); // New endpoint for user badges
        this.router.post('/achievements', (0, asyncHandler_1.asyncHandler)(this.grantAchievement.bind(this)));
        this.router.get('/achievements/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getAchievements.bind(this)));
        this.router.get('/streaks/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getStreaks.bind(this)));
        this.router.get('/leaderboard/:metric', (0, asyncHandler_1.asyncHandler)(this.getLeaderboard.bind(this)));
        this.router.get('/budgets/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getBudgets.bind(this)));
        this.router.post('/budgets', (0, asyncHandler_1.asyncHandler)(this.createBudget.bind(this)));
        this.router.put('/budgets/:budgetId', (0, asyncHandler_1.asyncHandler)(this.updateBudget.bind(this)));
        this.router.delete('/budgets/:budgetId', (0, asyncHandler_1.asyncHandler)(this.deleteBudget.bind(this)));
        this.router.get('/invoices/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getInvoices.bind(this)));
        this.router.post('/invoices', (0, asyncHandler_1.asyncHandler)(this.createInvoice.bind(this)));
        this.router.post('/payments', (0, asyncHandler_1.asyncHandler)(this.processPayment.bind(this)));
        this.router.get('/recommendations/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getCostOptimizationRecommendations.bind(this)));
        this.router.get('/resource-limits/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getResourceUsageLimits.bind(this)));
        this.router.post('/resource-limits', (0, asyncHandler_1.asyncHandler)(this.setResourceUsageLimit.bind(this)));
        this.router.get('/notifications/:entityType/:entityId', (0, asyncHandler_1.asyncHandler)(this.getNotifications.bind(this)));
        this.router.post('/notifications', (0, asyncHandler_1.asyncHandler)(this.createNotification.bind(this)));
    }
    logPowerOpsUsage = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const usage = await this.powerOpsService.logUsage(data);
        res.status(201).json(usage);
    });
    getPowerOpsUsage = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType, startDate, endDate } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const usage = await this.powerOpsService.getUsage((0, type_guards_1.requireParam)(entityId, 'entityId'), entityType, // Keep as EntityType, validation above
        (0, type_guards_1.getParam)(startDate), (0, type_guards_1.getParam)(endDate));
        res.status(200).json(usage);
    });
    // Gamification (XP)
    getXpByUser = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { id } = req.params;
        const xp = await this.powerOpsService.getXp((0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.User);
        res.status(200).json(xp);
    });
    getXpByOrg = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { id } = req.params;
        const xp = await this.powerOpsService.getXp((0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.PaimInstance); // Assuming org is PaimInstance
        res.status(200).json(xp);
    });
    awardXp = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body; // Use the updated AwardXpRequest
        const xp = await this.powerOpsService.awardXp(data);
        res.status(200).json(xp);
    });
    // Gamification (Badges)
    getAllBadges = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const badges = await this.powerOpsService.getAllBadges();
        res.status(200).json(badges);
    });
    getBadgesByUser = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { id } = req.params;
        const badges = await this.powerOpsService.getBadges((0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.User);
        res.status(200).json(badges);
    });
    awardBadge = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const badge = await this.powerOpsService.awardBadge(data);
        res.status(200).json(badge);
    });
    // Gamification (Achievements)
    getAchievements = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const achievements = await this.powerOpsService.getAchievements((0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(achievements);
    });
    grantAchievement = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const achievement = await this.powerOpsService.grantAchievement(data);
        res.status(200).json(achievement);
    });
    // Gamification (Streaks)
    getStreaks = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const streaks = await this.powerOpsService.getStreaks((0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(streaks);
    });
    // Cost Management (Budgets)
    getBudgets = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const budgets = await this.powerOpsService.getBudgets((0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(budgets);
    });
    createBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const budget = await this.powerOpsService.createBudget(data);
        res.status(201).json(budget);
    });
    updateBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { budgetId } = req.params;
        const data = req.body;
        const updatedBudget = await this.powerOpsService.updateBudget((0, type_guards_1.requireParam)(budgetId, 'budgetId'), data);
        res.status(200).json(updatedBudget);
    });
    deleteBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { budgetId } = req.params;
        await this.powerOpsService.deleteBudget((0, type_guards_1.requireParam)(budgetId, 'budgetId'));
        res.status(204).send();
    });
    // Billing (Invoices)
    getInvoices = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const invoices = await this.powerOpsService.getInvoices((0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(invoices);
    });
    createInvoice = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const invoice = await this.powerOpsService.createInvoice(data);
        res.status(201).json(invoice);
    });
    // Billing (Payments)
    processPayment = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const payment = await this.powerOpsService.processPayment(data);
        res.status(200).json(payment);
    });
    // Gamification (Leaderboard)
    getLeaderboard = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { metric, limit } = req.query;
        if (!metric) {
            throw new errors_1.CustomError('metric is a required query parameter.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const leaderboard = await this.powerOpsService.getLeaderboard((0, type_guards_1.requireParam)(metric, 'metric'), limit ? parseInt((0, type_guards_1.getParam)(limit)) : undefined);
        res.status(200).json(leaderboard);
    });
    // Cost Management (Recommendations)
    getCostOptimizationRecommendations = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const recommendations = await this.powerOpsService.getCostOptimizationRecommendations((0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(recommendations);
    });
    // Resource Management (Limits)
    getResourceUsageLimits = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const limits = await this.powerOpsService.getResourceUsageLimits((0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(limits);
    });
    setResourceUsageLimit = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const limit = await this.powerOpsService.setResourceUsageLimit(data);
        res.status(201).json(limit);
    });
    // Notifications
    getNotifications = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const { entityId, entityType } = req.query;
        if (!entityId || !entityType) {
            throw new errors_1.CustomError('entityId and entityType are required query parameters.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        if (!Object.values(powerops_types_1.EntityType).includes(entityType)) {
            throw new errors_1.CustomError(`Invalid entityType: ${entityType}. Must be 'user' or 'paim_instance'.`, { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
        }
        const notifications = await this.powerOpsService.getNotifications((0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(notifications);
    });
    createNotification = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        const data = req.body;
        const notification = await this.powerOpsService.createNotification(data);
        res.status(201).json(notification);
    });
}
exports.PowerOpsController = PowerOpsController;
