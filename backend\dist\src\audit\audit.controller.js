"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditController = void 0;
const express_1 = require("express");
const audit_service_1 = require("./audit.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger"));
const type_guards_1 = require("../utils/type-guards");
const authorization_1 = require("../middleware/authorization"); // Import authorization middleware
const permissions_1 = require("../auth/permissions"); // Import permissions
class AuditController {
    router;
    auditTrailService;
    constructor() {
        this.router = (0, express_1.Router)();
        this.auditTrailService = new audit_service_1.AuditTrailService();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AUDIT_VIEW]), (0, asyncHandler_1.asyncHandler)(this.getAuditLogs));
        this.router.get('/report/compliance', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AUDIT_VIEW]), (0, asyncHandler_1.asyncHandler)(this.getComplianceReport));
        this.router.get('/analytics', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AUDIT_VIEW]), (0, asyncHandler_1.asyncHandler)(this.getAuditAnalytics));
        // Future: Add endpoints for audit configuration management, data retention policies etc.
    }
    /**
     * GET /audit - Retrieve audit logs with filtering and pagination.
     */
    async getAuditLogs(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { page, limit, userId, category, severity, operationType, startDate, endDate, resourceId, keywords } = req.query;
        const filters = {
            tenantId: req.user.tenantId, // Use authenticated user's tenantId
            userId: (0, type_guards_1.getParam)(userId),
            category: (0, type_guards_1.getParam)(category),
            severity: (0, type_guards_1.getParam)(severity),
            operationType: (0, type_guards_1.getParam)(operationType),
            startDate: startDate ? new Date((0, type_guards_1.getParam)(startDate)) : undefined,
            endDate: endDate ? new Date((0, type_guards_1.getParam)(endDate)) : undefined,
            resourceId: (0, type_guards_1.getParam)(resourceId),
            keywords: (0, type_guards_1.getParam)(keywords),
        };
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(limit) || 20;
        logger_1.default.info(`Fetching audit logs for tenant ${req.user.tenantId} with filters: ${JSON.stringify(filters)}`);
        const auditLogs = await this.auditTrailService.getAuditLogs(req.user, filters, pageNum, limitNum);
        res.status(200).json(auditLogs);
    }
    /**
     * GET /audit/report/compliance - Generate compliance reports.
     */
    async getComplianceReport(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { complianceStandard, startDate, endDate } = req.query;
        if (!complianceStandard || !startDate || !endDate) {
            throw new errors_1.CustomError('Compliance standard, start date, and end date are required.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        logger_1.default.info(`Generating compliance report for tenant ${req.user.tenantId}, standard: ${complianceStandard}`);
        const report = await this.auditTrailService.generateComplianceReport(req.user, // Pass the user object
        complianceStandard, new Date((0, type_guards_1.getParam)(startDate)), new Date((0, type_guards_1.getParam)(endDate)));
        res.status(200).json(report);
    }
    /**
     * GET /audit/analytics - Retrieve audit analytics.
     */
    async getAuditAnalytics(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { startDate, endDate } = req.query;
        if (!startDate || !endDate) {
            throw new errors_1.CustomError('Start date and end date are required for analytics.', { originalErrorCode: 'Bad Request', originalStatusCode: 400 });
        }
        logger_1.default.info(`Fetching audit analytics for tenant ${req.user.tenantId}`);
        const analytics = await this.auditTrailService.getAuditAnalytics(req.user, // Pass the user object
        new Date((0, type_guards_1.getParam)(startDate)), new Date((0, type_guards_1.getParam)(endDate)));
        res.status(200).json(analytics);
    }
}
exports.AuditController = AuditController;
