"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enforceTenantContext = exports.authorizeRoles = exports.authorizePaimTier = exports.authenticate = void 0;
const supabase_1 = require("../config/supabase"); // Import supabaseAdmin
/**
 * Middleware to authenticate requests using JWT.
 * Attaches decoded user payload and tenantId to the request object.
 */
const authenticate = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ message: 'No token provided or invalid token format.' });
    }
    const token = authHeader.split(' ')[1];
    try {
        const { data: { user }, error } = await supabase_1.supabaseAdmin.auth.getUser(token);
        if (error || !user) {
            return res.status(401).json({ message: 'Invalid or expired token.' });
        }
        // Map Supabase user to JwtPayload
        req.user = {
            userId: user.id,
            email: user.email || '',
            tenantId: user.user_metadata?.tenant_id || '', // Assuming tenant_id is in user_metadata
            paimTier: user.user_metadata?.paim_tier || '', // Assuming paim_tier is in user_metadata
            roles: user.user_metadata?.roles || [], // Assuming roles are in user_metadata
            iat: user.last_sign_in_at ? Math.floor(new Date(user.last_sign_in_at).getTime() / 1000) : 0,
            exp: 0, // Supabase handles token expiration internally
        };
        req.tenantId = req.user.tenantId;
        next();
    }
    catch (error) {
        console.error('Supabase authentication error:', error);
        return res.status(500).json({ message: 'Authentication failed due to server error.' });
    }
};
exports.authenticate = authenticate;
/**
 * Middleware to enforce PAIM tier-based authorization.
 * Requires the authenticate middleware to be run first.
 */
const authorizePaimTier = (requiredTier) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ message: 'Authentication required for authorization.' });
        }
        const userTier = req.user.paimTier;
        const paimTierHierarchy = {
            'System Admin': 1,
            'Company Admin': 2,
            'Power User': 3,
            'Personal': 4,
        };
        if (paimTierHierarchy[userTier] > paimTierHierarchy[requiredTier]) {
            return res.status(403).json({ message: 'Forbidden: Insufficient PAIM tier.' });
        }
        next();
    };
};
exports.authorizePaimTier = authorizePaimTier;
/**
 * Middleware to enforce role-based authorization.
 * Requires the authenticate middleware to be run first.
 */
const authorizeRoles = (requiredRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ message: 'Authentication required for authorization.' });
        }
        const userRoles = req.user.roles;
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
        if (!hasRequiredRole) {
            return res.status(403).json({ message: 'Forbidden: Insufficient roles.' });
        }
        next();
    };
};
exports.authorizeRoles = authorizeRoles;
/**
 * Middleware to enforce tenant context for multi-tenant isolation.
 * Ensures that a user can only access resources within their assigned tenant.
 * Requires the authenticate middleware to be run first.
 */
const enforceTenantContext = (req, res, next) => {
    if (!req.user || !req.tenantId) {
        return res.status(401).json({ message: 'Authentication and tenant context required.' });
    }
    // For endpoints that operate on a specific tenant ID provided in the path or query,
    // compare it with the tenantId from the JWT.
    // Example: /api/v1/tenants/{tenantId}/users
    // If the route parameter 'tenantId' exists, ensure it matches req.tenantId
    const requestedTenantId = req.params.tenantId || req.query.tenantId;
    // System Admin can bypass tenant context for cross-tenant operations
    if (req.user.paimTier === 'System Admin') {
        return next();
    }
    if (requestedTenantId && requestedTenantId !== req.tenantId) {
        return res.status(403).json({ message: 'Forbidden: Access to other tenant data is not allowed.' });
    }
    next();
};
exports.enforceTenantContext = enforceTenantContext;
