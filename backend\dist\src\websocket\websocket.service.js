"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketService = void 0;
const ws_1 = require("ws");
const websocket_middleware_1 = require("./websocket.middleware");
class WebSocketService {
    wss;
    clientMessageRateLimits = new Map(); // userId -> RateLimitEntry
    maxMessagesPerMinute = 60; // Max messages per minute per user
    rateLimitWindowMs = 60 * 1000; // 1 minute
    constructor(server) {
        this.wss = new ws_1.Server({
            server,
            verifyClient: (info, done) => {
                // Cast info.req to AuthenticatedRequest for type compatibility
                (0, websocket_middleware_1.authenticateWebSocket)(info.req, info.req, () => {
                    // If authentication succeeds, proceed with connection
                    done(true);
                });
            }
        });
        this.initializeWebSocketServer();
    }
    initializeWebSocketServer() {
        this.wss.on('connection', (ws, request) => {
            // The request object here is the http.IncomingMessage, which is augmented by authenticateWebSocket
            const authenticatedRequest = request;
            console.log(`Client connected: User ID - ${authenticatedRequest.userId}`);
            ws.on('message', (message) => {
                if (!authenticatedRequest.userId) {
                    ws.send(JSON.stringify({ event: 'error', payload: 'Unauthorized' }));
                    return;
                }
                if (!this.checkRateLimit(authenticatedRequest.userId)) {
                    ws.send(JSON.stringify({ event: 'error', payload: 'Rate limit exceeded' }));
                    return;
                }
                try {
                    const parsedMessage = JSON.parse(message);
                    this.handleIncomingMessage(authenticatedRequest.userId, parsedMessage);
                }
                catch (error) {
                    console.error(`Failed to parse WebSocket message from ${authenticatedRequest.userId}:`, error);
                    ws.send(JSON.stringify({ event: 'error', payload: 'Invalid message format' }));
                }
            });
            ws.on('close', () => {
                const userId = authenticatedRequest.userId;
                console.log(`Client disconnected: User ID - ${userId}`);
                if (userId) {
                    this.clientMessageRateLimits.delete(userId); // Clean up rate limit entry
                    // Potentially emit a presence update for this user if they were in a session
                    // this.messageHandlers.get(WebSocketEvent.PRESENCE_UPDATE)?.forEach(handler =>
                    //   handler(userId, { userId, status: 'offline', workflowId: 'N/A' })
                    // );
                }
            });
            ws.on('error', (error) => {
                console.error(`WebSocket error for ${authenticatedRequest.userId}:`, error);
            });
        });
        console.log('WebSocket server initialized');
    }
    messageHandlers = new Map();
    handleIncomingMessage(userId, message) {
        const handlers = this.messageHandlers.get(message.event);
        if (handlers) {
            handlers.forEach(handler => handler(userId, message.payload));
        }
        else {
            console.warn(`No handler registered for WebSocket event: ${message.event}`);
        }
    }
    on(event, handler) {
        if (!this.messageHandlers.has(event)) {
            this.messageHandlers.set(event, []);
        }
        this.messageHandlers.get(event)?.push(handler);
    }
    broadcast(message) {
        this.wss.clients.forEach((client) => {
            if (client.readyState === client.OPEN) {
                client.send(message);
            }
        });
    }
    checkRateLimit(userId) {
        const now = Date.now();
        let entry = this.clientMessageRateLimits.get(userId);
        if (!entry || (now - entry.lastReset > this.rateLimitWindowMs)) {
            entry = { count: 0, lastReset: now };
            this.clientMessageRateLimits.set(userId, entry);
        }
        if (entry.count >= this.maxMessagesPerMinute) {
            logger.warn(`WebSocket rate limit exceeded for user: ${userId}`);
            return false;
        }
        entry.count++;
        return true;
    }
    sendToUser(userId, message) {
        this.wss.clients.forEach((client) => {
            if (client.readyState === client.OPEN && client.userId === userId) {
                client.send(message);
            }
        });
    }
}
exports.WebSocketService = WebSocketService;
