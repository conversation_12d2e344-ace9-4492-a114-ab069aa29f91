import { Injectable } from '@nestjs/common';
import type { GamificationBadge, BadgeCriteria } from './badge-rule.types';
import { PowerOpsService } from '../powerops/powerops.service';
import { NotificationService } from '../notifications/notification.service';
import { EntityType, Badge } from '../powerops/powerops.types';

@Injectable()
export class BadgeRulesService {
  constructor(
    private powerOpsService: PowerOpsService,
    private notificationService: NotificationService
  ) {}

  private async evaluateTimeBasedCriteria(
    userId: string,
    criteria: BadgeCriteria
  ): Promise<boolean> {
    // Implement time-based criteria evaluation
    // Example: "logged in for 7 consecutive days"
    return false;
  }

  private async evaluateSequenceBasedCriteria(
    userId: string,
    criteria: BadgeCriteria
  ): Promise<boolean> {
    // Implement sequence-based criteria evaluation
    // Example: "completed 3 workflows in a row without errors"
    return false;
  }

  private async evaluateXpBasedCriteria(
    userId: string,
    criteria: BadgeCriteria
  ): Promise<boolean> {
    const xp = await this.powerOpsService.getXp(
      { userId, roles: [], paimTier: 'standard' } as any,
      userId,
      EntityType.User
    );
    return xp.currentXp >= (criteria.requiredXp || 0);
  }

  private async evaluateBadgeBasedCriteria(
    userId: string,
    criteria: BadgeCriteria
  ): Promise<boolean> {
    const badges = await this.powerOpsService.getBadges(
      { userId, roles: [], paimTier: 'standard' } as any,
      userId,
      EntityType.User
    );
    return criteria.requiredBadgeIds?.every(requiredId =>
      badges.some(badge => badge.id === requiredId)
    ) ?? true;
  }

  async evaluateBadgeEligibility(userId: string, badge: Badge | GamificationBadge): Promise<boolean> {
    // Check if badge has criteria (GamificationBadge)
    const gamificationBadge = badge as GamificationBadge;
    if (!gamificationBadge.criteria) return false;

    const criteria = gamificationBadge.criteria;
    const results = await Promise.all([
      this.evaluateTimeBasedCriteria(userId, criteria),
      this.evaluateSequenceBasedCriteria(userId, criteria),
      this.evaluateXpBasedCriteria(userId, criteria),
      this.evaluateBadgeBasedCriteria(userId, criteria),
    ]);

    return results.every(result => result);
  }

  async checkAndAwardBadges(userId: string): Promise<Badge[]> {
    const allBadges = await this.powerOpsService.getAllBadges(
      { userId, roles: [], paimTier: 'standard' } as any
    );
    
    const awardedBadges: Badge[] = [];
    for (const badge of allBadges) {
      const isEligible = await this.evaluateBadgeEligibility(userId, badge);
      if (isEligible) {
        await this.powerOpsService.awardBadge(
          { userId, roles: [], paimTier: 'standard' } as any,
          {
            entityId: userId,
            entityType: EntityType.User,
            badgeId: badge.id
          }
        );
        awardedBadges.push(badge);

        await this.notificationService.createNotification({
          userId,
          type: 'success',
          message: `New Badge Earned: ${badge.name}!`,
        });
      }
    }
    return awardedBadges;
  }
}