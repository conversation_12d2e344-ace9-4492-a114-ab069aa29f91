"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = void 0;
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger")); // Assuming a logger is configured
const errorHandler = (err, req, res, next) => {
    if (err instanceof errors_1.BaseError) {
        logger_1.default.error(`Operational Error: ${err.message}`, {
            statusCode: err.statusCode,
            errorCode: err.errorCode,
            details: err.details,
            path: req.path,
            method: req.method,
            ip: req.ip,
        });
        return res.status(err.statusCode).json({
            status: 'error',
            code: err.errorCode,
            message: err.message,
            details: err.details,
        });
    }
    // Handle unexpected errors
    logger_1.default.error(`Unexpected Error: ${err.message}`, {
        stack: err.stack,
        path: req.path,
        method: req.method,
        ip: req.ip,
    });
    return res.status(500).json({
        status: 'error',
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred.',
    });
};
exports.errorHandler = errorHandler;
