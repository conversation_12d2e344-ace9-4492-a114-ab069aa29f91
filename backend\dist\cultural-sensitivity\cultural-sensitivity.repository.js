"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CulturalSensitivityRepository = void 0;
class CulturalSensitivityRepository {
    db;
    constructor(db) {
        this.db = db;
    }
    async getLocalizationSettings(entityId, entityType) {
        return this.db('localization_settings')
            .where({ entity_id: entityId, entity_type: entityType })
            .first();
    }
    async updateLocalizationSettings(entityId, entityType, settings) {
        const [updatedSettings] = await this.db('localization_settings')
            .where({ entity_id: entityId, entity_type: entityType })
            .update({
            preferred_language: settings.preferredLanguage,
            timezone: settings.timezone,
            date_format: settings.dateFormat,
            time_format: settings.timeFormat,
            currency: settings.currency,
            cultural_preferences: JSON.stringify(settings.culturalPreferences),
        }, ['*']);
        return updatedSettings;
    }
    async createLocalizationSettings(settings) {
        const [newSettings] = await this.db('localization_settings').insert({
            entity_id: settings.entityId,
            entity_type: settings.entityType,
            preferred_language: settings.preferredLanguage,
            timezone: settings.timezone,
            date_format: settings.dateFormat,
            time_format: settings.timeFormat,
            currency: settings.currency,
            cultural_preferences: JSON.stringify(settings.culturalPreferences),
        }, ['*']);
        return newSettings;
    }
    async getCulturalContext(locale, entityId, entityType) {
        let query = this.db('cultural_context').where({ locale });
        if (entityId && entityType) {
            query = query.andWhere({ entity_id: entityId, entity_type: entityType });
        }
        return query.first();
    }
    async createOrUpdateCulturalContext(context, entityId, entityType) {
        const dataToInsert = {
            locale: context.locale,
            cultural_norms: JSON.stringify(context.culturalNorms),
            common_phrases: JSON.stringify(context.commonPhrases),
            sensitive_topics: JSON.stringify(context.sensitiveTopics),
            historical_context: context.historicalContext,
            social_etiquette: context.socialEtiquette,
            business_practices: context.businessPractices,
            last_updated: new Date().toISOString(),
            entity_id: entityId,
            entity_type: entityType,
        };
        const [culturalContext] = await this.db('cultural_context')
            .insert(dataToInsert)
            .onConflict(['locale', 'entity_id', 'entity_type'])
            .merge()
            .returning('*');
        return culturalContext;
    }
    // Placeholder for dialect detection data storage
    async saveDialectDetectionResult(result) {
        await this.db('dialect_detection_logs').insert({
            text: result.text,
            detected_dialect: result.detectedDialect,
            confidence: result.confidence,
            possible_dialects: JSON.stringify(result.possibleDialects),
            timestamp: new Date().toISOString(),
        });
    }
    // Placeholder for cultural pattern and rules storage
    async getCulturalPattern(patternId) {
        return this.db('cultural_patterns').where({ id: patternId }).first();
    }
    async saveCulturalPattern(pattern) {
        await this.db('cultural_patterns').insert(pattern);
    }
}
exports.CulturalSensitivityRepository = CulturalSensitivityRepository;
