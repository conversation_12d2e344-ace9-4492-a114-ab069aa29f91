import { Agent, AgentTask, AgentCapability } from '../core/Agent';
import { Config } from '../core/Config';
import logger from '../../config/logger';

export interface APIRequest {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  endpoint: string;
  headers?: Record<string, string>;
  body?: any;
  query?: Record<string, string>;
}

export interface APIResponse {
  status: number;
  headers: Record<string, string>;
  data: any;
  timestamp: string;
}

export interface RateLimitConfig {
  enabled: boolean;
  requestsPerMinute: number;
  burstLimit: number;
}

/**
 * CL-API Agent - Core API Interface Agent
 * Handles API request processing, rate limiting, and response formatting
 */
export class CLAPIAgent extends Agent {
  private requestCount: number = 0;
  private lastResetTime: number = Date.now();
  private rateLimitConfig: RateLimitConfig;

  constructor(config?: Config) {
    super(
      'cl-api-agent',
      'CL-API',
      'core',
      config
    );

    // Initialize rate limiting configuration
    this.rateLimitConfig = {
      enabled: this.getConfig().get('rateLimit.enabled', true),
      requestsPerMinute: this.getConfig().get('rateLimit.requestsPerMinute', 100),
      burstLimit: this.getConfig().get('rateLimit.burstLimit', 10)
    };

    // Add API-specific capabilities
    this.addCapability({
      name: 'api_request_handling',
      version: '1.0.0',
      description: 'Handle HTTP API requests',
      enabled: true
    });

    this.addCapability({
      name: 'rate_limiting',
      version: '1.0.0',
      description: 'Apply rate limiting to requests',
      enabled: this.rateLimitConfig.enabled
    });

    this.addCapability({
      name: 'response_formatting',
      version: '1.0.0',
      description: 'Format API responses',
      enabled: true
    });

    logger.info('CL-API Agent initialized with rate limiting configuration');
  }

  /**
   * Execute API-related tasks
   */
  protected async executeTask(task: AgentTask): Promise<any> {
    switch (task.type) {
      case 'api_request':
        return await this.handleAPIRequest(task.payload as APIRequest);
      
      case 'format_response':
        return await this.formatResponse(task.payload);
      
      case 'check_rate_limit':
        return await this.checkRateLimit(task.payload.clientId);
      
      case 'get_api_status':
        return await this.getAPIStatus();
      
      default:
        return await super.executeTask(task);
    }
  }

  /**
   * Handle API request processing
   */
  private async handleAPIRequest(request: APIRequest): Promise<APIResponse> {
    logger.info(`Processing ${request.method} request to ${request.endpoint}`);

    try {
      // Check rate limiting
      if (this.rateLimitConfig.enabled) {
        const rateLimitCheck = await this.checkRateLimit('default');
        if (!rateLimitCheck.allowed) {
          throw new Error(`Rate limit exceeded: ${rateLimitCheck.message}`);
        }
      }

      // Validate request
      this.validateAPIRequest(request);

      // Process the request (mock implementation)
      const response = await this.processRequest(request);

      // Increment request count
      this.incrementRequestCount();

      logger.info(`API request processed successfully: ${request.method} ${request.endpoint}`);
      return response;

    } catch (error) {
      logger.error(`API request failed: ${error}`);
      throw error;
    }
  }

  /**
   * Validate API request
   */
  private validateAPIRequest(request: APIRequest): void {
    if (!request.method) {
      throw new Error('HTTP method is required');
    }

    if (!request.endpoint) {
      throw new Error('API endpoint is required');
    }

    if (!['GET', 'POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
      throw new Error(`Unsupported HTTP method: ${request.method}`);
    }

    // Validate endpoint format
    if (!request.endpoint.startsWith('/')) {
      throw new Error('Endpoint must start with /');
    }
  }

  /**
   * Process the actual API request (mock implementation)
   */
  private async processRequest(request: APIRequest): Promise<APIResponse> {
    // This is a mock implementation
    // In a real scenario, this would make actual HTTP requests
    
    const mockData = {
      message: `Mock response for ${request.method} ${request.endpoint}`,
      requestData: request.body || request.query,
      timestamp: new Date().toISOString()
    };

    return {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-Agent-Id': this.id,
        'X-Request-Id': `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      },
      data: mockData,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Format API response
   */
  private async formatResponse(data: any): Promise<any> {
    const formattedResponse = {
      success: true,
      data: data,
      metadata: {
        agent: this.name,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    };

    logger.debug('Response formatted by CL-API Agent');
    return formattedResponse;
  }

  /**
   * Check rate limiting
   */
  private async checkRateLimit(clientId: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    message?: string;
  }> {
    if (!this.rateLimitConfig.enabled) {
      return {
        allowed: true,
        remaining: this.rateLimitConfig.requestsPerMinute,
        resetTime: this.lastResetTime + 60000
      };
    }

    const now = Date.now();
    const timeSinceReset = now - this.lastResetTime;

    // Reset counter every minute
    if (timeSinceReset >= 60000) {
      this.requestCount = 0;
      this.lastResetTime = now;
    }

    const remaining = Math.max(0, this.rateLimitConfig.requestsPerMinute - this.requestCount);
    const allowed = this.requestCount < this.rateLimitConfig.requestsPerMinute;

    if (!allowed) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: this.lastResetTime + 60000,
        message: `Rate limit of ${this.rateLimitConfig.requestsPerMinute} requests per minute exceeded`
      };
    }

    return {
      allowed: true,
      remaining,
      resetTime: this.lastResetTime + 60000
    };
  }

  /**
   * Get API status
   */
  private async getAPIStatus(): Promise<any> {
    const rateLimitStatus = await this.checkRateLimit('status');
    
    return {
      agent: {
        id: this.id,
        name: this.name,
        type: this.type,
        active: this.getIsActive()
      },
      rateLimit: {
        enabled: this.rateLimitConfig.enabled,
        requestsPerMinute: this.rateLimitConfig.requestsPerMinute,
        currentCount: this.requestCount,
        remaining: rateLimitStatus.remaining,
        resetTime: new Date(rateLimitStatus.resetTime).toISOString()
      },
      metrics: this.getMetrics(),
      capabilities: this.getCapabilities(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Increment request count
   */
  private incrementRequestCount(): void {
    this.requestCount++;
  }

  /**
   * Update rate limit configuration
   */
  public updateRateLimitConfig(config: Partial<RateLimitConfig>): void {
    this.rateLimitConfig = { ...this.rateLimitConfig, ...config };
    
    // Update capability status
    const rateLimitCapability = this.getCapabilities().find(c => c.name === 'rate_limiting');
    if (rateLimitCapability) {
      rateLimitCapability.enabled = this.rateLimitConfig.enabled;
    }

    logger.info('Rate limit configuration updated', this.rateLimitConfig);
  }

  /**
   * Get current rate limit configuration
   */
  public getRateLimitConfig(): RateLimitConfig {
    return { ...this.rateLimitConfig };
  }

  /**
   * Reset rate limit counters
   */
  public resetRateLimit(): void {
    this.requestCount = 0;
    this.lastResetTime = Date.now();
    logger.info('Rate limit counters reset');
  }
}
