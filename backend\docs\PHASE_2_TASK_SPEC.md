# Phase 2: Database and Authentication - Task Specification

## Objective
Establish data layer connectivity and implement secure authentication system

## Priority
High - Required for API functionality

## Estimated Time
3-4 hours

## Prerequisites
- Phase 1 completed successfully
- Express server running without errors
- TypeScript compilation working
- Basic health check endpoint functional

## Detailed Tasks

### Task 2.1: Database Configuration and Connection
**Objective**: Establish reliable database connectivity

**Actions Required**:
1. **Knex Configuration Fix**:
   - Update Dockerfile to reference `knexfile.js` instead of `knexfile.ts`
   - Verify knex configuration in `knexfile.js`
   - Test database connection parameters
   - Ensure proper environment variable usage

2. **Database Connection Testing**:
   - Create database connection test utility
   - Verify connection to configured database
   - Test connection pooling configuration
   - Add connection retry logic

3. **Migration System Verification**:
   - Review existing migration files
   - Test migration execution: `npm run migrate:latest`
   - Verify database schema creation
   - Test rollback functionality if needed

**Files to Modify**:
- `Dockerfile` (knexfile reference)
- `knexfile.js` (connection configuration)
- `src/config/database-adapter.ts`
- `src/database/db.ts`

**Expected Outcome**: Database connection established and migrations run successfully

### Task 2.2: Authentication Middleware Implementation
**Objective**: Implement secure JWT-based authentication

**Actions Required**:
1. **JWT Token Handling**:
   - Fix JWT payload type definitions in `auth.types.ts`
   - Resolve type mismatches in `auth.utils.ts`
   - Implement proper token validation logic
   - Add token refresh mechanism

2. **Authentication Middleware**:
   - Fix authentication middleware in `middleware/authentication.ts`
   - Ensure proper request augmentation for user context
   - Add proper error handling for invalid tokens
   - Implement rate limiting for auth endpoints

3. **User Role Management**:
   - Fix role type definitions and validation
   - Implement proper role-based access control
   - Test user permission checking
   - Add role hierarchy support

**Files to Modify**:
- `src/auth/auth.types.ts`
- `src/auth/auth.utils.ts`
- `src/middleware/authentication.ts`
- `src/auth/auth.service.ts`
- `src/types/express-augmentation.d.ts`

**Expected Outcome**: JWT authentication working with proper user context

### Task 2.3: Authorization Service Fixes
**Objective**: Implement proper authorization checks

**Actions Required**:
1. **Authorization Service Type Fixes**:
   - Resolve parameter type issues in `authorization.service.ts`
   - Fix resource access checking logic
   - Implement proper permission validation
   - Add support for team and organization-level permissions

2. **Resource Access Control**:
   - Fix type mismatches in resource access checks
   - Implement proper ownership validation
   - Add support for shared resource access
   - Test authorization for different user roles

3. **Integration with Services**:
   - Fix authorization calls in agent service
   - Update PowerOps service authorization checks
   - Ensure consistent authorization patterns
   - Add authorization to all protected endpoints

**Files to Modify**:
- `src/auth/authorization.service.ts`
- `src/agent/agent.service.ts`
- `src/powerops/powerops.service.ts`

**Expected Outcome**: Authorization service working with proper access control

### Task 2.4: Database Repository Layer
**Objective**: Ensure database operations work correctly

**Actions Required**:
1. **Repository Pattern Implementation**:
   - Verify repository implementations
   - Test basic CRUD operations
   - Ensure proper error handling
   - Add transaction support where needed

2. **Data Model Validation**:
   - Verify database schema matches TypeScript types
   - Test data insertion and retrieval
   - Validate foreign key relationships
   - Ensure proper data validation

3. **Repository Testing**:
   - Test agent repository operations
   - Verify PAIM repository functionality
   - Test PowerOps repository methods
   - Validate audit trail functionality

**Files to Verify**:
- `src/agent/agent.repository.ts`
- `src/paim/paim.repository.ts`
- `src/powerops/powerops.repository.ts`
- `src/audit/audit.repository.ts`

**Expected Outcome**: All repository operations working with database

### Task 2.5: Basic API Endpoint Testing
**Objective**: Verify authenticated API endpoints work

**Actions Required**:
1. **Authentication Endpoints**:
   - Test user login endpoint
   - Verify token generation and validation
   - Test user registration if implemented
   - Validate logout functionality

2. **Protected Endpoint Testing**:
   - Test agent CRUD operations with authentication
   - Verify authorization checks work
   - Test error responses for unauthorized access
   - Validate proper HTTP status codes

3. **Integration Testing**:
   - Test end-to-end user authentication flow
   - Verify database operations with authenticated users
   - Test role-based access to different endpoints
   - Validate error handling and logging

**Expected Outcome**: Authenticated API endpoints working correctly

## Quality Control Checklist

### Database Verification
- [ ] Database connection established successfully
- [ ] Migrations run without errors
- [ ] Basic CRUD operations work
- [ ] Connection pooling configured properly
- [ ] Database schema matches application models

### Authentication Testing
- [ ] JWT token generation works
- [ ] Token validation middleware functional
- [ ] User context properly attached to requests
- [ ] Invalid token handling works correctly
- [ ] Rate limiting prevents abuse

### Authorization Testing
- [ ] Resource access control working
- [ ] Role-based permissions enforced
- [ ] Ownership validation functional
- [ ] Team/organization permissions work
- [ ] Unauthorized access properly blocked

### API Functionality
- [ ] Login endpoint returns valid tokens
- [ ] Protected endpoints require authentication
- [ ] Proper HTTP status codes returned
- [ ] Error messages are informative
- [ ] API responses follow consistent format

### Integration Testing
- [ ] End-to-end authentication flow works
- [ ] Database operations with auth context
- [ ] Multiple user roles tested
- [ ] Error scenarios handled gracefully
- [ ] Logging captures important events

## Success Criteria
1. **Database Connectivity**: Reliable connection and migrations working
2. **Authentication System**: JWT-based auth fully functional
3. **Authorization Control**: Proper access control implemented
4. **API Security**: Protected endpoints working correctly
5. **Data Operations**: CRUD operations with proper auth context

## Handoff to Phase 3
Upon successful completion:
1. Provide database connection verification report
2. Document authentication and authorization setup
3. Confirm protected API endpoints are functional
4. Provide test credentials for Phase 3 testing
5. Hand off secure, data-connected backend to Phase 3

## Risk Mitigation
- Test database connection before proceeding with migrations
- Implement proper error handling for auth failures
- Use test database for initial testing
- Keep backup of working configuration
- Document all authentication flows for troubleshooting

---

**Phase**: 2 of 4
**Dependencies**: Phase 1 completion
**Next Phase**: Feature Integration
**Estimated Completion**: 3-4 hours from Phase 1 completion
