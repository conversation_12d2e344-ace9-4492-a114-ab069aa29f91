"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.raw(`
    -- PowerOps Gamification and Billing Tables

    -- Table: PowerOpsUsage
    -- Tracks usage of PowerOps features by users within a tenant.
    CREATE TABLE PowerOpsUsage (
        usage_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES Users(user_id) ON DELETE CASCADE,
        feature_name VARCHAR(255) NOT NULL, -- e.g., 'AgentExecution', 'WorkflowRun', 'DataProcessing'
        usage_amount DECIMAL(18, 4) NOT NULL, -- Quantity of usage (e.g., tokens, compute time, API calls)
        unit_of_measure VARCHAR(50) NOT NULL, -- e.g., 'tokens', 'ms', 'calls'
        cost_per_unit DECIMAL(10, 6), -- Cost associated with each unit of measure
        total_cost DECIMAL(18, 6),
        usage_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        metadata JSONB -- Additional details about the usage
    );

    -- Table: BillingRecords
    -- Stores aggregated billing information for PowerOps usage.
    CREATE TABLE BillingRecords (
        billing_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        billing_period_start DATE NOT NULL,
        billing_period_end DATE NOT NULL,
        total_amount DECIMAL(18, 6) NOT NULL,
        currency VARCHAR(10) NOT NULL,
        status VARCHAR(50) NOT NULL, -- e.g., 'pending', 'billed', 'paid', 'failed'
        invoice_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_tenant_billing_period UNIQUE (tenant_id, billing_period_start, billing_period_end)
    );

    -- Table: UserXP
    -- Tracks experience points (XP) for users.
    CREATE TABLE UserXP (
        user_id UUID PRIMARY KEY REFERENCES Users(user_id) ON DELETE CASCADE,
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        current_xp BIGINT DEFAULT 0 NOT NULL,
        level INT DEFAULT 1 NOT NULL,
        last_xp_gain_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_userxp_tenant FOREIGN KEY (tenant_id) REFERENCES Tenants(tenant_id)
    );

    -- Table: Badges
    -- Defines available badges in the gamification system.
    CREATE TABLE Badges (
        badge_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        badge_name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        image_url TEXT,
        xp_reward INT,
        criteria JSONB, -- JSON defining the criteria to earn the badge
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: UserBadges
    -- Records badges earned by users.
    CREATE TABLE UserBadges (
        user_badge_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES Users(user_id) ON DELETE CASCADE,
        badge_id UUID NOT NULL REFERENCES Badges(badge_id) ON DELETE CASCADE,
        earned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_user_badge UNIQUE (user_id, badge_id)
    );

    -- Table: Achievements
    -- Defines various achievements that users can unlock.
    CREATE TABLE Achievements (
        achievement_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        achievement_name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        xp_reward INT,
        badge_reward_id UUID REFERENCES Badges(badge_id), -- Optional badge reward
        criteria JSONB, -- JSON defining the criteria to unlock the achievement
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: UserAchievements
    -- Records achievements unlocked by users.
    CREATE TABLE UserAchievements (
        user_achievement_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES Users(user_id) ON DELETE CASCADE,
        achievement_id UUID NOT NULL REFERENCES Achievements(achievement_id) ON DELETE CASCADE,
        unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_user_achievement UNIQUE (user_id, achievement_id)
    );

    -- Table: Streaks
    -- Tracks user streaks for continuous activities.
    CREATE TABLE Streaks (
        streak_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES Users(user_id) ON DELETE CASCADE,
        streak_type VARCHAR(100) NOT NULL, -- e.g., 'daily_login', 'workflow_completion'
        current_length INT DEFAULT 0 NOT NULL,
        longest_length INT DEFAULT 0 NOT NULL,
        last_activity_at TIMESTAMP WITH TIME ZONE,
        started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_user_streak_type UNIQUE (user_id, streak_type)
    );

    -- Table: BudgetControls
    -- Manages budget limits for tenants or specific users.
    CREATE TABLE BudgetControls (
        budget_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        user_id UUID REFERENCES Users(user_id), -- Can be null for tenant-level budget
        budget_limit DECIMAL(18, 6) NOT NULL,
        currency VARCHAR(10) NOT NULL,
        period_type VARCHAR(50) NOT NULL, -- e.g., 'monthly', 'quarterly', 'yearly'
        period_start_date DATE NOT NULL,
        period_end_date DATE NOT NULL,
        current_spend DECIMAL(18, 6) DEFAULT 0 NOT NULL,
        alert_threshold DECIMAL(5, 2), -- Percentage of budget limit to trigger alert
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT uq_tenant_user_budget UNIQUE (tenant_id, user_id, period_type, period_start_date)
    );

    -- Indexes for performance optimization
    CREATE INDEX idx_poweropsusage_tenant_id ON PowerOpsUsage(tenant_id);
    CREATE INDEX idx_poweropsusage_user_id ON PowerOpsUsage(user_id);
    CREATE INDEX idx_poweropsusage_feature_name ON PowerOpsUsage(feature_name);
    CREATE INDEX idx_poweropsusage_timestamp ON PowerOpsUsage(usage_timestamp);
    CREATE INDEX idx_billingrecords_tenant_id ON BillingRecords(tenant_id);
    CREATE INDEX idx_billingrecords_period_start ON BillingRecords(billing_period_start);
    CREATE INDEX idx_userxp_tenant_id ON UserXP(tenant_id);
    CREATE INDEX idx_userbadges_user_id ON UserBadges(user_id);
    CREATE INDEX idx_userbadges_badge_id ON UserBadges(badge_id);
    CREATE INDEX idx_userachievements_user_id ON UserAchievements(user_id);
    CREATE INDEX idx_userachievements_achievement_id ON UserAchievements(achievement_id);
    CREATE INDEX idx_streaks_user_id ON Streaks(user_id);
    CREATE INDEX idx_streaks_streak_type ON Streaks(streak_type);
    CREATE INDEX idx_budgetcontrols_tenant_id ON BudgetControls(tenant_id);
    CREATE INDEX idx_budgetcontrols_user_id ON BudgetControls(user_id);
  `);
}
async function down(knex) {
    await knex.schema.raw(`
    DROP TABLE IF EXISTS BudgetControls;
    DROP TABLE IF EXISTS Streaks;
    DROP TABLE IF EXISTS UserAchievements;
    DROP TABLE IF EXISTS Achievements;
    DROP TABLE IF EXISTS UserBadges;
    DROP TABLE IF EXISTS Badges;
    DROP TABLE IF EXISTS UserXP;
    DROP TABLE IF EXISTS BillingRecords;
    DROP TABLE IF EXISTS PowerOpsUsage;
  `);
}
