// backend/src/auth/authorization.service.ts
import { UserRole, ROLE_PERMISSIONS } from './roles';
import { Permission, PERMISSIONS } from './permissions';
import { PAIM_TIER_PERMISSIONS, PAIMTier } from '../paim/paim.permissions';
import { JwtPayload } from './auth.types'; // Import JwtPayload

export class AuthorizationService {
  /**
   * Checks if a user has a specific role.
   * @param userRoles The roles assigned to the user.
   * @param requiredRole The role to check against.
   * @returns True if the user has the required role, false otherwise.
   */
  public hasRole(userRoles: UserRole[], requiredRole: UserRole): boolean {
    return userRoles.includes(requiredRole);
  }

  /**
   * Checks if a user has a specific permission based on their roles and PAIM tier.
   * @param userRoles The roles assigned to the user.
   * @param userPaimTier The PAIM tier of the user's organization.
   * @param requiredPermission The permission to check for.
   * @returns True if the user has the required permission, false otherwise.
   */
  public hasPermission(
    userRoles: UserRole[],
    userPaimTier: PAIMTier,
    requiredPermission: Permission,
  ): boolean {
    // Check role-based permissions
    for (const role of userRoles) {
      const permissionsForRole = ROLE_PERMISSIONS[role];
      if (permissionsForRole && permissionsForRole.includes(requiredPermission)) {
        return true;
      }
    }

    // Check PAIM tier-based permissions
    const paimTierPermissions = PAIM_TIER_PERMISSIONS[userPaimTier];
    if (paimTierPermissions && paimTierPermissions.includes(requiredPermission)) {
      return true;
    }

    return false;
  }

  /**
   * Checks if a user has any of the required permissions.
   * @param userRoles The roles assigned to the user.
   * @param userPaimTier The PAIM tier of the user's organization.
   * @param requiredPermissions An array of permissions, any of which would grant access.
   * @returns True if the user has at least one of the required permissions, false otherwise.
   */
  public hasAnyPermission(
    userRoles: UserRole[],
    userPaimTier: PAIMTier,
    requiredPermissions: Permission[],
  ): boolean {
    for (const permission of requiredPermissions) {
      if (this.hasPermission(userRoles, userPaimTier, permission)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Checks if a user has all of the required permissions.
   * @param userRoles The roles assigned to the user.
   * @param userPaimTier The PAIM tier of the user's organization.
   * @param requiredPermissions An array of permissions, all of which must be present.
   * @returns True if the user has all of the required permissions, false otherwise.
   */
  public hasAllPermissions(
    userRoles: UserRole[],
    userPaimTier: PAIMTier,
    requiredPermissions: Permission[],
  ): boolean {
    for (const permission of requiredPermissions) {
      if (!this.hasPermission(userRoles, userPaimTier, permission)) {
        return false;
      }
    }
    return true;
  }

  /**
   * Checks if a user can access a specific resource based on ownership, team, or organization.
   * This method assumes the resource object has `ownerId`, `organizationId`, and optionally `teamId`.
   * It also assumes the user object (JwtPayload) has `userId`, `tenantId` (which can be used as organizationId), and `teamId` if applicable.
   * @param user The authenticated user's JwtPayload.
   * @param resource The resource being accessed (e.g., PaimInstance, Workflow, Agent).
   * @param action The action being performed ('read', 'update', 'delete', 'manage').
   * @returns True if the user can access the resource, false otherwise.
   */
  public canAccessResource(
    user: JwtPayload,
    resource: { ownerId?: string; organizationId?: string; teamId?: string; },
    action: 'read' | 'update' | 'delete' | 'manage',
  ): boolean {
    // 1. Check if the user is the owner of the resource
    if (resource.ownerId === user.userId) {
      return true;
    }

    // 2. Check if the user is in the same organization as the resource owner
    // Assuming user.tenantId represents the user's organizationId
    if (resource.organizationId === user.tenantId) {
      // If within the same organization, check if the user has organization-level permissions
      // For example, OrgAdmin might have 'manage' permission over all resources in their organization.
      // This would typically be covered by role-based permissions, but can be explicitly checked here.
      if (user.roles.includes(UserRole.OrgAdmin) || user.roles.includes(UserRole.SuperAdmin)) {
        return true; // OrgAdmin/SuperAdmin can manage all resources in their organization
      }
      // If not OrgAdmin/SuperAdmin, check if they are in the same team (if applicable)
      if (resource.teamId && user.teamId && resource.teamId === user.teamId) {
        return true; // User is in the same team as the resource
      }
    }

    // 3. Fallback to role/tier-based permissions for generic access if no specific ownership/team/org match
    // This means if a user has a global permission (e.g., PAIM_VIEW_ALL), they can access it regardless of ownership.
    // This check should ideally happen before calling canAccessResource, but included here for completeness.
    // For example, a SuperAdmin can access any resource.
    if (this.hasPermission(user.roles, user.paimTier, PERMISSIONS.SYSTEM_ADMIN_ACCESS)) {
      return true;
    }

    return false;
  }
}

export const authorizationService = new AuthorizationService();