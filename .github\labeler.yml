# .github/labeler.yml

documentation:
  - 'docs/*'
  - 'README.md'

backend:
  - 'backend/**/*'
  - 'knexfile.ts'
  - 'package.json' # if changes affect backend dependencies

frontend:
  - 'frontend/**/*' # Adjust path if your frontend is in a different directory

tests:
  - '**/*.test.ts'
  - '**/*.spec.ts'
  - 'jest.config.js'

ci-cd:
  - '.github/workflows/*'
  - '.github/dependabot.yml'

database:
  - 'db/**/*'
  - 'backend/migrations/*'
  - 'backend/seeds/*'

api-specs:
  - 'api_specs/**/*'

security:
  - 'semgrep.yml' # If you have a dedicated Semgrep config
  - '.github/workflows/ci.yaml' # If security steps are modified
  - 'backend/src/auth/*' # If auth/security logic changes