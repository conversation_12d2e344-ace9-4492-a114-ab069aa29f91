"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasRole = exports.authorize = void 0;
const authorization_service_1 = require("../auth/authorization.service");
const errors_1 = require("../utils/errors"); // Using AuthorizationError for authorization failures
/**
 * Middleware to check if the user has the required permissions.
 * @param requiredPermissions An array of permissions, any of which would grant access.
 */
const authorize = (requiredPermissions) => {
    return (req, res, next) => {
        // In a real application, user information would be populated by an authentication middleware
        // For now, we'll use a placeholder or assume it's already available from a previous middleware
        const user = req.user;
        if (!user) {
            // If no user is authenticated, return 401 Unauthorized
            return next(new errors_1.AuthorizationError('Unauthorized: No user authenticated', 401));
        }
        const { roles, paimTier } = user;
        if (!authorization_service_1.authorizationService.hasAnyPermission(roles, paimTier, requiredPermissions)) {
            // If the user does not have any of the required permissions, return 403 Forbidden
            return next(new errors_1.AuthorizationError('Forbidden: Insufficient permissions', 403));
        }
        next(); // User has permission, proceed to the next middleware/route handler
    };
};
exports.authorize = authorize;
/**
 * Middleware to check if the user has a specific role.
 * @param requiredRole The role to check against.
 */
const hasRole = (requiredRole) => {
    return (req, res, next) => {
        const user = req.user;
        if (!user) {
            return next(new errors_1.AuthorizationError('Unauthorized: No user authenticated', 401));
        }
        if (!authorization_service_1.authorizationService.hasRole(user.roles, requiredRole)) {
            return next(new errors_1.AuthorizationError(`Forbidden: Requires ${requiredRole} role`, 403));
        }
        next();
    };
};
exports.hasRole = hasRole;
// TODO: Implement resource-level authorization middleware
// This would involve fetching the resource and checking ownership/team/org context
// export const authorizeResource = (
//   action: 'read' | 'update' | 'delete' | 'manage',
//   getResource: (req: Request) => Promise<any> // Function to fetch the resource from the request
// ) => {
//   return async (req: Request, res: Response, next: NextFunction) => {
//     const user = req.user;
//     if (!user) {
//       return next(new AppError('Unauthorized: No user authenticated', 401));
//     }
//     try {
//       const resource = await getResource(req);
//       if (!resource) {
//         return next(new AppError('Resource not found', 404));
//       }
//       if (!authorizationService.canAccessResource(user, resource, action)) {
//         return next(new AppError('Forbidden: Cannot access this resource', 403));
//       }
//       next();
//     } catch (error) {
//       next(new AppError('Error checking resource access', 500));
//     }
//   };
// };
