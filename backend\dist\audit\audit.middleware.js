"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditMiddleware = void 0;
const audit_service_1 = require("./audit.service");
const audit_types_1 = require("./audit.types");
const auditTrailService = new audit_service_1.AuditTrailService();
const auditMiddleware = (req, res, next) => {
    const startTime = process.hrtime.bigint();
    // Capture request details
    const tenantId = req.headers['x-tenant-id'] || 'unknown-tenant';
    const userId = req.user?.userId || 'anonymous'; // Assuming user info is populated by auth middleware
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown-ip';
    const userAgent = req.get('User-Agent') || 'unknown-user-agent';
    const operationType = `${req.method} ${req.path}`;
    // Function to mask sensitive data (placeholder)
    const maskSensitiveData = (data) => {
        if (data && typeof data === 'object') {
            const sensitiveFields = ['password', 'accessToken', 'refreshToken', 'creditCardNumber'];
            const maskedData = { ...data };
            for (const field of sensitiveFields) {
                if (maskedData[field]) {
                    maskedData[field] = '[MASKED]';
                }
            }
            return maskedData;
        }
        return data;
    };
    // Log audit event after the response has finished
    res.on('finish', () => {
        const endTime = process.hrtime.bigint();
        const durationMs = Number(endTime - startTime) / 1_000_000; // Convert nanoseconds to milliseconds
        const statusCode = res.statusCode;
        const isError = statusCode >= 400;
        const severity = isError ? audit_types_1.AuditEventSeverity.HIGH : audit_types_1.AuditEventSeverity.INFO;
        const description = isError ? `Request failed with status ${statusCode}` : `Request completed with status ${statusCode}`;
        // Log the event asynchronously without awaiting, so it doesn't block the response
        auditTrailService.logEvent({
            tenantId,
            userId,
            category: audit_types_1.AuditEventCategory.SYSTEM_OPERATION, // Or more specific based on route
            severity,
            operationType,
            description,
            timestamp: new Date(),
            ipAddress: ipAddress, // Ensure ipAddress is explicitly assigned
            userAgent: userAgent || 'unknown-user-agent', // Ensure userAgent is explicitly assigned
            metadata: {
                requestHeaders: req.headers,
                requestBody: maskSensitiveData(req.body),
                responseStatusCode: statusCode,
                durationMs,
            },
        }).catch((error) => {
            // Log any errors from the audit logging itself
            console.error('Error logging audit event:', error);
        });
    });
    next();
};
exports.auditMiddleware = auditMiddleware;
