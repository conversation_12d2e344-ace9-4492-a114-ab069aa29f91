"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseAdapter = void 0;
const supabase_1 = require("./supabase");
const knex_1 = __importDefault(require("knex"));
const knex_config_1 = __importDefault(require("../config/knex.config"));
// Keep Knex for complex queries during migration period
const db = (0, knex_1.default)(knex_config_1.default[process.env.NODE_ENV || 'development']);
class DatabaseAdapter {
    // Supabase operations
    static async getUser(id) {
        console.log(`[DatabaseAdapter] getUser START - id: ${id}`);
        const { data, error } = await supabase_1.supabaseAdmin
            .from('users')
            .select('*')
            .eq('user_id', id) // Assuming user_id is the primary key
            .single();
        if (error && error.code !== 'PGRST116')
            throw error; // PGRST116 means no rows found
        console.log(`[DatabaseAdapter] getUser END - id: ${id}, found: ${!!data}`);
        return data;
    }
    static async getUserByEmail(email) {
        console.log(`[DatabaseAdapter] getUserByEmail START - email: ${email}`);
        const { data, error } = await supabase_1.supabaseAdmin
            .from('users')
            .select('*')
            .eq('email', email)
            .single();
        if (error && error.code !== 'PGRST116')
            throw error;
        console.log(`[DatabaseAdapter] getUserByEmail END - email: ${email}, found: ${!!data}`);
        return data;
    }
    static async getTenantCount() {
        console.log(`[DatabaseAdapter] getTenantCount START`);
        const { count, error } = await supabase_1.supabaseAdmin
            .from('tenants')
            .select('*', { count: 'exact', head: true })
            .is('deleted_at', null);
        if (error)
            throw error;
        console.log(`[DatabaseAdapter] getTenantCount END - count: ${count}`);
        return count || 0;
    }
    static async createTenant(tenant_name, schema_name, founder_club_flag, promo_start_date, promo_end_date, org_seats_allowed, org_seats_used) {
        console.log(`[DatabaseAdapter] createTenant START - name: ${tenant_name}`);
        const { data, error } = await supabase_1.supabaseAdmin
            .from('tenants')
            .insert({
            tenant_name,
            schema_name,
            founder_club_flag,
            promo_start_date: promo_start_date?.toISOString(),
            promo_end_date: promo_end_date?.toISOString(),
            org_seats_allowed,
            org_seats_used,
        })
            .select()
            .single();
        if (error)
            throw error;
        console.log(`[DatabaseAdapter] createTenant END - name: ${tenant_name}, id: ${data?.tenant_id}`);
        return data;
    }
    static async getRoleByName(tenantId, roleName) {
        console.log(`[DatabaseAdapter] getRoleByName START - tenant: ${tenantId}, role: ${roleName}`);
        const { data, error } = await supabase_1.supabaseAdmin
            .from('roles')
            .select('*')
            .eq('tenant_id', tenantId)
            .eq('role_name', roleName)
            .single();
        if (error && error.code !== 'PGRST116')
            throw error;
        console.log(`[DatabaseAdapter] getRoleByName END - tenant: ${tenantId}, role: ${roleName}, found: ${!!data}`);
        return data;
    }
    static async createRole(tenantId, roleName, description) {
        console.log(`[DatabaseAdapter] createRole START - tenant: ${tenantId}, role: ${roleName}`);
        const { data, error } = await supabase_1.supabaseAdmin
            .from('roles')
            .insert({
            tenant_id: tenantId,
            role_name: roleName,
            description,
        })
            .select()
            .single();
        if (error)
            throw error;
        console.log(`[DatabaseAdapter] createRole END - tenant: ${tenantId}, role: ${roleName}, id: ${data?.role_id}`);
        return data;
    }
    static async createUser(tenantId, email, passwordHash, firstName, lastName, paimTier) {
        console.log(`[DatabaseAdapter] createUser START - email: ${email}, tenant: ${tenantId}`);
        const { data, error } = await supabase_1.supabaseAdmin
            .from('users')
            .insert({
            tenant_id: tenantId,
            email,
            password_hash: passwordHash,
            first_name: firstName,
            last_name: lastName,
            paim_tier: paimTier,
        })
            .select()
            .single();
        if (error)
            throw error;
        console.log(`[DatabaseAdapter] createUser END - email: ${email}, id: ${data?.user_id}`);
        return data;
    }
    static async assignUserRole(userId, roleId) {
        console.log(`[DatabaseAdapter] assignUserRole START - user: ${userId}, role: ${roleId}`);
        const { error } = await supabase_1.supabaseAdmin
            .from('user_roles')
            .insert({
            user_id: userId,
            role_id: roleId,
        });
        if (error)
            throw error;
        console.log(`[DatabaseAdapter] assignUserRole END - user: ${userId}, role: ${roleId}`);
    }
    static async getUserRoles(userId) {
        console.log(`[DatabaseAdapter] getUserRoles START - user: ${userId}`);
        const { data, error } = await supabase_1.supabaseAdmin
            .from('user_roles')
            .select(`
        roles (
          role_id,
          role_name,
          description
        )
      `)
            .eq('user_id', userId);
        if (error)
            throw error;
        console.log(`[DatabaseAdapter] getUserRoles END - user: ${userId}, roles: ${data?.length}`);
        return data.map((item) => item.roles);
    }
    static async getUserProfile(userId) {
        const { data, error } = await supabase_1.supabaseAdmin
            .from('user_profiles')
            .select(`
        *,
        users (
          email,
          first_name,
          last_name
        )
      `)
            .eq('user_id', userId)
            .single();
        if (error)
            throw error;
        return data;
    }
    static async updateUserXP(userId, tenantId, xpGained, eventType, description) {
        // Start a transaction
        const { data: profile, error: profileError } = await supabase_1.supabaseAdmin
            .from('user_profiles')
            .select('xp, total_xp')
            .eq('user_id', userId)
            .eq('tenant_id', tenantId)
            .single();
        if (profileError)
            throw profileError;
        const newXP = profile.xp + xpGained;
        const newTotalXP = profile.total_xp + xpGained;
        // Update profile
        const { error: updateError } = await supabase_1.supabaseAdmin
            .from('user_profiles')
            .update({
            xp: newXP,
            total_xp: newTotalXP,
            last_activity: new Date().toISOString()
        })
            .eq('user_id', userId)
            .eq('tenant_id', tenantId);
        if (updateError)
            throw updateError;
        // Log XP event
        const { error: eventError } = await supabase_1.supabaseAdmin
            .from('xp_events')
            .insert({
            user_id: userId,
            tenant_id: tenantId,
            event_type: eventType,
            xp_gained: xpGained,
            description: description
        });
        if (eventError)
            throw eventError;
        return { newXP, newTotalXP };
    }
    // Complex queries still use Knex during migration
    static async getComplexBillingReport(tenantId, startDate, endDate) {
        return await db.raw(`
      SELECT 
        u.email,
        u.first_name,
        u.last_name,
        SUM(bu.usage_amount) as total_usage,
        SUM(bu.cost) as total_cost,
        COUNT(bu.id) as usage_events
      FROM users u
      LEFT JOIN billing_usage bu ON u.id = bu.user_id
      WHERE u.tenant_id = ?
        AND bu.billing_period_start >= ?
        AND bu.billing_period_end <= ?
      GROUP BY u.id, u.email, u.first_name, u.last_name
      ORDER BY total_cost DESC
    `, [tenantId, startDate, endDate]);
    }
    // Real-time subscriptions
    static subscribeToUserProfile(userId, callback) {
        return supabase_1.supabase
            .channel(`user_profile_${userId}`)
            .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'user_profiles',
            filter: `user_id=eq.${userId}`
        }, callback)
            .subscribe();
    }
    static subscribeToXPEvents(userId, callback) {
        return supabase_1.supabase
            .channel(`xp_events_${userId}`)
            .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'xp_events',
            filter: `user_id=eq.${userId}`
        }, callback)
            .subscribe();
    }
}
exports.DatabaseAdapter = DatabaseAdapter;
