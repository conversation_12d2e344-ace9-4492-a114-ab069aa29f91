"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const knex_1 = __importDefault(require("knex"));
const knex_config_1 = __importDefault(require("../config/knex.config"));
const logger_1 = __importDefault(require("../config/logger"));
const environment = process.env.NODE_ENV || 'development';
const config = knex_config_1.default[environment];
const db = (0, knex_1.default)(config);
db.raw('SELECT 1')
    .then(() => {
    logger_1.default.info('Database connected successfully!');
})
    .catch((error) => {
    logger_1.default.error('Database connection failed:', error);
    process.exit(1); // Exit the process if DB connection fails
});
exports.default = db;
