import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // mcp_servers table
  await knex.schema.createTable('mcp_servers', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.string('name').notNullable();
    table.string('base_url').notNullable().unique();
    table.string('protocol_version').notNullable();
    table.enum('status', ['active', 'inactive', 'unhealthy']).notNullable().defaultTo('inactive');
    table.timestamp('last_heartbeat').defaultTo(knex.fn.now());
    table.jsonb('capabilities').defaultTo('[]'); // Storing a snapshot of capabilities
    table.jsonb('metadata').defaultTo('{}');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
  });

  // mcp_capabilities table
  await knex.schema.createTable('mcp_capabilities', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
    table.enum('type', ['tool', 'resource', 'prompt_template']).notNullable();
    table.string('name').notNullable();
    table.text('description');
    table.jsonb('schema').defaultTo('{}'); // JSON schema for tool arguments, resource structure, or prompt template variables
    table.jsonb('metadata').defaultTo('{}');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.unique(['server_id', 'name', 'type']); // A server should have unique capability names per type
  });

  // mcp_request_logs table
  await knex.schema.createTable('mcp_request_logs', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
    table.uuid('capability_id').notNullable().references('id').inTable('mcp_capabilities').onDelete('CASCADE');
    table.jsonb('request_payload').notNullable();
    table.jsonb('response_payload').notNullable();
    table.enum('status', ['success', 'failure']).notNullable();
    table.text('error_message');
    table.integer('duration_ms').notNullable();
    table.timestamp('requested_at').defaultTo(knex.fn.now());
    table.index('server_id');
    table.index('capability_id');
    table.index('status');
    table.index('requested_at');
  });

  // mcp_metrics table
  await knex.schema.createTable('mcp_metrics', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
    table.enum('metric_type', ['latency', 'error_rate', 'throughput']).notNullable();
    table.double('value').notNullable();
    table.timestamp('timestamp').defaultTo(knex.fn.now());
    table.jsonb('metadata').defaultTo('{}');
    table.index('server_id');
    table.index('metric_type');
    table.index('timestamp');
  });

  // mcp_configurations table
  await knex.schema.createTable('mcp_configurations', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.string('key').notNullable().unique();
    table.text('value').notNullable();
    table.enum('type', ['string', 'number', 'boolean', 'json']).notNullable();
    table.text('description');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
  });

  // mcp_access_controls table
  await knex.schema.createTable('mcp_access_controls', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
    table.uuid('paim_tier_id').notNullable(); // Link to PAIM hierarchy - foreign key added in a later migration
    table.enum('permission', ['read', 'write', 'execute', 'admin']).notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.unique(['server_id', 'paim_tier_id']);
  });

  // mcp_rate_limits table
  await knex.schema.createTable('mcp_rate_limits', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
    table.uuid('paim_tier_id'); // Optional: apply to specific PAIM hierarchy - foreign key added in a later migration
    table.integer('limit').notNullable(); // requests per duration
    table.enum('duration', ['minute', 'hour', 'day']).notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.unique(['server_id', 'paim_tier_id']); // Unique per server, optionally per PAIM hierarchy
  });

  // mcp_quotas table
  await knex.schema.createTable('mcp_quotas', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
    table.uuid('paim_tier_id'); // Optional: apply to specific PAIM hierarchy - foreign key added in a later migration
    table.integer('quota').notNullable(); // total requests allowed
    table.integer('used').notNullable().defaultTo(0);
    table.enum('reset_interval', ['daily', 'weekly', 'monthly']).notNullable();
    table.timestamp('last_reset').defaultTo(knex.fn.now());
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.unique(['server_id', 'paim_tier_id']); // Unique per server, optionally per PAIM hierarchy
  });

  // mcp_notifications table
  await knex.schema.createTable('mcp_notifications', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
    table.enum('type', ['server_status', 'capability_update', 'error', 'rate_limit_exceeded']).notNullable();
    table.text('message').notNullable();
    table.timestamp('timestamp').defaultTo(knex.fn.now());
    table.boolean('read').notNullable().defaultTo(false);
    table.jsonb('metadata').defaultTo('{}');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.index('server_id');
    table.index('type');
    table.index('read');
    table.index('timestamp');
  });

  // mcp_audit_logs table (for MCP specific audit events, distinct from general audit_compliance)
  await knex.schema.createTable('mcp_audit_logs', table => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table.uuid('server_id').references('id').inTable('mcp_servers').onDelete('SET NULL'); // Can be null if server is deregistered
    table.uuid('user_id'); // User who initiated the action (if applicable)
    table.string('action').notNullable(); // e.g., 'server_registered', 'tool_executed', 'config_updated'
    table.jsonb('details').defaultTo('{}');
    table.timestamp('timestamp').defaultTo(knex.fn.now());
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.index('server_id');
    table.index('user_id');
    table.index('action');
    table.index('timestamp');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('mcp_audit_logs');
  await knex.schema.dropTableIfExists('mcp_notifications');
  await knex.schema.dropTableIfExists('mcp_quotas');
  await knex.schema.dropTableIfExists('mcp_rate_limits');
  await knex.schema.dropTableIfExists('mcp_access_controls');
  await knex.schema.dropTableIfExists('mcp_configurations');
  await knex.schema.dropTableIfExists('mcp_metrics');
  await knex.schema.dropTableIfExists('mcp_request_logs');
  await knex.schema.dropTableIfExists('mcp_capabilities');
  await knex.schema.dropTableIfExists('mcp_servers');
}