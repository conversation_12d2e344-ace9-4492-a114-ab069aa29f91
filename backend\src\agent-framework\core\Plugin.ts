import { Agent } from './Agent';
import { Config } from './Config';
import logger from '../../config/logger';

export interface PluginMetadata {
  name: string;
  version: string;
  description: string;
  author?: string;
  dependencies?: string[];
  capabilities?: string[];
}

export interface PluginHooks {
  beforeInstall?: (agent: Agent) => Promise<void> | void;
  afterInstall?: (agent: Agent) => Promise<void> | void;
  beforeUninstall?: (agent: Agent) => Promise<void> | void;
  afterUninstall?: (agent: Agent) => Promise<void> | void;
  onAgentStart?: (agent: Agent) => Promise<void> | void;
  onAgentStop?: (agent: Agent) => Promise<void> | void;
  onTaskExecute?: (agent: Agent, task: any) => Promise<void> | void;
}

export abstract class Plugin {
  public readonly metadata: PluginMetadata;
  protected config: Config;
  protected hooks: PluginHooks = {};
  private isInstalled: boolean = false;
  private installedAgents: Set<string> = new Set();

  constructor(metadata: PluginMetadata, config?: Config) {
    this.metadata = metadata;
    this.config = config || new Config();
    
    logger.debug(`Plugin ${this.metadata.name} v${this.metadata.version} created`);
  }

  /**
   * Get plugin name
   */
  public get name(): string {
    return this.metadata.name;
  }

  /**
   * Get plugin version
   */
  public get version(): string {
    return this.metadata.version;
  }

  /**
   * Check if plugin is installed
   */
  public get installed(): boolean {
    return this.isInstalled;
  }

  /**
   * Get list of agents this plugin is installed on
   */
  public getInstalledAgents(): string[] {
    return Array.from(this.installedAgents);
  }

  /**
   * Install the plugin on an agent
   */
  public async install(agent: Agent): Promise<void> {
    if (this.installedAgents.has(agent.id)) {
      throw new Error(`Plugin ${this.name} is already installed on agent ${agent.name}`);
    }

    try {
      // Run before install hook
      if (this.hooks.beforeInstall) {
        await this.hooks.beforeInstall(agent);
      }

      // Check dependencies
      await this.checkDependencies(agent);

      // Perform plugin-specific installation
      await this.onInstall(agent);

      // Register event listeners
      this.registerEventListeners(agent);

      // Mark as installed
      this.installedAgents.add(agent.id);
      this.isInstalled = true;

      // Run after install hook
      if (this.hooks.afterInstall) {
        await this.hooks.afterInstall(agent);
      }

      logger.info(`Plugin ${this.name} installed on agent ${agent.name}`);

    } catch (error) {
      logger.error(`Failed to install plugin ${this.name} on agent ${agent.name}:`, error);
      throw error;
    }
  }

  /**
   * Uninstall the plugin from an agent
   */
  public async uninstall(agent: Agent): Promise<void> {
    if (!this.installedAgents.has(agent.id)) {
      throw new Error(`Plugin ${this.name} is not installed on agent ${agent.name}`);
    }

    try {
      // Run before uninstall hook
      if (this.hooks.beforeUninstall) {
        await this.hooks.beforeUninstall(agent);
      }

      // Unregister event listeners
      this.unregisterEventListeners(agent);

      // Perform plugin-specific uninstallation
      await this.onUninstall(agent);

      // Mark as uninstalled
      this.installedAgents.delete(agent.id);
      
      if (this.installedAgents.size === 0) {
        this.isInstalled = false;
      }

      // Run after uninstall hook
      if (this.hooks.afterUninstall) {
        await this.hooks.afterUninstall(agent);
      }

      logger.info(`Plugin ${this.name} uninstalled from agent ${agent.name}`);

    } catch (error) {
      logger.error(`Failed to uninstall plugin ${this.name} from agent ${agent.name}:`, error);
      throw error;
    }
  }

  /**
   * Configure the plugin
   */
  public configure(config: Config | object): this {
    if (config instanceof Config) {
      this.config = config;
    } else {
      this.config.load(config);
    }

    logger.debug(`Plugin ${this.name} configured`);
    return this;
  }

  /**
   * Get plugin configuration
   */
  public getConfig(): Config {
    return this.config;
  }

  /**
   * Set plugin hooks
   */
  public setHooks(hooks: Partial<PluginHooks>): this {
    this.hooks = { ...this.hooks, ...hooks };
    logger.debug(`Plugin ${this.name} hooks updated`);
    return this;
  }

  /**
   * Check if plugin is compatible with agent
   */
  public async isCompatible(agent: Agent): Promise<boolean> {
    try {
      await this.checkCompatibility(agent);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get plugin status
   */
  public getStatus(): {
    installed: boolean;
    agentCount: number;
    metadata: PluginMetadata;
  } {
    return {
      installed: this.isInstalled,
      agentCount: this.installedAgents.size,
      metadata: { ...this.metadata }
    };
  }

  // Abstract methods to be implemented by concrete plugins

  /**
   * Plugin-specific installation logic
   */
  protected abstract onInstall(agent: Agent): Promise<void>;

  /**
   * Plugin-specific uninstallation logic
   */
  protected abstract onUninstall(agent: Agent): Promise<void>;

  /**
   * Check plugin compatibility with agent
   */
  protected async checkCompatibility(agent: Agent): Promise<void> {
    // Default implementation - can be overridden
    logger.debug(`Checking compatibility for plugin ${this.name} with agent ${agent.name}`);
  }

  /**
   * Check plugin dependencies
   */
  protected async checkDependencies(agent: Agent): Promise<void> {
    if (!this.metadata.dependencies || this.metadata.dependencies.length === 0) {
      return;
    }

    // Check if required capabilities are available
    const agentCapabilities = agent.getCapabilities().map(c => c.name);
    
    for (const dependency of this.metadata.dependencies) {
      if (!agentCapabilities.includes(dependency)) {
        throw new Error(`Plugin ${this.name} requires capability '${dependency}' which is not available on agent ${agent.name}`);
      }
    }

    logger.debug(`All dependencies satisfied for plugin ${this.name}`);
  }

  /**
   * Register event listeners on the agent
   */
  protected registerEventListeners(agent: Agent): void {
    if (this.hooks.onAgentStart) {
      agent.on('agent:start', this.hooks.onAgentStart);
    }

    if (this.hooks.onAgentStop) {
      agent.on('agent:stop', this.hooks.onAgentStop);
    }

    if (this.hooks.onTaskExecute) {
      agent.on('task:start', this.hooks.onTaskExecute);
    }

    logger.debug(`Event listeners registered for plugin ${this.name}`);
  }

  /**
   * Unregister event listeners from the agent
   */
  protected unregisterEventListeners(agent: Agent): void {
    if (this.hooks.onAgentStart) {
      agent.off('agent:start', this.hooks.onAgentStart);
    }

    if (this.hooks.onAgentStop) {
      agent.off('agent:stop', this.hooks.onAgentStop);
    }

    if (this.hooks.onTaskExecute) {
      agent.off('task:start', this.hooks.onTaskExecute);
    }

    logger.debug(`Event listeners unregistered for plugin ${this.name}`);
  }
}

/**
 * Plugin Registry for managing plugins
 */
export class PluginRegistry {
  private static instance: PluginRegistry;
  private plugins: Map<string, Plugin> = new Map();

  private constructor() {}

  public static getInstance(): PluginRegistry {
    if (!PluginRegistry.instance) {
      PluginRegistry.instance = new PluginRegistry();
    }
    return PluginRegistry.instance;
  }

  /**
   * Register a plugin
   */
  public register(plugin: Plugin): void {
    if (this.plugins.has(plugin.name)) {
      throw new Error(`Plugin ${plugin.name} is already registered`);
    }

    this.plugins.set(plugin.name, plugin);
    logger.info(`Plugin ${plugin.name} registered in registry`);
  }

  /**
   * Unregister a plugin
   */
  public unregister(pluginName: string): boolean {
    const removed = this.plugins.delete(pluginName);
    if (removed) {
      logger.info(`Plugin ${pluginName} unregistered from registry`);
    }
    return removed;
  }

  /**
   * Get a plugin by name
   */
  public get(pluginName: string): Plugin | undefined {
    return this.plugins.get(pluginName);
  }

  /**
   * Get all registered plugins
   */
  public getAll(): Plugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * Get plugins by capability
   */
  public getByCapability(capability: string): Plugin[] {
    return Array.from(this.plugins.values()).filter(plugin =>
      plugin.metadata.capabilities?.includes(capability)
    );
  }

  /**
   * Clear all plugins
   */
  public clear(): void {
    this.plugins.clear();
    logger.info('Plugin registry cleared');
  }
}
