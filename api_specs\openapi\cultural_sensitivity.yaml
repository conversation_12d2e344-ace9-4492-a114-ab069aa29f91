openapi: 3.0.0
info:
  title: The AIgency & Cultural Sensitivity API
  version: 1.0.0
  description: API endpoints for managing cultural sensitivity, localization, and Arabic language processing.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Localization & Internationalization
    description: Managing language, locale, and regional settings
  - name: Cultural Context
    description: Managing and applying cultural context to AI interactions
  - name: Arabic Language Processing
    description: Specific endpoints for Arabic dialect detection and adaptation

paths:
  /localization/settings:
    get:
      summary: Get localization settings for a user or PAIM instance
      operationId: getLocalizationSettings
      tags:
        - Localization & Internationalization
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: ID of the user or PAIM instance to retrieve settings for.
        - in: query
          name: entityType
          schema:
            type: string
            enum: [user, paim_instance]
          description: Type of entity (user or paim_instance).
      responses:
        '200':
          description: Localization settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LocalizationSettings'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    put:
      summary: Update localization settings for a user or PAIM instance
      operationId: updateLocalizationSettings
      tags:
        - Localization & Internationalization
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLocalizationSettingsRequest'
      responses:
        '200':
          description: Localization settings updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LocalizationSettings'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /cultural-context:
    get:
      summary: Get cultural context for a given locale or entity
      operationId: getCulturalContext
      tags:
        - Cultural Context
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: locale
          schema:
            type: string
            example: ar-AE
          description: BCP 47 language tag (e.g., en-US, ar-SA).
        - in: query
          name: entityId
          schema:
            type: string
            format: uuid
          description: Optional ID of the user or PAIM instance to retrieve context for.
        - in: query
          name: entityType
          schema:
            type: string
            enum: [user, paim_instance]
          description: Optional type of entity (user or paim_instance).
      responses:
        '200':
          description: Cultural context details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CulturalContext'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Create or update cultural context for a locale or entity
      operationId: createOrUpdateCulturalContext
      tags:
        - Cultural Context
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CulturalContextRequest'
      responses:
        '200':
          description: Cultural context created or updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CulturalContext'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /arabic-processing/dialect-detection:
    post:
      summary: Detect Arabic dialect from text
      operationId: detectArabicDialect
      tags:
        - Arabic Language Processing
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - text
              properties:
                text:
                  type: string
                  description: The Arabic text to analyze for dialect detection.
                  example: "كيف حالك؟"
      responses:
        '200':
          description: Detected Arabic dialect
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DialectDetectionResult'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /arabic-processing/adapt-content:
    post:
      summary: Adapt content to a specific Arabic dialect
      operationId: adaptArabicContent
      tags:
        - Arabic Language Processing
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - text
                - targetDialect
              properties:
                text:
                  type: string
                  description: The original Arabic text to adapt.
                  example: "مرحباً بك في خدمتنا."
                targetDialect:
                  type: string
                  enum: [MSA, EGY, SAU, UAE, LEV, MAG] # Example dialects
                  description: The target Arabic dialect for adaptation.
                  example: EGY
      responses:
        '200':
          description: Adapted Arabic content
          content:
            application/json:
              schema:
                type: object
                properties:
                  adaptedText:
                    type: string
                    description: The text adapted to the target dialect.
                    example: "أهلاً بيك في خدمتنا."
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    LocalizationSettings:
      type: object
      properties:
        entityId:
          type: string
          format: uuid
          description: ID of the user or PAIM instance these settings apply to.
        entityType:
          type: string
          enum: [user, paim_instance]
        preferredLanguage:
          type: string
          example: ar-AE
          description: BCP 47 language tag (e.g., en-US, ar-SA).
        timezone:
          type: string
          example: Asia/Dubai
        dateFormat:
          type: string
          example: YYYY-MM-DD
        timeFormat:
          type: string
          example: HH:mm
        currency:
          type: string
          example: AED
        culturalPreferences:
          type: object
          description: Custom cultural preferences (e.g., formality level, humor).
          example:
            formality: formal
            humor: none

    UpdateLocalizationSettingsRequest:
      type: object
      properties:
        entityId:
          type: string
          format: uuid
          description: ID of the user or PAIM instance to update settings for.
        entityType:
          type: string
          enum: [user, paim_instance]
        preferredLanguage:
          type: string
          example: ar-SA
        timezone:
          type: string
          example: Asia/Riyadh
        dateFormat:
          type: string
          example: DD/MM/YYYY
        timeFormat:
          type: string
          example: hh:mm A
        currency:
          type: string
          example: SAR
        culturalPreferences:
          type: object
          description: Custom cultural preferences (e.g., formality level, humor).

    CulturalContext:
      type: object
      properties:
        locale:
          type: string
          example: ar-AE
          description: BCP 47 language tag (e.g., en-US, ar-SA).
        culturalNorms:
          type: array
          items:
            type: string
          description: Key cultural norms relevant to the locale.
          example: ["respect for elders", "indirect communication"]
        commonPhrases:
          type: object
          description: Common phrases and their culturally appropriate usage.
          example:
            greeting: "السلام عليكم"
            farewell: "مع السلامة"
        sensitiveTopics:
          type: array
          items:
            type: string
          description: Topics that require careful handling or avoidance.
          example: ["politics", "religion"]
        historicalContext:
          type: string
          description: Brief overview of relevant historical context.
        socialEtiquette:
          type: string
          description: Guidelines for social interactions.
        businessPractices:
          type: string
          description: Common business practices and expectations.
        lastUpdated:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    CulturalContextRequest:
      type: object
      required:
        - locale
      properties:
        locale:
          type: string
          example: ar-AE
          description: BCP 47 language tag (e.g., en-US, ar-SA).
        culturalNorms:
          type: array
          items:
            type: string
        commonPhrases:
          type: object
        sensitiveTopics:
          type: array
          items:
            type: string
        historicalContext:
          type: string
        socialEtiquette:
          type: string
        businessPractices:
          type: string

    DialectDetectionResult:
      type: object
      properties:
        text:
          type: string
          description: The original text analyzed.
          example: "كيف حالك؟"
        detectedDialect:
          type: string
          enum: [MSA, EGY, SAU, UAE, LEV, MAG, Other] # Example dialects
          description: The detected Arabic dialect. MSA for Modern Standard Arabic.
          example: EGY
        confidence:
          type: number
          format: float
          description: Confidence score of the detection (0.0 - 1.0).
          example: 0.95
        possibleDialects:
          type: array
          items:
            type: object
            properties:
              dialect:
                type: string
              confidence:
                type: number
          description: List of other possible dialects with their confidence scores.
          example:
            - dialect: MSA
              confidence: 0.03
            - dialect: LEV
              confidence: 0.02