"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsService = void 0;
const powerops_repository_1 = require("./powerops.repository");
const database_adapter_1 = require("../config/database-adapter"); // Import DatabaseAdapter
const db_1 = __importDefault(require("../database/db")); // Import the Knex instance
const powerops_types_1 = require("./powerops.types");
const errors_1 = require("../utils/errors");
class PowerOpsService {
    powerOpsRepository;
    constructor(powerOpsRepository) {
        // Pass the Knex instance to the repository
        // This assumes 'db' is accessible globally or passed via dependency injection
        // For now, we'll import it directly for simplicity in this migration phase.
        // In a production app, consider a more robust DI solution.
        this.powerOpsRepository = powerOpsRepository || new powerops_repository_1.PowerOpsRepository(db_1.default);
    }
    async scaleServiceResources(serviceName, direction) {
        console.log(`Simulating scaling ${direction} for service: ${serviceName}`);
        // In a real scenario, this would interact with infrastructure APIs
        // For example, updating replica counts in Kubernetes, or adjusting VM sizes in a cloud
        // You might also log this action to the audit system here.
    }
    // PowerOps Usage & Cost Management
    async logUsage(data) {
        const usageEntry = await this.powerOpsRepository.logPowerOpsUsage(data);
        // In a real system, this would trigger budget checks and notifications
        return {
            entityId: usageEntry.entityId,
            entityType: usageEntry.entityType,
            totalUsageUnits: usageEntry.usageUnits,
            estimatedCost: usageEntry.estimatedCost,
        };
    }
    async getUsage(entityId, entityType, startDate, endDate) {
        const usageEntries = await this.powerOpsRepository.getPowerOpsUsage(entityId, entityType, startDate, endDate);
        // Aggregate usage for PowerOpsUsage type
        const aggregatedUsage = {};
        usageEntries.forEach(entry => {
            const key = `${entry.entityId}-${entry.entityType}`;
            if (!aggregatedUsage[key]) {
                aggregatedUsage[key] = {
                    entityId: entry.entityId,
                    entityType: entry.entityType,
                    totalUsageUnits: 0,
                    estimatedCost: 0,
                };
            }
            aggregatedUsage[key].totalUsageUnits += entry.usageUnits;
            aggregatedUsage[key].estimatedCost += entry.estimatedCost;
        });
        return Object.values(aggregatedUsage);
    }
    async getBudgets(entityId, entityType) {
        return this.powerOpsRepository.getBudgets(entityId, entityType);
    }
    async createBudget(data) {
        return this.powerOpsRepository.createBudget(data);
    }
    async updateBudget(budgetId, data) {
        const updatedBudget = await this.powerOpsRepository.updateBudget(budgetId, data);
        if (!updatedBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return updatedBudget;
    }
    async deleteBudget(budgetId) {
        const deleted = await this.powerOpsRepository.deleteBudget(budgetId);
        if (!deleted) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return deleted;
    }
    async getCostOptimizationRecommendations(entityId, entityType) {
        // In a real system, this would involve analyzing usage patterns and suggesting optimizations.
        // For now, it's a direct call to the repository.
        return this.powerOpsRepository.getCostOptimizationRecommendations(entityId, entityType);
    }
    async getResourceUsageLimits(entityId, entityType) {
        return this.powerOpsRepository.getResourceUsageLimits(entityId, entityType);
    }
    async setResourceUsageLimit(data) {
        return this.powerOpsRepository.setResourceUsageLimit(data);
    }
    // Gamification (XP, Badges, Achievements, Streaks)
    async getXp(id, entityType) {
        const xp = await this.powerOpsRepository.getXp(id, entityType);
        if (!xp) {
            // Return a default XP object if not found
            return { entityId: id, entityType, currentXp: 0, level: 1 };
        }
        return xp;
    }
    async awardXp(data) {
        const XP_MULTIPLIER = 2;
        const xpGained = data.powerops * XP_MULTIPLIER;
        // Use DatabaseAdapter to update XP and log the event
        // Assuming entityId in AwardXpRequest maps to userId in updateUserXP
        // And org_id maps to tenantId
        const { newXP, newTotalXP } = await database_adapter_1.DatabaseAdapter.updateUserXP(data.entityId, // Assuming entityId is the user_id
        data.org_id, // Assuming org_id is the tenant_id
        xpGained, data.reason, // Using reason as eventType
        data.metadata ? JSON.stringify(data.metadata) : undefined // metadata as description
        );
        // Return an Xp object based on the updated values
        // Note: The level calculation might need to be consistent with how it's done in DatabaseAdapter
        return {
            entityId: data.entityId,
            entityType: data.entityType, // Assuming entityType is part of AwardXpRequest
            currentXp: newXP,
            level: Math.floor(newTotalXP / 1000) + 1, // Simple level calculation
        };
    }
    async getAllBadges() {
        return this.powerOpsRepository.getAllBadges();
    }
    async getBadges(entityId, entityType) {
        return this.powerOpsRepository.getBadgesForEntity(entityId, entityType);
    }
    async awardBadge(data) {
        // Logic to check if badge can be awarded (e.g., prerequisites)
        // For now, directly award
        return this.powerOpsRepository.awardBadge(data);
    }
    async evaluateBadgeEligibility() {
        console.log('Evaluating badge eligibility for all users/organizations...');
        // This method would be called by a nightly job.
        // It would iterate through users/organizations, check their progress against badge unlock_rules,
        // and award badges if criteria are met.
        // This is a placeholder for complex logic.
        // Example:
        // const allUsers = await this.userRepository.getAllUsers();
        // for (const user of allUsers) {
        //   const userXp = await this.getXp(user.id, EntityType.User);
        //   const availableBadges = await this.getAllBadges();
        //   for (const badge of availableBadges) {
        //     if (this.checkBadgeUnlockRule(user, userXp, badge.unlock_rule)) {
        //       await this.awardBadge({ entityId: user.id, entityType: EntityType.User, badgeId: badge.id });
        //     }
        //   }
        // }
    }
    checkBadgeUnlockRule(user, xp, rule) {
        // Placeholder for complex rule evaluation logic
        // e.g., rule.minXp, rule.activityCount, etc.
        return true; // Always true for now
    }
    async getAchievements(entityId, entityType) {
        return this.powerOpsRepository.getAchievements(entityId, entityType);
    }
    async grantAchievement(data) {
        // Logic to check if achievement can be granted
        return this.powerOpsRepository.grantAchievement(data);
    }
    async getStreaks(entityId, entityType) {
        return this.powerOpsRepository.getStreaks(entityId, entityType);
    }
    async getLeaderboard(metric, limit) {
        // This would involve more complex logic to calculate and sort leaderboard entries
        return this.powerOpsRepository.getLeaderboard(metric, limit);
    }
    // Billing and Payments
    async getInvoices(entityId, entityType) {
        return this.powerOpsRepository.getInvoices(entityId, entityType);
    }
    async createInvoice(data) {
        // Logic for complex invoice generation, e.g., pulling usage data for line items
        return this.powerOpsRepository.createInvoice(data);
    }
    async processPayment(data) {
        // Integration with external payment systems would happen here
        // For now, it's a direct call to the repository
        return this.powerOpsRepository.processPayment(data);
    }
    // Notifications
    async getNotifications(entityId, entityType) {
        return this.powerOpsRepository.getNotifications(entityId, entityType);
    }
    async createNotification(data) {
        return this.powerOpsRepository.createNotification(data);
    }
    // Helper for calculating usage cost (can be moved to a utility or pricing service)
    calculateUsageCost(usageUnits, costCategory) {
        // This is a placeholder. In a real system, this would come from a configuration or pricing service.
        switch (costCategory) {
            case powerops_types_1.CostCategory.Compute: return usageUnits * 0.01;
            case powerops_types_1.CostCategory.Storage: return usageUnits * 0.001;
            case powerops_types_1.CostCategory.Bandwidth: return usageUnits * 0.005;
            case powerops_types_1.CostCategory.AIModel: return usageUnits * 0.05;
            default: return usageUnits * 0.01;
        }
    }
}
exports.PowerOpsService = PowerOpsService;
