import { Knex } from 'knex'; // Import Knex type
import {
  PowerOpsUsageEntry,
  LogPowerOpsUsageRequest,
  Xp,
  AwardXpRequest,
  XpEvent, // Import XpEvent
  Badge,
  AwardBadgeRequest,
  Achievement,
  GrantAchievementRequest,
  Streak,
  Budget,
  CreateBudgetRequest,
  UpdateBudgetRequest,
  Invoice,
  CreateInvoiceRequest,
  Payment,
  ProcessPaymentRequest,
  LeaderboardEntry,
  CostOptimizationRecommendation,
  ResourceUsageLimit,
  SetResourceUsageLimitRequest,
  Notification,
  CreateNotificationRequest,
  EntityType,
  CostCategory,
} from './powerops.types';

export class PowerOpsRepository {
  private db: Knex; // Use Knex type for db instance

  private readonly TABLE_POWER_OPS_USAGE = 'power_ops_usage';
  private readonly TABLE_XP = 'xp';
  private readonly TABLE_XP_EVENTS = 'xp_events'; // New table for XP events
  private readonly TABLE_BADGES = 'badges';
  private readonly TABLE_USER_BADGES = 'user_badges'; // New table for user badges
  private readonly TABLE_ACHIEVEMENTS = 'achievements';
  private readonly TABLE_STREAKS = 'streaks';
  private readonly TABLE_BUDGETS = 'budgets';
  private readonly TABLE_INVOICES = 'invoices';
  private readonly TABLE_PAYMENTS = 'payments';
  private readonly TABLE_LEADERBOARD = 'leaderboard';
  private readonly TABLE_COST_OPTIMIZATION_RECOMMENDATIONS = 'cost_optimization_recommendations';
  private readonly TABLE_RESOURCE_USAGE_LIMITS = 'resource_usage_limits';
  private readonly TABLE_NOTIFICATIONS = 'notifications';

  constructor(db: Knex) { // Accept db as constructor argument
    this.db = db;
  }

  // PowerOps Usage
  async logPowerOpsUsage(data: LogPowerOpsUsageRequest): Promise<PowerOpsUsageEntry> {
    const [id] = await this.db(this.TABLE_POWER_OPS_USAGE).insert({
      entity_id: data.entityId,
      entity_type: data.entityType,
      usage_units: data.usageUnits,
      cost_category: data.costCategory,
      estimated_cost: data.usageUnits * this.getCostPerUnit(data.costCategory), // Placeholder for cost calculation
      description: data.description,
      timestamp: data.timestamp,
    }).returning('id');
    const entry = await this.getPowerOpsUsageEntryById(id as string);
    if (!entry) {
      throw new Error('Failed to retrieve PowerOps usage entry after creation.');
    }
    return entry;
  }

  async getPowerOpsUsage(entityId: string, entityType: EntityType, startDate?: string, endDate?: string): Promise<PowerOpsUsageEntry[]> {
    let query = this.db(this.TABLE_POWER_OPS_USAGE)
      .where({ entity_id: entityId, entity_type: entityType });

    if (startDate) {
      query = query.andWhere('timestamp', '>=', startDate);
    }
    if (endDate) {
      query = query.andWhere('timestamp', '<=', endDate);
    }

    return query.select('*');
  }

  async getPowerOpsUsageEntryById(id: string): Promise<PowerOpsUsageEntry | undefined> {
    return this.db(this.TABLE_POWER_OPS_USAGE).where({ id }).first();
  }

  // XP System
  async getXp(entityId: string, entityType: EntityType): Promise<Xp | undefined> {
    const xpDb = await this.db(this.TABLE_XP)
      .where({ entity_id: entityId, entity_type: entityType })
      .select('entity_id', 'entity_type', 'current_xp', 'last_awarded_at')
      .first();

    if (!xpDb) {
      return undefined;
    }
    return this.mapDbToXp(xpDb);
  }

  async awardXp(data: AwardXpRequest): Promise<Xp> {
    const { entityId, entityType, powerops, reason, metadata } = data;
    const currentTimestamp = new Date().toISOString();

    // Log the XP event
    await this.db(this.TABLE_XP_EVENTS).insert({
      user_id: entityId, // Assuming entityId is user_id for xp_events
      amount: powerops,
      reason: reason,
      metadata: metadata ? JSON.stringify(metadata) : null,
      created_at: currentTimestamp,
    });

    // Update or insert XP for the entity
    let xpEntry = await this.getXp(entityId, entityType);
    let newXpAmount = powerops;

    if (xpEntry) {
      newXpAmount = xpEntry.currentXp + powerops;
      await this.db(this.TABLE_XP)
        .where({ entity_id: entityId, entity_type: entityType })
        .update({
          current_xp: newXpAmount,
          level: this.calculateLevel(newXpAmount),
          last_awarded_at: currentTimestamp,
        });
    } else {
      await this.db(this.TABLE_XP).insert({
        entity_id: entityId,
        entity_type: entityType,
        current_xp: newXpAmount,
        level: this.calculateLevel(newXpAmount),
        last_awarded_at: currentTimestamp,
        created_at: currentTimestamp,
        updated_at: currentTimestamp,
      });
    }

    // Fetch and return the updated XP entry
    const updatedXp = await this.getXp(entityId, entityType);
    if (!updatedXp) {
      throw new Error('Failed to retrieve XP after awarding.');
    }
    return updatedXp;
  }

  async logXpEvent(xpEvent: XpEvent): Promise<void> {
    await this.db(this.TABLE_XP_EVENTS).insert({
      user_id: xpEvent.org_id,
      amount: xpEvent.xp_gained,
      reason: xpEvent.reason,
      metadata: xpEvent.metadata ? JSON.stringify(xpEvent.metadata) : null,
      created_at: xpEvent.timestamp || new Date(),
    });
  }

  // Badges
  async getAllBadges(): Promise<Badge[]> {
    return this.db(this.TABLE_BADGES).select('*');
  }

  async getBadgesForEntity(entityId: string, entityType: EntityType): Promise<Badge[]> {
    // Assuming entityId is user_id for badges
    return this.db(this.TABLE_USER_BADGES)
      .join(this.TABLE_BADGES, `${this.TABLE_USER_BADGES}.badge_id`, '=', `${this.TABLE_BADGES}.badge_id`)
      .where(`${this.TABLE_USER_BADGES}.user_id`, entityId)
      .select(`${this.TABLE_BADGES}.*`, `${this.TABLE_USER_BADGES}.earned_at`);
  }

  async awardBadge(data: AwardBadgeRequest): Promise<Badge> {
    const [id] = await this.db(this.TABLE_USER_BADGES).insert({ // Insert into user_badges
      user_id: data.entityId, // Assuming entityId is user_id
      badge_id: data.badgeId,
      earned_at: new Date().toISOString(),
    }).returning('user_badge_id'); // Return the ID of the user_badge entry
    
    // Fetch the actual badge details
    const badge = await this.db(this.TABLE_BADGES).where({ badge_id: data.badgeId }).first();
    if (!badge) {
      throw new Error('Failed to retrieve badge after awarding.');
    }
    return badge as Badge;
  }

  // Achievements
  async getAchievements(entityId: string, entityType: EntityType): Promise<Achievement[]> {
    return this.db(this.TABLE_ACHIEVEMENTS).where({ entity_id: entityId, entity_type: entityType }).select('*');
  }

  async grantAchievement(data: GrantAchievementRequest): Promise<Achievement> {
    const [id] = await this.db(this.TABLE_ACHIEVEMENTS).insert({
      entity_id: data.entityId,
      entity_type: data.entityType,
      achievement_id: data.achievementId,
      unlocked_at: new Date().toISOString(),
    }).returning('id');
    const achievement = await this.db(this.TABLE_ACHIEVEMENTS).where({ id: id as string }).first();
    if (!achievement) {
      throw new Error('Failed to retrieve achievement after creation.');
    }
    return achievement as Achievement;
  }

  // Streaks
  async getStreaks(entityId: string, entityType: EntityType): Promise<Streak[]> {
    return this.db(this.TABLE_STREAKS).where({ entity_id: entityId, entity_type: entityType }).select('*');
  }

  // Budgets
  async getBudgets(entityId: string, entityType: EntityType): Promise<Budget[]> {
    return this.db(this.TABLE_BUDGETS).where({ entity_id: entityId, entity_type: entityType }).select('*');
  }

  async getBudgetById(budgetId: string): Promise<Budget | undefined> {
    return this.db(this.TABLE_BUDGETS).where({ id: budgetId }).first();
  }

  async createBudget(data: CreateBudgetRequest): Promise<Budget> {
    const [id] = await this.db(this.TABLE_BUDGETS).insert({
      entity_id: data.entityId,
      entity_type: data.entityType,
      monthly_limit: data.monthlyLimit,
      currency: data.currency,
      alert_threshold: data.alertThreshold,
      current_spend: 0, // New budgets start with 0 spend
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }).returning('id');
    const budget = await this.db(this.TABLE_BUDGETS).where({ id: id as string }).first();
    if (!budget) {
      throw new Error('Failed to retrieve budget after creation.');
    }
    return budget as Budget;
  }

  async updateBudget(budgetId: string, data: UpdateBudgetRequest): Promise<Budget | undefined> {
    await this.db(this.TABLE_BUDGETS)
      .where({ id: budgetId })
      .update({
        monthly_limit: data.monthlyLimit,
        alert_threshold: data.alertThreshold,
        updated_at: new Date().toISOString(),
      });
    return this.db(this.TABLE_BUDGETS).where({ id: budgetId }).first();
  }

  async deleteBudget(budgetId: string): Promise<boolean> {
    const deletedCount = await this.db(this.TABLE_BUDGETS).where({ id: budgetId }).del();
    return deletedCount > 0;
  }

  // Invoices
  async getInvoices(entityId: string, entityType: EntityType): Promise<Invoice[]> {
    return this.db(this.TABLE_INVOICES).where({ entity_id: entityId, entity_type: entityType }).select('*');
  }

  async createInvoice(data: CreateInvoiceRequest): Promise<Invoice> {
    const [id] = await this.db(this.TABLE_INVOICES).insert({
      entity_id: data.entityId,
      entity_type: data.entityType,
      billing_period_start: data.billingPeriodStart,
      billing_period_end: data.billingPeriodEnd,
      total_amount: data.lineItems.reduce((sum, item) => sum + item.total, 0),
      currency: 'USD', // Assuming USD for now, can be dynamic
      status: 'pending',
      issue_date: new Date().toISOString(),
      due_date: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString(), // 30 days from now
      payment_method_id: data.paymentMethodId,
      line_items: JSON.stringify(data.lineItems), // Store line items as JSON
    }).returning('id');
    const invoice = await this.db(this.TABLE_INVOICES).where({ id: id as string }).first();
    if (!invoice) {
      throw new Error('Failed to retrieve invoice after creation.');
    }
    return invoice as Invoice;
  }

  // Payments
  async processPayment(data: ProcessPaymentRequest): Promise<Payment> {
    const [id] = await this.db(this.TABLE_PAYMENTS).insert({
      invoice_id: data.invoiceId,
      amount: data.amount,
      currency: data.currency,
      payment_method: data.paymentMethod,
      transaction_id: 'mock_transaction_id_' + Date.now(), // Mock transaction ID
      status: 'success', // Assume success for now
      timestamp: new Date().toISOString(),
    }).returning('id');

    // Update invoice status to paid
    await this.db(this.TABLE_INVOICES)
      .where({ id: data.invoiceId })
      .update({ status: 'paid' });

    const payment = await this.db(this.TABLE_PAYMENTS).where({ id: id as string }).first();
    if (!payment) {
      throw new Error('Failed to retrieve payment after creation.');
    }
    return payment as Payment;
  }

  // Leaderboard
  async getLeaderboard(metric: string, limit: number = 10): Promise<LeaderboardEntry[]> {
    // This is a simplified example. A real leaderboard would involve more complex aggregation.
    return this.db(this.TABLE_LEADERBOARD)
      .orderBy('score', 'desc')
      .limit(limit)
      .select('*');
  }

  // Cost Optimization Recommendations
  async getCostOptimizationRecommendations(entityId: string, entityType: EntityType): Promise<CostOptimizationRecommendation[]> {
    return this.db(this.TABLE_COST_OPTIMIZATION_RECOMMENDATIONS).where({ entity_id: entityId, entity_type: entityType }).select('*');
  }

  // Resource Usage Limits
  async getResourceUsageLimits(entityId: string, entityType: EntityType): Promise<ResourceUsageLimit[]> {
    return this.db(this.TABLE_RESOURCE_USAGE_LIMITS).where({ entity_id: entityId, entity_type: entityType }).select('*');
  }

  async setResourceUsageLimit(data: SetResourceUsageLimitRequest): Promise<ResourceUsageLimit> {
    const [id] = await this.db(this.TABLE_RESOURCE_USAGE_LIMITS).insert({
      entity_id: data.entityId,
      entity_type: data.entityType,
      resource_type: data.resourceType,
      limit: data.limit,
      unit: data.unit,
      current_usage: 0, // New limits start with 0 usage
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }).returning('id');
    const resourceUsageLimit = await this.db(this.TABLE_RESOURCE_USAGE_LIMITS).where({ id: id as string }).first();
    if (!resourceUsageLimit) {
      throw new Error('Failed to retrieve resource usage limit after creation.');
    }
    return resourceUsageLimit as ResourceUsageLimit;
  }

  // Notifications
  async getNotifications(entityId: string, entityType: EntityType): Promise<Notification[]> {
    return this.db(this.TABLE_NOTIFICATIONS).where({ entity_id: entityId, entity_type: entityType }).select('*');
  }

  async createNotification(data: CreateNotificationRequest): Promise<Notification> {
    const [id] = await this.db(this.TABLE_NOTIFICATIONS).insert({
      entity_id: data.entityId,
      entity_type: data.entityType,
      type: data.type,
      message: data.message,
      read: false,
      timestamp: new Date().toISOString(),
    }).returning('id');
    const notification = await this.db(this.TABLE_NOTIFICATIONS).where({ id: id as string }).first();
    if (!notification) {
      throw new Error('Failed to retrieve notification after creation.');
    }
    return notification as Notification;
  }

  // Helper functions
  private getCostPerUnit(category: CostCategory): number {
    // This is a placeholder. In a real system, this would come from a configuration or pricing service.
    switch (category) {
      case CostCategory.Compute: return 0.01;
      case CostCategory.Storage: return 0.001;
      case CostCategory.Bandwidth: return 0.005;
      case CostCategory.AIModel: return 0.05;
      default: return 0.01;
    }
  }

  private calculateLevel(xp: number): number {
    // Simple linear progression for now. Can be replaced with a more complex formula.
    return Math.floor(xp / 1000) + 1;
  }

  // Helper to map database row to Xp interface
  private mapDbToXp(dbRow: any): Xp {
    return {
      entityId: dbRow.entity_id,
      entityType: dbRow.entity_type,
      currentXp: dbRow.current_xp,
      level: dbRow.level,
      lastAwardedAt: dbRow.last_awarded_at,
    };
  }

  // Helper to map database row to Badge interface
  private mapDbToBadge(dbRow: any): Badge {
    return {
      id: dbRow.badge_id,
      name: dbRow.name,
      description: dbRow.description,
      imageUrl: dbRow.image_url,
      awardedAt: dbRow.earned_at, // Assuming 'earned_at' from user_badges table for awardedAt
    };
  }
}