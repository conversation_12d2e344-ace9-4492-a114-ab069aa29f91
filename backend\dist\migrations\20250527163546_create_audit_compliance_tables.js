"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.raw(`
    -- Audit Logging and Compliance Tables

    -- Table: ActivityLogs
    -- Records all significant user and system activities for auditing purposes.
    CREATE TABLE ActivityLogs (
        log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        user_id UUID REFERENCES Users(user_id), -- Null if system activity
        activity_type VARCHAR(100) NOT NULL, -- e.g., 'login', 'create_workflow', 'update_agent'
        activity_details JSONB, -- Detailed JSON of the activity, including old/new values
        cultural_context JSONB, -- Cultural context at the time of the activity
        ip_address INET,
        user_agent TEXT,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: SecurityEvents
    -- Tracks security-related events, such as failed logins, unauthorized access attempts, etc.
    CREATE TABLE SecurityEvents (
        event_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        user_id UUID REFERENCES Users(user_id), -- Null if system-level event
        event_type VARCHAR(100) NOT NULL, -- e.g., 'failed_login', 'unauthorized_access', 'data_breach_attempt'
        event_details JSONB, -- Detailed JSON of the security event
        severity VARCHAR(50) NOT NULL, -- e.g., 'low', 'medium', 'high', 'critical'
        ip_address INET,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Table: ComplianceRecords
    -- Stores records and evidence related to various compliance regulations (e.g., GDPR, HIPAA).
    CREATE TABLE ComplianceRecords (
        record_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        compliance_standard VARCHAR(100) NOT NULL, -- e.g., 'GDPR', 'HIPAA', 'SOC2'
        record_type VARCHAR(100) NOT NULL, -- e.g., 'data_processing_agreement', 'consent_form', 'data_retention_policy'
        record_details JSONB, -- JSON of the compliance record details and evidence
        document_url TEXT, -- URL to a stored document if applicable
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        effective_date DATE,
        review_date DATE
    );

    -- Table: DataAccessLogs
    -- Logs every access to sensitive data for privacy compliance and auditing.
    CREATE TABLE DataAccessLogs (
        access_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL REFERENCES Tenants(tenant_id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES Users(user_id) ON DELETE CASCADE,
        data_entity_type VARCHAR(100) NOT NULL, -- e.g., 'User', 'WorkflowExecution', 'AgentDefinition'
        data_entity_id UUID NOT NULL, -- ID of the specific data entity accessed
        access_type VARCHAR(50) NOT NULL, -- e.g., 'read', 'write', 'delete'
        accessed_fields JSONB, -- JSON array of specific fields accessed (if applicable)
        ip_address INET,
        user_agent TEXT,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Indexes for performance optimization
    CREATE INDEX idx_activitylogs_tenant_id ON ActivityLogs(tenant_id);
    CREATE INDEX idx_activitylogs_user_id ON ActivityLogs(user_id);
    CREATE INDEX idx_activitylogs_type ON ActivityLogs(activity_type);
    CREATE INDEX idx_activitylogs_timestamp ON ActivityLogs(timestamp);
    CREATE INDEX idx_securityevents_tenant_id ON SecurityEvents(tenant_id);
    CREATE INDEX idx_securityevents_type ON SecurityEvents(event_type);
    CREATE INDEX idx_securityevents_timestamp ON SecurityEvents(timestamp);
    CREATE INDEX idx_compliancerecords_tenant_id ON ComplianceRecords(tenant_id);
    CREATE INDEX idx_compliancerecords_standard ON ComplianceRecords(compliance_standard);
    CREATE INDEX idx_dataaccesslogs_tenant_id ON DataAccessLogs(tenant_id);
    CREATE INDEX idx_dataaccesslogs_user_id ON DataAccessLogs(user_id);
    CREATE INDEX idx_dataaccesslogs_entity_type ON DataAccessLogs(data_entity_type);
    CREATE INDEX idx_dataaccesslogs_timestamp ON DataAccessLogs(timestamp);
  `);
}
async function down(knex) {
    await knex.schema.raw(`
    DROP TABLE IF EXISTS DataAccessLogs;
    DROP TABLE IF EXISTS ComplianceRecords;
    DROP TABLE IF EXISTS SecurityEvents;
    DROP TABLE IF EXISTS ActivityLogs;
  `);
}
