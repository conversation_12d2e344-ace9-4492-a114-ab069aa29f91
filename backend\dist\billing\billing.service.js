"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingService = void 0;
// STRIPE DISABLED: import Stripe from 'stripe';
const audit_service_1 = require("../audit/audit.service"); // Import AuditTrailService
const audit_types_1 = require("../audit/audit.types"); // Import audit types
const logger_1 = __importDefault(require("../config/logger")); // Import the logger
class BillingService {
    billingRepository;
    // STRIPE DISABLED: private stripe: Stripe;
    auditTrailService; // Declare audit service
    POWEROP_COST_USD = 0.08; // 1 PowerOp = $0.08
    constructor(billingRepository) {
        this.billingRepository = billingRepository;
        // STRIPE DISABLED: Stripe integration temporarily disabled
        // this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
        //   apiVersion: '2024-04-10', // Use a recent API version
        // });
        this.auditTrailService = new audit_service_1.AuditTrailService(); // Initialize audit service
    }
    async trackPowerOpsUsage(org_id, powerops_used) {
        logger_1.default.info(`Attempting to track PowerOps usage for organization ${org_id} with ${powerops_used} PowerOps.`);
        const cost_usd = powerops_used * this.POWEROP_COST_USD;
        const data = {
            org_id,
            powerops_used,
            cost_usd,
            month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01 for the current month
        };
        const record = await this.billingRepository.insertPowerOpsUsage(data);
        logger_1.default.info(`Successfully tracked PowerOps usage for organization ${org_id}. Record ID: ${record.id}`);
        await this.auditTrailService.logEvent({
            tenantId: org_id,
            category: audit_types_1.AuditEventCategory.BILLING,
            operationType: 'POWER_OPS_USAGE_TRACKED',
            description: `PowerOps usage tracked for organization ${org_id}: ${powerops_used} PowerOps, Cost: $${cost_usd.toFixed(2)}`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { powerops_used, cost_usd },
        });
        // STRIPE DISABLED: Invoice line item creation temporarily disabled
        // Assuming org_id can be mapped to a Stripe Customer ID
        // For simplicity, let's assume org_id is the Stripe Customer ID for now.
        // In a real application, you'd have a mapping table or retrieve it from your DB.
        // await this.createInvoiceLineItem(
        //   org_id, // This should be the Stripe Customer ID
        //   Math.round(cost_usd * 100), // Amount in cents
        //   `PowerOps Usage for ${new Date().toLocaleString('en-US', { month: 'long', year: 'numeric' })}`
        // );
        logger_1.default.info(`STRIPE DISABLED: Would have created invoice line item for ${org_id} with cost $${cost_usd.toFixed(2)}`);
        return record;
    }
    // STRIPE DISABLED: createInvoiceLineItem method temporarily disabled
    async createInvoiceLineItem(customer_id, amount, // in cents
    description, currency = 'usd') {
        // STRIPE DISABLED: All Stripe API calls commented out
        logger_1.default.info(`STRIPE DISABLED: Would have created invoice item for customer ${customer_id}: Amount ${amount / 100} ${currency}, Description: ${description}`);
        await this.auditTrailService.logEvent({
            tenantId: customer_id,
            category: audit_types_1.AuditEventCategory.BILLING,
            operationType: 'STRIPE_DISABLED_INVOICE_ITEM_SKIPPED',
            description: `Stripe disabled: Would have created invoice item for customer ${customer_id}: Amount ${amount / 100} ${currency}, Description: ${description}`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { customer_id, amount, currency, description, stripe_disabled: true },
        });
        // Return a mock response to maintain compatibility
        return {
            id: `mock_ii_${Date.now()}`,
            object: 'invoiceitem',
            amount: amount,
            currency: currency,
            customer: customer_id,
            description: description,
            stripe_disabled: true
        };
        /* STRIPE DISABLED: Original implementation commented out
        try {
          const invoiceItem = await this.stripe.invoiceItems.create({
            customer: customer_id,
            amount: amount,
            currency: currency,
            description: description,
          });
          logger.info(`Stripe Invoice Item created: ${invoiceItem.id}`);
    
          await this.auditTrailService.logEvent({
            tenantId: customer_id,
            category: AuditEventCategory.BILLING,
            operationType: 'STRIPE_INVOICE_ITEM_CREATED',
            description: `Stripe invoice item created for customer ${customer_id}: Amount ${amount / 100} ${currency}, Description: ${description}`,
            severity: AuditEventSeverity.INFO,
            timestamp: new Date(),
            resourceId: invoiceItem.id,
            metadata: { customer_id, amount, currency, description },
          });
    
          return invoiceItem;
        } catch (error: any) {
          logger.error(`Error creating Stripe Invoice Item for customer ${customer_id}: ${error.message}`, { error });
    
          await this.auditTrailService.logEvent({
            tenantId: customer_id,
            category: AuditEventCategory.BILLING,
            operationType: 'STRIPE_INVOICE_ITEM_FAILED',
            description: `Failed to create Stripe invoice item for customer ${customer_id}: ${error.message || 'Unknown error'}`,
            severity: AuditEventSeverity.HIGH,
            timestamp: new Date(),
            metadata: { customer_id, amount, currency, description, error: error.message },
          });
    
          // Depending on the error, you might want to retry, log to a different system, or alert.
          // For now, re-throw to indicate failure.
          throw new Error('Failed to create Stripe Invoice Item');
        }
        */
    }
}
exports.BillingService = BillingService;
