import { PowerOpsRepository } from './powerops.repository';
import { AuthorizationService } from '../auth/authorization.service';
import { NotificationService } from '../notifications/notification.service';
import { JwtPayload } from '../auth/auth.types';
import { EntityType, PERMISSIONS } from '../auth/authorization.types';
import { CustomError } from '../utils/errors';
import { db } from '../database/database';
import {
  PowerOpsUsage,
  LogPowerOpsUsageRequest,
  Budget,
  CreateBudgetRequest,
  UpdateBudgetRequest,
  CostOptimizationRecommendation,
  ResourceUsageLimit,
  SetResourceUsageLimitRequest,
  Xp,
  AwardXpRequest,
  Badge,
  AwardBadgeRequest,
  Achievement,
  GrantAchievementRequest,
  Streak,
  LeaderboardEntry,
  Invoice,
  CreateInvoiceRequest,
  Payment,
  ProcessPaymentRequest,
  Notification,
  CreateNotificationRequest,
  CostCategory,
  PowerOpsAchievementPayload
} from './powerops.types';

export class PowerOpsService {
  private powerOpsRepository: PowerOpsRepository;
  private authorizationService: AuthorizationService;
  private notificationService: NotificationService;

  constructor(
    powerOpsRepository?: PowerOpsRepository,
    authorizationService?: AuthorizationService,
    notificationService?: NotificationService
  ) {
    this.powerOpsRepository = powerOpsRepository || new PowerOpsRepository(db);
    this.authorizationService = authorizationService || new AuthorizationService();
    this.notificationService = notificationService || new NotificationService(
      {} as any, {} as any
    );
  }

  async scaleServiceResources(serviceName: string, direction: 'up' | 'down'): Promise<void> {
    console.log(`Simulating scaling ${direction} for service: ${serviceName}`);
    // In a real scenario, this would interact with infrastructure APIs
  }

  // PowerOps Usage & Cost Management
  async logUsage(user: JwtPayload, data: LogPowerOpsUsageRequest): Promise<PowerOpsUsage> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_CREATE);
    const usageEntry = await this.powerOpsRepository.logPowerOpsUsage(data);
    return {
      entityId: usageEntry.entityId,
      entityType: usageEntry.entityType,
      totalUsageUnits: usageEntry.usageUnits,
      estimatedCost: usageEntry.estimatedCost,
    };
  }

  async getUsage(user: JwtPayload, entityId: string, entityType: EntityType, startDate?: string, endDate?: string): Promise<PowerOpsUsage[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);

    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read');
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }

    const usageEntries = await this.powerOpsRepository.getPowerOpsUsage(entityId, entityType, startDate, endDate);
    const aggregatedUsage: { [key: string]: PowerOpsUsage } = {};

    usageEntries.forEach(entry => {
      const key = `${entry.entityId}-${entry.entityType}`;
      if (!aggregatedUsage[key]) {
        aggregatedUsage[key] = {
          entityId: entry.entityId,
          entityType: entry.entityType,
          totalUsageUnits: 0,
          estimatedCost: 0,
        };
      }
      aggregatedUsage[key].totalUsageUnits += entry.usageUnits;
      aggregatedUsage[key].estimatedCost += entry.estimatedCost;
    });

    return Object.values(aggregatedUsage);
  }

  async getBudgets(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Budget[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);

    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read');
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW_ALL);
    }

    return this.powerOpsRepository.getBudgets(entityId, entityType);
  }

  async createBudget(user: JwtPayload, data: CreateBudgetRequest): Promise<Budget> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    return this.powerOpsRepository.createBudget(data);
  }

  async updateBudget(user: JwtPayload, budgetId: string, data: UpdateBudgetRequest): Promise<Budget> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
    if (!existingBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    this.authorizationService.canAccessResource(user, {
      ownerId: existingBudget.entityType === EntityType.User ? existingBudget.entityId : undefined,
      organizationId: existingBudget.entityType === EntityType.Organization ? existingBudget.entityId : user.organizationId,
      teamId: existingBudget.entityType === EntityType.Team ? existingBudget.entityId : user.teamId,
    }, 'update');

    const updatedBudget = await this.powerOpsRepository.updateBudget(budgetId, data);
    if (!updatedBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    return updatedBudget;
  }

  async deleteBudget(user: JwtPayload, budgetId: string): Promise<boolean> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
    if (!existingBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    this.authorizationService.canAccessResource(user, {
      ownerId: existingBudget.entityType === EntityType.User ? existingBudget.entityId : undefined,
      organizationId: existingBudget.entityType === EntityType.Organization ? existingBudget.entityId : user.organizationId,
      teamId: existingBudget.entityType === EntityType.Team ? existingBudget.entityId : user.teamId,
    }, 'delete');

    const deleted = await this.powerOpsRepository.deleteBudget(budgetId);
    if (!deleted) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    return deleted;
  }

  async getCostOptimizationRecommendations(user: JwtPayload, entityId: string, entityType: EntityType): Promise<CostOptimizationRecommendation[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    return this.powerOpsRepository.getCostOptimizationRecommendations(entityId, entityType);
  }

  async getResourceUsageLimits(user: JwtPayload, entityId: string, entityType: EntityType): Promise<ResourceUsageLimit[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    return this.powerOpsRepository.getResourceUsageLimits(entityId, entityType);
  }

  async setResourceUsageLimit(user: JwtPayload, data: SetResourceUsageLimitRequest): Promise<ResourceUsageLimit> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    return this.powerOpsRepository.setResourceUsageLimit(data);
  }

  // Gamification (XP, Badges, Achievements, Streaks)
  async getXp(user: JwtPayload, id: string, entityType: EntityType): Promise<Xp> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    const xp = await this.powerOpsRepository.getXp(id, entityType);
    if (!xp) {
      return { entityId: id, entityType, currentXp: 0, level: 1 };
    }
    return xp;
  }

  async awardXp(user: JwtPayload, data: AwardXpRequest): Promise<Xp> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    const updatedXp = await this.powerOpsRepository.awardXp(data);

    // Check for level-up and send notification
    if (updatedXp.level > (await this.powerOpsRepository.getXp(data.entityId, data.entityType))?.level || 0) {
      await this.notificationService.createNotification({
        userId: data.entityId,
        type: 'success',
        message: `Congratulations! You've reached PowerOps Level ${updatedXp.level}!`,
      });

      this.notificationService.wsService.broadcast(JSON.stringify({
        event: 'powerOpsAchievement',
        payload: {
          userId: data.entityId,
          achievementName: `Level Up: ${updatedXp.level}`,
          levelUp: true,
        } as PowerOpsAchievementPayload,
      }));
    }

    return updatedXp;
  }

  async getAllBadges(user: JwtPayload): Promise<Badge[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    return this.powerOpsRepository.getAllBadges();
  }

  async getBadges(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Badge[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    return this.powerOpsRepository.getBadgesForEntity(entityId, entityType);
  }

  async awardBadge(user: JwtPayload, data: AwardBadgeRequest): Promise<Badge> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    return this.powerOpsRepository.awardBadge(data);
  }

  // Helper for calculating usage cost
  private calculateUsageCost(usageUnits: number, costCategory: CostCategory): number {
    switch (costCategory) {
      case CostCategory.Compute: return usageUnits * 0.01;
      case CostCategory.Storage: return usageUnits * 0.001;
      case CostCategory.Bandwidth: return usageUnits * 0.005;
      case CostCategory.AIModel: return usageUnits * 0.05;
      default: return usageUnits * 0.01;
    }
  }
}