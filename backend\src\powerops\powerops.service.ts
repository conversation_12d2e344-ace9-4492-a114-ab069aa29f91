import { PowerOpsRepository } from './powerops.repository';
import { AuthorizationService } from '../auth/authorization.service';
import { NotificationService } from '../notifications/notification.service';
import { JwtPayload } from '../auth/auth.types';
import { EntityType, PERMISSIONS } from '../auth/authorization.types';
import { CustomError } from '../utils/errors';
import { db } from '../database/database';
import { v4 as uuidv4 } from 'uuid';
import logger from '../config/logger';
import {
  PowerOpsUsage,
  LogPowerOpsUsageRequest,
  Budget,
  CreateBudgetRequest,
  UpdateBudgetRequest,
  CostOptimizationRecommendation,
  ResourceUsageLimit,
  SetResourceUsageLimitRequest,
  Xp,
  AwardXpRequest,
  Badge,
  AwardBadgeRequest,
  Achievement,
  GrantAchievementRequest,
  Streak,
  LeaderboardEntry,
  Invoice,
  CreateInvoiceRequest,
  Payment,
  ProcessPaymentRequest,
  Notification,
  CreateNotificationRequest,
  CostCategory,
  PowerOpsAchievementPayload
} from './powerops.types';

export class PowerOpsService {
  private powerOpsRepository: PowerOpsRepository;
  private authorizationService: AuthorizationService;
  private notificationService: NotificationService;

  constructor(
    powerOpsRepository?: PowerOpsRepository,
    authorizationService?: AuthorizationService,
    notificationService?: NotificationService
  ) {
    this.powerOpsRepository = powerOpsRepository || new PowerOpsRepository(db);
    this.authorizationService = authorizationService || new AuthorizationService();
    this.notificationService = notificationService || new NotificationService(
      {} as any, {} as any
    );
  }

  async scaleServiceResources(serviceName: string, direction: 'up' | 'down'): Promise<void> {
    console.log(`Simulating scaling ${direction} for service: ${serviceName}`);
    // In a real scenario, this would interact with infrastructure APIs
  }

  // PowerOps Usage & Cost Management
  async logUsage(user: JwtPayload, data: LogPowerOpsUsageRequest): Promise<PowerOpsUsage> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_CREATE);
    const usageEntry = await this.powerOpsRepository.logPowerOpsUsage(data);
    return {
      entityId: usageEntry.entityId,
      entityType: usageEntry.entityType,
      totalUsageUnits: usageEntry.usageUnits,
      estimatedCost: usageEntry.estimatedCost,
    };
  }

  async getUsage(user: JwtPayload, entityId: string, entityType: EntityType, startDate?: string, endDate?: string): Promise<PowerOpsUsage[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);

    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read');
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }

    const usageEntries = await this.powerOpsRepository.getPowerOpsUsage(entityId, entityType, startDate, endDate);
    const aggregatedUsage: { [key: string]: PowerOpsUsage } = {};

    usageEntries.forEach(entry => {
      const key = `${entry.entityId}-${entry.entityType}`;
      if (!aggregatedUsage[key]) {
        aggregatedUsage[key] = {
          entityId: entry.entityId,
          entityType: entry.entityType,
          totalUsageUnits: 0,
          estimatedCost: 0,
        };
      }
      aggregatedUsage[key].totalUsageUnits += entry.usageUnits;
      aggregatedUsage[key].estimatedCost += entry.estimatedCost;
    });

    return Object.values(aggregatedUsage);
  }

  async getBudgets(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Budget[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);

    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read');
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW_ALL);
    }

    return this.powerOpsRepository.getBudgets(entityId, entityType);
  }

  async createBudget(user: JwtPayload, data: CreateBudgetRequest): Promise<Budget> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    return this.powerOpsRepository.createBudget(data);
  }

  async updateBudget(user: JwtPayload, budgetId: string, data: UpdateBudgetRequest): Promise<Budget> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
    if (!existingBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    this.authorizationService.canAccessResource(user, {
      ownerId: existingBudget.entityType === EntityType.User ? existingBudget.entityId : undefined,
      organizationId: existingBudget.entityType === EntityType.Organization ? existingBudget.entityId : user.organizationId,
      teamId: existingBudget.entityType === EntityType.Team ? existingBudget.entityId : user.teamId,
    }, 'update');

    const updatedBudget = await this.powerOpsRepository.updateBudget(budgetId, data);
    if (!updatedBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    return updatedBudget;
  }

  async deleteBudget(user: JwtPayload, budgetId: string): Promise<boolean> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
    if (!existingBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    this.authorizationService.canAccessResource(user, {
      ownerId: existingBudget.entityType === EntityType.User ? existingBudget.entityId : undefined,
      organizationId: existingBudget.entityType === EntityType.Organization ? existingBudget.entityId : user.organizationId,
      teamId: existingBudget.entityType === EntityType.Team ? existingBudget.entityId : user.teamId,
    }, 'delete');

    const deleted = await this.powerOpsRepository.deleteBudget(budgetId);
    if (!deleted) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }

    return deleted;
  }

  async getCostOptimizationRecommendations(user: JwtPayload, entityId: string, entityType: EntityType): Promise<CostOptimizationRecommendation[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    return this.powerOpsRepository.getCostOptimizationRecommendations(entityId, entityType);
  }

  async getResourceUsageLimits(user: JwtPayload, entityId: string, entityType: EntityType): Promise<ResourceUsageLimit[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    return this.powerOpsRepository.getResourceUsageLimits(entityId, entityType);
  }

  async setResourceUsageLimit(user: JwtPayload, data: SetResourceUsageLimitRequest): Promise<ResourceUsageLimit> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    return this.powerOpsRepository.setResourceUsageLimit(data);
  }

  // Gamification (XP, Badges, Achievements, Streaks)
  async getXp(user: JwtPayload, id: string, entityType: EntityType): Promise<Xp> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    const xp = await this.powerOpsRepository.getXp(id, entityType);
    if (!xp) {
      return { entityId: id, entityType, currentXp: 0, level: 1 };
    }
    return xp;
  }

  async awardXp(user: JwtPayload, data: AwardXpRequest): Promise<Xp> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    const updatedXp = await this.powerOpsRepository.awardXp(data);

    // Check for level-up and send notification
    if (updatedXp.level > (await this.powerOpsRepository.getXp(data.entityId, data.entityType))?.level || 0) {
      const notification = await this.notificationService.createNotification({
        userId: data.entityId,
        type: 'success',
        message: `Congratulations! You've reached PowerOps Level ${updatedXp.level}!`,
      });

      // The createNotification method already calls emitNotification internally
      logger.info(`Level up notification sent for user ${data.entityId}: Level ${updatedXp.level}`);
    }

    return updatedXp;
  }

  async getAllBadges(user: JwtPayload): Promise<Badge[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    return this.powerOpsRepository.getAllBadges();
  }

  async getBadges(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Badge[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    return this.powerOpsRepository.getBadgesForEntity(entityId, entityType);
  }

  async awardBadge(user: JwtPayload, data: AwardBadgeRequest): Promise<Badge> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');

    return this.powerOpsRepository.awardBadge(data);
  }

  // Achievement methods
  async getAchievements(user: JwtPayload, entityId: string, entityType: EntityType): Promise<any[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    logger.info(`Getting achievements for ${entityType} ${entityId} by user ${user.userId}`);

    // Placeholder implementation
    return [
      {
        id: uuidv4(),
        name: 'First Steps',
        description: 'Complete your first task',
        entityId,
        entityType,
        unlockedAt: new Date().toISOString(),
        progress: 100
      }
    ];
  }

  async grantAchievement(user: JwtPayload, data: GrantAchievementRequest): Promise<any> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    logger.info(`Granting achievement ${data.achievementId} to ${data.entityType} ${data.entityId} by user ${user.userId}`);

    // Placeholder implementation
    return {
      id: uuidv4(),
      achievementId: data.achievementId,
      entityId: data.entityId,
      entityType: data.entityType,
      grantedAt: new Date().toISOString(),
      grantedBy: user.userId
    };
  }

  // Streak methods
  async getStreaks(user: JwtPayload, entityId: string, entityType: EntityType): Promise<any[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    logger.info(`Getting streaks for ${entityType} ${entityId} by user ${user.userId}`);

    // Placeholder implementation
    return [
      {
        id: uuidv4(),
        type: 'daily_login',
        currentStreak: 5,
        longestStreak: 12,
        lastActivity: new Date().toISOString(),
        entityId,
        entityType
      }
    ];
  }

  // Invoice methods
  async getInvoices(user: JwtPayload, entityId: string, entityType: EntityType): Promise<any[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    logger.info(`Getting invoices for ${entityType} ${entityId} by user ${user.userId}`);

    // Placeholder implementation
    return [
      {
        id: uuidv4(),
        entityId,
        entityType,
        amount: 250.00,
        currency: 'USD',
        status: 'paid',
        billingPeriodStart: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        billingPeriodEnd: new Date().toISOString(),
        createdAt: new Date().toISOString()
      }
    ];
  }

  async createInvoice(user: JwtPayload, data: CreateInvoiceRequest): Promise<any> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    logger.info(`Creating invoice for ${data.entityType} ${data.entityId} by user ${user.userId}`);

    // Placeholder implementation
    return {
      id: uuidv4(),
      ...data,
      status: 'pending',
      createdAt: new Date().toISOString(),
      createdBy: user.userId
    };
  }

  // Payment methods
  async processPayment(user: JwtPayload, data: ProcessPaymentRequest): Promise<any> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    logger.info(`Processing payment for invoice ${data.invoiceId} by user ${user.userId}`);

    // Placeholder implementation
    return {
      id: uuidv4(),
      invoiceId: data.invoiceId,
      amount: data.amount,
      currency: data.currency,
      status: 'completed',
      processedAt: new Date().toISOString(),
      processedBy: user.userId
    };
  }

  // Leaderboard methods
  async getLeaderboard(user: JwtPayload, metric: string, limit?: number): Promise<any[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    logger.info(`Getting leaderboard for metric ${metric} by user ${user.userId}`);

    // Placeholder implementation
    return [
      {
        rank: 1,
        entityId: 'user-1',
        entityType: EntityType.User,
        value: 1500,
        metric
      },
      {
        rank: 2,
        entityId: 'user-2',
        entityType: EntityType.User,
        value: 1200,
        metric
      }
    ].slice(0, limit || 10);
  }

  // Notification methods
  async getNotifications(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Notification[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    logger.info(`Getting notifications for ${entityType} ${entityId} by user ${user.userId}`);

    // Placeholder implementation
    return [
      {
        id: uuidv4(),
        entityId,
        entityType,
        type: 'budget_alert',
        message: 'Budget threshold reached',
        timestamp: new Date().toISOString(),
        read: false
      }
    ];
  }

  async createNotification(user: JwtPayload, data: CreateNotificationRequest): Promise<Notification> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_CREATE);
    logger.info(`Creating notification for ${data.entityType} ${data.entityId} by user ${user.userId}`);

    // Placeholder implementation
    const notification: Notification = {
      id: uuidv4(),
      ...data,
      timestamp: new Date().toISOString(),
      read: false
    };

    return notification;
  }

  // Helper for calculating usage cost
  private calculateUsageCost(usageUnits: number, costCategory: CostCategory): number {
    switch (costCategory) {
      case CostCategory.Compute: return usageUnits * 0.01;
      case CostCategory.Storage: return usageUnits * 0.001;
      case CostCategory.Bandwidth: return usageUnits * 0.005;
      case CostCategory.AIModel: return usageUnits * 0.05;
      default: return usageUnits * 0.01;
    }
  }
}