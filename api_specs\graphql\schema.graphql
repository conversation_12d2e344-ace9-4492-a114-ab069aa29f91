# GraphQL Schema for The AIgency and PAIM Backend Services

# Scalar Types
scalar DateTime
scalar UUID

# Enums
enum PaimTier {
  SYSTEM_ADMIN
  COMPANY_ADMIN
  POWER_USER
  PERSONAL
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  BUSY
  AVAILABLE
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum WorkflowStatus {
  ACTIVE
  INACTIVE
  DRAFT
}

enum PowerOpType {
  AI_COMPUTATION
  DATA_STORAGE
  API_CALLS
  # Add more as needed
}

enum EntityType {
  USER
  PAIM_INSTANCE
}

enum EscalationStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum EscalationSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

# Core Types
type User {
  id: UUID!
  email: String!
  firstName: String
  lastName: String
  companyId: UUID
  paimTier: PaimTier!
  roles: [Role!]!
  createdAt: DateTime!
  updatedAt: DateTime!
  paimInstances: [PaimInstance!]!
  tasks: [Task!]!
  powerOpsUsage: PowerOpsUsage
  xp: XP
  badges: [Badge!]!
  achievements: [Achievement!]!
  streaks: [Streak!]!
  budgets: [Budget!]!
}

type Role {
  id: UUID!
  name: String!
  description: String
}

type PaimInstance {
  id: UUID!
  name: String!
  description: String
  owner: User!
  tier: PaimTier!
  status: String! # active, inactive, suspended
  createdAt: DateTime!
  updatedAt: DateTime!
  agents: [Agent!]!
  workflows: [Workflow!]!
  tasks: [Task!]!
  powerOpsUsage: PowerOpsUsage
  xp: XP
  badges: [Badge!]!
  achievements: [Achievement!]!
  streaks: [Streak!]!
  budgets: [Budget!]!
  hierarchy: PaimHierarchy
}

type Agent {
  id: UUID!
  name: String!
  persona: String!
  description: String
  status: AgentStatus!
  paimInstance: PaimInstance!
  capabilities: [String!]!
  createdAt: DateTime!
  updatedAt: DateTime!
  performanceMetrics: AgentPerformanceMetrics
  assignedTasks: [Task!]!
}

type Workflow {
  id: UUID!
  name: String!
  description: String
  owner: User!
  paimInstance: PaimInstance!
  status: WorkflowStatus!
  definition: String! # JSON string or custom scalar for workflow definition
  createdAt: DateTime!
  updatedAt: DateTime!
  tasks: [Task!]!
  executions: [WorkflowExecutionStatus!]!
}

type Task {
  id: UUID!
  title: String!
  description: String
  status: TaskStatus!
  priority: TaskPriority!
  assignedTo: UserOrAgent # Union type for assignedTo
  dueDate: DateTime
  workflow: Workflow
  createdAt: DateTime!
  updatedAt: DateTime!
}

union UserOrAgent = User | Agent

type CollaborationSession {
  id: UUID!
  name: String!
  description: String
  owner: User!
  paimInstance: PaimInstance!
  participants: [User!]!
  status: String! # active, ended
  createdAt: DateTime!
  endedAt: DateTime
}

# Cultural Sensitivity Types
type LocalizationSettings {
  entityId: UUID!
  entityType: EntityType!
  preferredLanguage: String! # BCP 47 tag
  timezone: String
  dateFormat: String
  timeFormat: String
  currency: String
  culturalPreferences: JSON # Custom scalar for JSON object
}

type CulturalContext {
  locale: String! # BCP 47 tag
  culturalNorms: [String!]!
  commonPhrases: JSON # Map of phrases
  sensitiveTopics: [String!]!
  historicalContext: String
  socialEtiquette: String
  businessPractices: String
  lastUpdated: DateTime!
}

type DialectDetectionResult {
  text: String!
  detectedDialect: String! # MSA, EGY, SAU, UAE, LEV, MAG, Other
  confidence: Float!
  possibleDialects: [DialectConfidence!]!
}

type DialectConfidence {
  dialect: String!
  confidence: Float!
}

# PowerOps & Gamification Types
type PowerOpsUsage {
  entityId: UUID!
  entityType: EntityType!
  totalUsageUnits: Float!
  estimatedCost: Float!
  currency: String!
  usageBreakdown: JSON # Map of PowerOpType to units
  lastUpdated: DateTime!
}

type PowerOpsUsageEntry {
  id: UUID!
  entityId: UUID!
  entityType: EntityType!
  powerOpType: PowerOpType!
  unitsConsumed: Float!
  cost: Float!
  timestamp: DateTime!
}

type XP {
  entityId: UUID!
  entityType: EntityType!
  currentXp: Int!
  level: Int!
  xpToNextLevel: Int!
  lastUpdated: DateTime!
}

type Badge {
  id: UUID!
  name: String!
  description: String
  imageUrl: String
  awardedAt: DateTime!
}

type Achievement {
  id: UUID!
  name: String!
  description: String
  progress: Float! # 0.0 - 1.0
  isCompleted: Boolean!
  completedAt: DateTime
}

type Streak {
  id: UUID!
  name: String!
  currentLength: Int!
  maxLength: Int!
  lastActivityDate: String! # YYYY-MM-DD
  isBroken: Boolean!
}

type Budget {
  id: UUID!
  entityId: UUID!
  entityType: EntityType!
  monthlyLimit: Float!
  currentSpend: Float!
  currency: String!
  startDate: String! # YYYY-MM-DD
  endDate: String! # YYYY-MM-DD
  alertThreshold: Float
}

# System Administration Types
type SystemHealthStatus {
  overallStatus: String! # operational, degraded, critical
  components: JSON # Map of component status
  lastChecked: DateTime!
}

type SystemMetrics {
  metricType: String!
  unit: String!
  dataPoints: [MetricDataPoint!]!
  average: Float
  min: Float
  max: Float
}

type MetricDataPoint {
  timestamp: DateTime!
  value: Float!
}

type AutoHealingStatus {
  processId: UUID!
  component: String!
  status: String! # initiated, in_progress, completed, failed, cancelled
  startTime: DateTime!
  endTime: DateTime
  message: String
}

type Escalation {
  id: UUID!
  title: String!
  description: String
  severity: EscalationSeverity!
  status: EscalationStatus!
  assignedTo: String # User ID or team name
  createdAt: DateTime!
  updatedAt: DateTime!
  resolvedAt: DateTime
}

type AuditLogEntry {
  id: UUID!
  timestamp: DateTime!
  userId: UUID
  paimInstanceId: UUID
  eventType: String!
  resourceType: String
  resourceId: UUID
  details: JSON # Custom scalar for JSON object
}

type PaimHierarchy {
  paimInstanceId: UUID!
  hierarchyTree: JSON # Nested JSON structure
  lastUpdated: DateTime!
}

# Pagination
type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

interface Connection {
  pageInfo: PageInfo!
  edges: [Edge!]
}

interface Edge {
  cursor: String!
  node: Node
}

interface Node {
  id: UUID!
}

type UserConnection implements Connection {
  pageInfo: PageInfo!
  edges: [UserEdge]
}

type UserEdge implements Edge {
  cursor: String!
  node: User
}

type PaimInstanceConnection implements Connection {
  pageInfo: PageInfo!
  edges: [PaimInstanceEdge]
}

type PaimInstanceEdge implements Edge {
  cursor: String!
  node: PaimInstance
}

type AgentConnection implements Connection {
  pageInfo: PageInfo!
  edges: [AgentEdge]
}

type AgentEdge implements Edge {
  cursor: String!
  node: Agent
}

type WorkflowConnection implements Connection {
  pageInfo: PageInfo!
  edges: [WorkflowEdge]
}

type WorkflowEdge implements Edge {
  cursor: String!
  node: Workflow
}

type TaskConnection implements Connection {
  pageInfo: PageInfo!
  edges: [TaskEdge]
}

type TaskEdge implements Edge {
  cursor: String!
  node: Task
}

type EscalationConnection implements Connection {
  pageInfo: PageInfo!
  edges: [EscalationEdge]
}

type EscalationEdge implements Edge {
  cursor: String!
  node: Escalation
}

type AuditLogEntryConnection implements Connection {
  pageInfo: PageInfo!
  edges: [AuditLogEntryEdge]
}

type AuditLogEntryEdge implements Edge {
  cursor: String!
  node: AuditLogEntry
}

# Custom Scalar for JSON
scalar JSON

# Query Type
type Query {
  # Authentication & Authorization
  user(id: UUID!): User
  users(first: Int, after: String, companyId: UUID, paimTier: PaimTier): UserConnection!
  roles: [Role!]!

  # PAIM Management
  paimInstance(id: UUID!): PaimInstance
  paimInstances(first: Int, after: String, ownerId: UUID, tier: PaimTier, status: String): PaimInstanceConnection!
  paimHierarchy(paimInstanceId: UUID!): PaimHierarchy

  # Agent Management
  agent(id: UUID!): Agent
  agents(first: Int, after: String, paimInstanceId: UUID, persona: String, status: AgentStatus): AgentConnection!
  agentPerformanceMetrics(agentId: UUID!, startDate: String, endDate: String): AgentPerformanceMetrics

  # Cultural Sensitivity
  localizationSettings(entityId: UUID!, entityType: EntityType!): LocalizationSettings
  culturalContext(locale: String!, entityId: UUID, entityType: EntityType): CulturalContext

  # PowerOps & Gamification
  powerOpsUsage(entityId: UUID!, entityType: EntityType!): PowerOpsUsage
  xp(entityId: UUID!, entityType: EntityType!): XP
  badges(entityId: UUID!, entityType: EntityType!): [Badge!]!
  achievements(entityId: UUID!, entityType: EntityType!): [Achievement!]!
  streaks(entityId: UUID!, entityType: EntityType!): [Streak!]!
  budgets(entityId: UUID!, entityType: EntityType!): [Budget!]!

  # Workflow & Collaboration
  workflow(id: UUID!): Workflow
  workflows(first: Int, after: String, paimInstanceId: UUID, status: WorkflowStatus): WorkflowConnection!
  task(id: UUID!): Task
  tasks(first: Int, after: String, workflowId: UUID, status: TaskStatus, assignedToId: UUID): TaskConnection!
  collaborationSession(id: UUID!): CollaborationSession

  # System Administration
  systemHealth: SystemHealthStatus
  systemMetrics(metricType: String!, duration: String): SystemMetrics
  escalation(id: UUID!): Escalation
  escalations(first: Int, after: String, status: EscalationStatus, severity: EscalationSeverity): EscalationConnection!
  auditLogs(first: Int, after: String, userId: UUID, eventType: String, startDate: DateTime, endDate: DateTime): AuditLogEntryConnection!
}

# Mutation Type
type Mutation {
  # Authentication & Authorization
  updateUserPaimTier(userId: UUID!, newTier: PaimTier!, reason: String): User!
  assignUserRoles(userId: UUID!, roleIds: [UUID!]!): User!

  # PAIM Management
  createPaimInstance(name: String!, description: String, ownerId: UUID!, tier: PaimTier!): PaimInstance!
  updatePaimInstance(id: UUID!, name: String, description: String, status: String): PaimInstance!
  deletePaimInstance(id: UUID!): Boolean!
  requestPaimTierChange(paimInstanceId: UUID!, requestedTier: PaimTier!, reason: String!): AutoHealingStatus! # Reusing AutoHealingStatus for request status
  communicateWithPaim(sourcePaimInstanceId: UUID!, targetPaimInstanceId: UUID!, message: String!, messageType: String): Boolean!
  updatePaimHierarchy(paimInstanceId: UUID!, hierarchyTree: JSON!): PaimHierarchy!

  # Agent Management
  createAgent(name: String!, persona: String!, paimInstanceId: UUID!, description: String, capabilities: [String!]): Agent!
  updateAgent(id: UUID!, name: String, persona: String, description: String, status: AgentStatus, capabilities: [String!]): Agent!
  deleteAgent(id: UUID!): Boolean!
  assignAgent(agentId: UUID!, assignedToType: String!, assignedToId: UUID!, assignmentType: String!, notes: String): Agent!
  executeWorkflow(workflowId: UUID!, inputData: JSON!, agentIds: [UUID!], callbackUrl: String): WorkflowExecutionStatus!

  # Cultural Sensitivity
  updateLocalizationSettings(entityId: UUID!, entityType: EntityType!, preferredLanguage: String, timezone: String, dateFormat: String, timeFormat: String, currency: String, culturalPreferences: JSON): LocalizationSettings!
  createOrUpdateCulturalContext(locale: String!, culturalNorms: [String!], commonPhrases: JSON, sensitiveTopics: [String!], historicalContext: String, socialEtiquette: String, businessPractices: String): CulturalContext!
  detectArabicDialect(text: String!): DialectDetectionResult!
  adaptArabicContent(text: String!, targetDialect: String!): String! # Returns adaptedText

  # PowerOps & Gamification
  logPowerOpsUsage(entityId: UUID!, entityType: EntityType!, powerOpType: PowerOpType!, unitsConsumed: Float!, costPerUnit: Float): PowerOpsUsageEntry!
  awardXp(entityId: UUID!, entityType: EntityType!, amount: Int!, reason: String): XP!
  awardBadge(entityId: UUID!, entityType: EntityType!, badgeId: UUID!, reason: String): Badge!
  grantAchievement(entityId: UUID!, entityType: EntityType!, achievementId: UUID!, progress: Float): Achievement!
  createBudget(entityId: UUID!, entityType: EntityType!, monthlyLimit: Float!, currency: String!, alertThreshold: Float): Budget!
  updateBudget(id: UUID!, monthlyLimit: Float, alertThreshold: Float): Budget!
  deleteBudget(id: UUID!): Boolean!

  # Workflow & Collaboration
  createWorkflow(name: String!, description: String, ownerId: UUID!, paimInstanceId: UUID!, definition: JSON!): Workflow!
  updateWorkflow(id: UUID!, name: String, description: String, status: WorkflowStatus, definition: JSON): Workflow!
  deleteWorkflow(id: UUID!): Boolean!
  createTask(title: String!, description: String, status: TaskStatus!, priority: TaskPriority!, assignedToId: UUID, assignedToType: String, dueDate: DateTime, workflowId: UUID): Task!
  updateTask(id: UUID!, title: String, description: String, status: TaskStatus, priority: TaskPriority, assignedToId: UUID, assignedToType: String, dueDate: DateTime, workflowId: UUID): Task!
  deleteTask(id: UUID!): Boolean!
  startCollaborationSession(name: String!, description: String, ownerId: UUID!, paimInstanceId: UUID!, initialParticipants: [UUID!]): CollaborationSession!
  joinCollaborationSession(sessionId: UUID!, userId: UUID!): CollaborationSession!
  leaveCollaborationSession(sessionId: UUID!, userId: UUID!): Boolean!
  sendCrossTenantMessage(senderPaimInstanceId: UUID!, recipientPaimInstanceId: UUID!, messageContent: String!, messageType: String, relatedTaskId: UUID): Boolean!

  # System Administration
  triggerAutoHealing(component: String!, reason: String!, parameters: JSON): AutoHealingStatus!
  createEscalation(title: String!, description: String, severity: EscalationSeverity!): Escalation!
  updateEscalation(id: UUID!, title: String, description: String, severity: EscalationSeverity, status: EscalationStatus, assignedTo: String, resolutionNotes: String): Escalation!
}