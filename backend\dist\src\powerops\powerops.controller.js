"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsController = void 0;
const express_1 = require("express");
const powerops_service_1 = require("./powerops.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const validation_1 = require("../utils/validation"); // Import validate middleware
const errors_1 = require("../utils/errors");
const express_rate_limit_1 = __importDefault(require("express-rate-limit")); // Import rateLimit
const type_guards_1 = require("../utils/type-guards");
const authorization_1 = require("../middleware/authorization"); // Import authorization middleware
const permissions_1 = require("../auth/permissions"); // Import permissions
const powerops_types_1 = require("./powerops.types");
const powerops_validation_1 = require("./powerops.validation"); // Import Joi schemas
// Rate limiting for XP endpoints to prevent farming
const xpLimiter = (0, express_rate_limit_1.default)({
    windowMs: 60 * 1000, // 1 minute
    max: 5, // Limit each IP to 5 requests per minute
    message: 'Too many XP requests from this IP, please try again after a minute.',
    keyGenerator: (req) => {
        // Use a combination of IP and IP and user ID (if authenticated) for more granular limiting
        const requestWithUser = req;
        return (requestWithUser.ip || 'unknown') + (requestWithUser.user ? `-${requestWithUser.user.userId}` : '');
    },
});
class PowerOpsController {
    router;
    powerOpsService;
    constructor(notificationService) {
        this.powerOpsService = new powerops_service_1.PowerOpsService(undefined, undefined, notificationService); // Pass notificationService
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    // PowerOps Usage & Cost Management
    initializeRoutes() {
        this.router.post('/usage', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_CREATE]), (0, validation_1.validate)(powerops_validation_1.logPowerOpsUsageSchema), (0, asyncHandler_1.asyncHandler)(this.logPowerOpsUsage.bind(this)));
        this.router.get('/usage/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.getPowerOpsUsageQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getPowerOpsUsage.bind(this)));
        this.router.post('/xp/add', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL]), xpLimiter, (0, validation_1.validate)(powerops_validation_1.awardXpSchema), (0, asyncHandler_1.asyncHandler)(this.awardXp.bind(this))); // Apply XP rate limit
        this.router.get('/xp/user/:id', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, asyncHandler_1.asyncHandler)(this.getXpByUser.bind(this))); // New endpoint for user XP
        this.router.get('/xp/org/:id', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, asyncHandler_1.asyncHandler)(this.getXpByOrg.bind(this))); // New endpoint for organization XP
        this.router.post('/badges/award', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL]), (0, validation_1.validate)(powerops_validation_1.awardBadgeSchema), (0, asyncHandler_1.asyncHandler)(this.awardBadge.bind(this))); // Changed to /badges/award
        this.router.get('/badges', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, asyncHandler_1.asyncHandler)(this.getAllBadges.bind(this))); // New endpoint for all badges
        this.router.get('/badges/user/:id', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getBadgesByUser.bind(this))); // New endpoint for user badges
        this.router.post('/achievements', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL]), (0, validation_1.validate)(powerops_validation_1.grantAchievementSchema), (0, asyncHandler_1.asyncHandler)(this.grantAchievement.bind(this)));
        this.router.get('/achievements/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getAchievements.bind(this)));
        this.router.get('/streaks/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getStreaks.bind(this)));
        this.router.get('/leaderboard/:metric', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.getLeaderboardQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getLeaderboard.bind(this)));
        this.router.get('/budgets/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getBudgets.bind(this)));
        this.router.post('/budgets', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.createBudgetSchema), (0, asyncHandler_1.asyncHandler)(this.createBudget.bind(this)));
        this.router.put('/budgets/:budgetId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.updateBudgetSchema), (0, asyncHandler_1.asyncHandler)(this.updateBudget.bind(this)));
        this.router.delete('/budgets/:budgetId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, asyncHandler_1.asyncHandler)(this.deleteBudget.bind(this)));
        this.router.get('/invoices/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getInvoices.bind(this)));
        this.router.post('/invoices', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.createInvoiceSchema), (0, asyncHandler_1.asyncHandler)(this.createInvoice.bind(this)));
        this.router.post('/payments', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.processPaymentSchema), (0, asyncHandler_1.asyncHandler)(this.processPayment.bind(this)));
        this.router.get('/recommendations/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getCostOptimizationRecommendations.bind(this)));
        this.router.get('/resource-limits/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getResourceUsageLimits.bind(this)));
        this.router.post('/resource-limits', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.setResourceUsageLimitSchema), (0, asyncHandler_1.asyncHandler)(this.setResourceUsageLimit.bind(this)));
        this.router.get('/notifications/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getNotifications.bind(this)));
        this.router.post('/notifications', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_CREATE]), (0, validation_1.validate)(powerops_validation_1.createNotificationSchema), (0, asyncHandler_1.asyncHandler)(this.createNotification.bind(this)));
    }
    logPowerOpsUsage = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const usage = await this.powerOpsService.logUsage(req.user, data);
        res.status(201).json(usage);
    });
    getPowerOpsUsage = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType, startDate, endDate } = req.query;
        // Validation is now handled by Joi middleware
        const usage = await this.powerOpsService.getUsage(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), entityType, // Keep as EntityType, validation above
        (0, type_guards_1.getParam)(startDate), (0, type_guards_1.getParam)(endDate));
        res.status(200).json(usage);
    });
    // Gamification (XP)
    getXpByUser = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { id } = req.params;
        const xp = await this.powerOpsService.getXp(req.user, (0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.User);
        res.status(200).json(xp);
    });
    getXpByOrg = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { id } = req.params;
        const xp = await this.powerOpsService.getXp(req.user, (0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.PaimInstance); // Assuming org is PaimInstance
        res.status(200).json(xp);
    });
    awardXp = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const xp = await this.powerOpsService.awardXp(req.user, data);
        res.status(200).json(xp);
    });
    // Gamification (Badges)
    getAllBadges = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const badges = await this.powerOpsService.getAllBadges(req.user);
        res.status(200).json(badges);
    });
    getBadgesByUser = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { id } = req.params;
        const badges = await this.powerOpsService.getBadges(req.user, (0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.User);
        res.status(200).json(badges);
    });
    awardBadge = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const badge = await this.powerOpsService.awardBadge(req.user, data);
        res.status(200).json(badge);
    });
    // Gamification (Achievements)
    getAchievements = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType } = req.query;
        // Validation is now handled by Joi middleware
        const achievements = await this.powerOpsService.getAchievements(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(achievements);
    });
    grantAchievement = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const achievement = await this.powerOpsService.grantAchievement(req.user, data);
        res.status(200).json(achievement);
    });
    // Gamification (Streaks)
    getStreaks = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType } = req.query;
        // Validation is now handled by Joi middleware
        const streaks = await this.powerOpsService.getStreaks(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(streaks);
    });
    // Cost Management (Budgets)
    getBudgets = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType } = req.query;
        // Validation is now handled by Joi middleware
        const budgets = await this.powerOpsService.getBudgets(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(budgets);
    });
    createBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const budget = await this.powerOpsService.createBudget(req.user, data);
        res.status(201).json(budget);
    });
    updateBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { budgetId } = req.params;
        const data = req.body;
        const updatedBudget = await this.powerOpsService.updateBudget(req.user, (0, type_guards_1.requireParam)(budgetId, 'budgetId'), data);
        res.status(200).json(updatedBudget);
    });
    deleteBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { budgetId } = req.params;
        await this.powerOpsService.deleteBudget(req.user, (0, type_guards_1.requireParam)(budgetId, 'budgetId'));
        res.status(204).send();
    });
    // Billing (Invoices)
    getInvoices = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType } = req.query;
        // Validation is now handled by Joi middleware
        const invoices = await this.powerOpsService.getInvoices(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(invoices);
    });
    createInvoice = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const invoice = await this.powerOpsService.createInvoice(req.user, data);
        res.status(201).json(invoice);
    });
    // Billing (Payments)
    processPayment = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const payment = await this.powerOpsService.processPayment(req.user, data);
        res.status(200).json(payment);
    });
    // Gamification (Leaderboard)
    getLeaderboard = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { metric, limit } = req.query;
        // Validation is now handled by Joi middleware
        const leaderboard = await this.powerOpsService.getLeaderboard(req.user, (0, type_guards_1.requireParam)(metric, 'metric'), limit ? parseInt((0, type_guards_1.getParam)(limit)) : undefined);
        res.status(200).json(leaderboard);
    });
    // Cost Management (Recommendations)
    getCostOptimizationRecommendations = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType } = req.query;
        // Validation is now handled by Joi middleware
        const recommendations = await this.powerOpsService.getCostOptimizationRecommendations(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(recommendations);
    });
    // Resource Management (Limits)
    getResourceUsageLimits = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType } = req.query;
        // Validation is now handled by Joi middleware
        const limits = await this.powerOpsService.getResourceUsageLimits(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(limits);
    });
    setResourceUsageLimit = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const limit = await this.powerOpsService.setResourceUsageLimit(req.user, data);
        res.status(201).json(limit);
    });
    // Notifications
    getNotifications = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { entityId, entityType } = req.query;
        // Validation is now handled by Joi middleware
        const notifications = await this.powerOpsService.getNotifications(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
        res.status(200).json(notifications);
    });
    createNotification = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const data = req.body;
        const notification = await this.powerOpsService.createNotification(req.user, data);
        res.status(201).json(notification);
    });
}
exports.PowerOpsController = PowerOpsController;
