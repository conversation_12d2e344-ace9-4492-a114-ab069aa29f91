"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditRepository = void 0;
const db_1 = __importDefault(require("../database/db"));
const crypto_1 = __importDefault(require("crypto"));
class AuditRepository {
    AUDIT_TABLE = 'audit_events'; // Assuming a new, more generic audit table
    constructor() {
        // Ensure the audit_events table exists and has the necessary columns.
        // This would typically be handled by migrations, but for robustness,
        // a check or a more explicit migration strategy might be needed.
    }
    generateHash(event) {
        const data = JSON.stringify({
            tenantId: event.tenantId,
            userId: event.userId,
            category: event.category,
            severity: event.severity,
            operationType: event.operationType,
            description: event.description,
            timestamp: event.timestamp.toISOString(),
            ipAddress: event.ipAddress,
            userAgent: event.userAgent,
            resourceId: event.resourceId,
            metadata: event.metadata,
            correlationId: event.correlationId,
            complianceStatus: event.complianceStatus,
        });
        return crypto_1.default.createHash('sha256').update(data).digest('hex');
    }
    async getLatestEventHash(tenantId) {
        const latestEvent = await (0, db_1.default)(this.AUDIT_TABLE)
            .where({ tenant_id: tenantId })
            .orderBy('timestamp', 'desc')
            .first();
        return latestEvent ? latestEvent.event_hash : null;
    }
    async saveAuditEvent(event) {
        const previousHash = await this.getLatestEventHash(event.tenantId);
        const currentHash = this.generateHash(event);
        await (0, db_1.default)(this.AUDIT_TABLE).insert({
            tenant_id: event.tenantId,
            user_id: event.userId,
            category: event.category,
            severity: event.severity,
            operation_type: event.operationType,
            description: event.description,
            timestamp: event.timestamp,
            ip_address: event.ipAddress,
            user_agent: event.userAgent,
            resource_id: event.resourceId,
            metadata: JSON.stringify(event.metadata),
            correlation_id: event.correlationId,
            compliance_status: JSON.stringify(event.complianceStatus),
            event_hash: currentHash,
            previous_event_hash: previousHash,
            is_tamper_proofed: true, // Mark as tamper-proofed upon insertion
        });
    }
    async getAuditEvents(filters, page, limit) {
        let query = (0, db_1.default)(this.AUDIT_TABLE).select('*');
        if (filters.tenantId) {
            query = query.where('tenant_id', filters.tenantId);
        }
        if (filters.userId) {
            query = query.where('user_id', filters.userId);
        }
        if (filters.category) {
            query = query.where('category', filters.category);
        }
        if (filters.severity) {
            query = query.where('severity', filters.severity);
        }
        if (filters.operationType) {
            query = query.where('operation_type', filters.operationType);
        }
        if (filters.startDate) {
            query = query.where('timestamp', '>=', filters.startDate);
        }
        if (filters.endDate) {
            query = query.where('timestamp', '<=', filters.endDate);
        }
        if (filters.resourceId) {
            query = query.where('resource_id', filters.resourceId);
        }
        if (filters.keywords) {
            query = query.andWhere((builder) => {
                builder
                    .orWhere('description', 'like', `%${filters.keywords}%`)
                    .orWhere('operation_type', 'like', `%${filters.keywords}%`)
                    .orWhere('metadata', 'like', `%${filters.keywords}%`);
            });
        }
        const totalQuery = query.clone().count('* as total');
        const totalResult = await totalQuery.first();
        const total = totalResult ? parseInt(totalResult.total, 10) : 0;
        const events = await query
            .orderBy('timestamp', 'desc')
            .offset((page - 1) * limit)
            .limit(limit);
        return {
            events: events.map((event) => ({
                id: event.id,
                tenantId: event.tenant_id,
                userId: event.user_id,
                category: event.category,
                severity: event.severity,
                operationType: event.operation_type,
                description: event.description,
                timestamp: event.timestamp,
                ipAddress: event.ip_address,
                userAgent: event.user_agent,
                resourceId: event.resource_id,
                metadata: JSON.parse(event.metadata),
                isTamperProofed: event.is_tamper_proofed,
                correlationId: event.correlation_id,
                complianceStatus: JSON.parse(event.compliance_status),
            })),
            total,
            page,
            limit,
        };
    }
    async getComplianceReport(tenantId, complianceStandard, startDate, endDate) {
        const events = await (0, db_1.default)(this.AUDIT_TABLE)
            .where('tenant_id', tenantId)
            .where('timestamp', '>=', startDate)
            .where('timestamp', '<=', endDate)
            .whereRaw(`JSON_EXTRACT(compliance_status, '$.${complianceStandard}') = true`)
            .select('*');
        return events.map((event) => ({
            id: event.id,
            tenantId: event.tenant_id,
            userId: event.user_id,
            category: event.category,
            severity: event.severity,
            operationType: event.operation_type,
            description: event.description,
            timestamp: event.timestamp,
            ipAddress: event.ip_address,
            userAgent: event.user_agent,
            resourceId: event.resource_id,
            metadata: JSON.parse(event.metadata),
            isTamperProofed: event.is_tamper_proofed,
            correlationId: event.correlation_id,
            complianceStatus: JSON.parse(event.compliance_status),
        }));
    }
    async getAuditAnalytics(tenantId, startDate, endDate) {
        const totalEvents = await (0, db_1.default)(this.AUDIT_TABLE)
            .where('tenant_id', tenantId)
            .where('timestamp', '>=', startDate)
            .where('timestamp', '<=', endDate)
            .count('* as total');
        const eventsByCategory = await (0, db_1.default)(this.AUDIT_TABLE)
            .where('tenant_id', tenantId)
            .where('timestamp', '>=', startDate)
            .where('timestamp', '<=', endDate)
            .select('category')
            .count('* as count')
            .groupBy('category');
        const eventsBySeverity = await (0, db_1.default)(this.AUDIT_TABLE)
            .where('tenant_id', tenantId)
            .where('timestamp', '>=', startDate)
            .where('timestamp', '<=', endDate)
            .select('severity')
            .count('* as count')
            .groupBy('severity');
        return {
            totalEvents: totalEvents[0] ? totalEvents[0].total : 0,
            eventsByCategory: eventsByCategory,
            eventsBySeverity: eventsBySeverity,
        };
    }
}
exports.AuditRepository = AuditRepository;
