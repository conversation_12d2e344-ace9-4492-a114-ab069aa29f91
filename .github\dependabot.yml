version: 2
updates:
  - package-ecosystem: "npm" # Look for npm dependencies
    directory: "/backend" # Location of package.json
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    reviewers:
      - "TheAIgency/backend-team" # Replace with your team's slug or individual usernames
    labels:
      - "dependencies"
      - "security"
    target-branch: "develop" # Or your main development branch

  - package-ecosystem: "npm" # For frontend if applicable
    directory: "/frontend" # Adjust if your frontend is in a different directory
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    reviewers:
      - "TheAIgency/frontend-team"
    labels:
      - "dependencies"
      - "security"
    target-branch: "develop"

  - package-ecosystem: "docker" # For Dockerfile dependencies
    directory: "/" # Location of Dockerfile
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    reviewers:
      - "TheAIgency/devops-team"
    labels:
      - "dependencies"
      - "docker"
    target-branch: "develop"

  - package-ecosystem: "github-actions" # For GitHub Actions workflows
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    reviewers:
      - "TheAIgency/devops-team"
    labels:
      - "dependencies"
      - "github_actions"
    target-branch: "develop"