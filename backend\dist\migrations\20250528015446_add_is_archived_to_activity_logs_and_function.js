"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.alterTable('activitylogs', (table) => {
        table.boolean('is_archived').notNullable().defaultTo(false);
    });
}
async function down(knex) {
    await knex.schema.alterTable('activitylogs', (table) => {
        table.dropColumn('is_archived');
    });
}
