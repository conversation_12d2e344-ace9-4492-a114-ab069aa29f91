"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppwriteAdapter = void 0;
const node_appwrite_1 = require("node-appwrite");
const appwrite_1 = require("../../config/appwrite"); // Use appwriteService
class AppwriteAdapter {
    client;
    account;
    databases;
    storage;
    users; // For server-side user management
    constructor() {
        this.client = appwrite_1.appwriteService.getClient();
        this.account = appwrite_1.appwriteService.getAccount();
        this.databases = appwrite_1.appwriteService.getDatabases();
        this.storage = appwrite_1.appwriteService.getStorage();
        this.users = appwrite_1.appwriteService.getUsers(); // Get Users from appwriteService
    }
    // --- Authentication ---
    async signUp(email, password, metadata) {
        // Appwrite's create user method is for server-side. For client-side, use account.create
        // Assuming this is a server-side operation based on the context of backend/src/auth/auth.service.ts
        const user = await this.users.create('unique()', email, password, undefined, JSON.stringify(metadata));
        return user;
    }
    async signIn(email, password) {
        const session = await this.account.createEmailPasswordSession(email, password);
        return session;
    }
    async signOut() {
        await this.account.deleteSession('current');
    }
    async getCurrentUser() {
        try {
            const user = await this.account.get();
            return user;
        }
        catch (error) {
            if (error instanceof node_appwrite_1.AppwriteException && error.code === 401) {
                return null;
            }
            throw error;
        }
    }
    async updateUser(userId, data) {
        // Appwrite's update user method is for server-side. For client-side, use account.update
        // Assuming this is a server-side operation
        let updatedUser;
        if ('name' in data && typeof data.name === 'string') {
            updatedUser = await this.users.updateName(userId, data.name);
        }
        if ('email' in data && typeof data.email === 'string') {
            updatedUser = await this.users.updateEmail(userId, data.email);
        }
        if ('password' in data && typeof data.password === 'string') {
            updatedUser = await this.users.updatePassword(userId, data.password);
        }
        return updatedUser;
    }
    // --- CRUD Operations ---
    async createDocument(collectionId, data, documentId) {
        const doc = await this.databases.createDocument(process.env.APPWRITE_DATABASE_ID, // Assuming database ID is in env
        collectionId, documentId || 'unique()', data);
        return doc;
    }
    async getDocument(collectionId, documentId) {
        const doc = await this.databases.getDocument(process.env.APPWRITE_DATABASE_ID, collectionId, documentId);
        return doc;
    }
    async updateDocument(collectionId, documentId, data) {
        const updatedDoc = await this.databases.updateDocument(process.env.APPWRITE_DATABASE_ID, collectionId, documentId, data);
        return updatedDoc;
    }
    async deleteDocument(collectionId, documentId) {
        await this.databases.deleteDocument(process.env.APPWRITE_DATABASE_ID, collectionId, documentId);
    }
    async listDocuments(collectionId, queries) {
        const response = await this.databases.listDocuments(process.env.APPWRITE_DATABASE_ID, collectionId, queries);
        return response.documents;
    }
    // --- File Storage ---
    async uploadFile(bucketId, file, fileName) {
        const uploadedFile = await this.storage.createFile(bucketId, 'unique()', file // Cast to File
        );
        return uploadedFile;
    }
    async getFile(bucketId, fileId) {
        const fileBuffer = await this.storage.getFileDownload(bucketId, fileId);
        return new Blob([fileBuffer]); // Convert ArrayBuffer to Blob
    }
    async deleteFile(bucketId, fileId) {
        await this.storage.deleteFile(bucketId, fileId);
    }
    async listFiles(bucketId) {
        const response = await this.storage.listFiles(bucketId);
        return response.files;
    }
    // --- Additional methods used by AuthService (Supabase equivalents) ---
    async getUserByEmail(email) {
        // Appwrite does not have a direct "getUserByEmail" for server-side.
        // You would typically query your 'users' collection in a database
        // or list users and filter. For simplicity, let's assume a 'users' collection
        // where email is stored.
        const response = await this.databases.listDocuments(process.env.APPWRITE_DATABASE_ID, 'users', // Assuming a 'users' collection
        [`email=${email}`] // Appwrite query syntax
        );
        return response.documents.length > 0 ? response.documents[0] : null;
    }
    async createUser(userData) {
        // This maps to the signUp method for server-side user creation
        // Assuming userData contains email, password, and other metadata
        const { email, password, ...metadata } = userData;
        return this.signUp(email, password, metadata);
    }
    async createSession(userId, data) {
        // Appwrite sessions are typically created via login.
        // If this is for a specific server-side session creation,
        // you might need to use the Users service to create a JWT or handle it differently.
        // For now, let's assume it's part of a login flow handled by signIn.
        // If `data` contains a token, you might use account.createSession(token)
        // This method might need to be re-evaluated based on actual usage in AuthService.
        // For now, returning a mock session or throwing an error if not applicable.
        console.warn('createSession in AppwriteAdapter might need custom implementation based on AuthService usage.');
        return { session_id: 'mock_session_id_appwrite', userId, ...data };
    }
    async getUserById(userId) {
        try {
            const user = await this.users.get(userId);
            return user;
        }
        catch (error) {
            if (error instanceof node_appwrite_1.AppwriteException && error.code === 404) {
                return null;
            }
            throw error;
        }
    }
    async deleteSession(sessionId) {
        await this.account.deleteSession(sessionId);
    }
    async healthCheck() {
        try {
            // Appwrite client has a health check method, or you can check a service status
            await appwrite_1.appwriteService.healthCheck(); // Use the healthCheck from appwriteService
            return true;
        }
        catch (error) {
            console.error('Appwrite health check failed:', error);
            return false;
        }
    }
}
exports.AppwriteAdapter = AppwriteAdapter;
