import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mcp_access_controls', table => {
    table.foreign('paim_tier_id').references('paim_tier_id').inTable('PaimTiers').onDelete('CASCADE');
  });

  await knex.schema.alterTable('mcp_rate_limits', table => {
    table.foreign('paim_tier_id').references('paim_tier_id').inTable('PaimTiers').onDelete('CASCADE');
  });

  await knex.schema.alterTable('mcp_quotas', table => {
    table.foreign('paim_tier_id').references('paim_tier_id').inTable('PaimTiers').onDelete('CASCADE');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mcp_access_controls', table => {
    table.dropForeign('paim_tier_id');
  });

  await knex.schema.alterTable('mcp_rate_limits', table => {
    table.dropForeign('paim_tier_id');
  });

  await knex.schema.alterTable('mcp_quotas', table => {
    table.dropForeign('paim_tier_id');
  });
}