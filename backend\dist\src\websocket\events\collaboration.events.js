"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollaborationEvents = void 0;
const websocket_types_1 = require("../websocket.types");
class CollaborationEvents {
    wsService;
    constructor(wsService) {
        this.wsService = wsService;
        this.registerIncomingEventHandlers();
    }
    registerIncomingEventHandlers() {
        this.wsService.on(websocket_types_1.WebSocketEvent.CURSOR_UPDATE, (userId, payload) => {
            // Re-broadcast to all clients in the same workflow, or all if no specific workflow targeting
            this.emitCursorUpdate(payload);
        });
        this.wsService.on(websocket_types_1.WebSocketEvent.PRESENCE_UPDATE, (userId, payload) => {
            // Re-broadcast to all clients in the same workflow, or all if no specific workflow targeting
            this.emitPresenceUpdate(payload);
        });
        this.wsService.on(websocket_types_1.WebSocketEvent.EDIT_UPDATE, (userId, payload) => {
            // Re-broadcast to all clients in the same workflow, or all if no specific workflow targeting
            this.emitEditUpdate(payload);
        });
        // Add handlers for other incoming collaboration events as needed
    }
    emitWorkflowUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.COLLABORATION_UPDATE,
            payload,
        }));
    }
    emitCursorUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.CURSOR_UPDATE,
            payload,
        }));
    }
    emitPresenceUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.PRESENCE_UPDATE,
            payload,
        }));
    }
    emitEditUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.EDIT_UPDATE,
            payload,
        }));
    }
}
exports.CollaborationEvents = CollaborationEvents;
