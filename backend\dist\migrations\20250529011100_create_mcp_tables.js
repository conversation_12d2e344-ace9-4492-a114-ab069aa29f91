"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(knex) {
    await knex.schema.createTable('mcp_servers', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.string('name').notNullable();
        table.string('base_url').notNullable().unique();
        table.string('protocol_version').notNullable();
        table.enum('status', ['active', 'inactive', 'unhealthy']).notNullable().defaultTo('inactive');
        table.timestamp('last_heartbeat').defaultTo(knex.fn.now());
        table.jsonb('capabilities').defaultTo('[]');
        table.jsonb('metadata').defaultTo('{}');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    await knex.schema.createTable('mcp_capabilities', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
        table.enum('type', ['tool', 'resource', 'prompt_template']).notNullable();
        table.string('name').notNullable();
        table.text('description');
        table.jsonb('schema').defaultTo('{}');
        table.jsonb('metadata').defaultTo('{}');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        table.unique(['server_id', 'name', 'type']);
    });
    await knex.schema.createTable('mcp_request_logs', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
        table.uuid('capability_id').notNullable().references('id').inTable('mcp_capabilities').onDelete('CASCADE');
        table.jsonb('request_payload').notNullable();
        table.jsonb('response_payload').notNullable();
        table.enum('status', ['success', 'failure']).notNullable();
        table.text('error_message');
        table.integer('duration_ms').notNullable();
        table.timestamp('requested_at').defaultTo(knex.fn.now());
        table.index('server_id');
        table.index('capability_id');
        table.index('status');
        table.index('requested_at');
    });
    await knex.schema.createTable('mcp_metrics', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
        table.enum('metric_type', ['latency', 'error_rate', 'throughput']).notNullable();
        table.double('value').notNullable();
        table.timestamp('timestamp').defaultTo(knex.fn.now());
        table.jsonb('metadata').defaultTo('{}');
        table.index('server_id');
        table.index('metric_type');
        table.index('timestamp');
    });
    await knex.schema.createTable('mcp_configurations', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.string('key').notNullable().unique();
        table.text('value').notNullable();
        table.enum('type', ['string', 'number', 'boolean', 'json']).notNullable();
        table.text('description');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    await knex.schema.createTable('mcp_access_controls', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
        table.uuid('paim_tier_id').notNullable();
        table.enum('permission', ['read', 'write', 'execute', 'admin']).notNullable();
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        table.unique(['server_id', 'paim_tier_id']);
    });
    await knex.schema.createTable('mcp_rate_limits', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
        table.uuid('paim_tier_id');
        table.integer('limit').notNullable();
        table.enum('duration', ['minute', 'hour', 'day']).notNullable();
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        table.unique(['server_id', 'paim_tier_id']);
    });
    await knex.schema.createTable('mcp_quotas', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
        table.uuid('paim_tier_id');
        table.integer('quota').notNullable();
        table.integer('used').notNullable().defaultTo(0);
        table.enum('reset_interval', ['daily', 'weekly', 'monthly']).notNullable();
        table.timestamp('last_reset').defaultTo(knex.fn.now());
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        table.unique(['server_id', 'paim_tier_id']);
    });
    await knex.schema.createTable('mcp_notifications', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').notNullable().references('id').inTable('mcp_servers').onDelete('CASCADE');
        table.enum('type', ['server_status', 'capability_update', 'error', 'rate_limit_exceeded']).notNullable();
        table.text('message').notNullable();
        table.timestamp('timestamp').defaultTo(knex.fn.now());
        table.boolean('read').notNullable().defaultTo(false);
        table.jsonb('metadata').defaultTo('{}');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        table.index('server_id');
        table.index('type');
        table.index('read');
        table.index('timestamp');
    });
    await knex.schema.createTable('mcp_audit_logs', table => {
        table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
        table.uuid('server_id').references('id').inTable('mcp_servers').onDelete('SET NULL');
        table.uuid('user_id');
        table.string('action').notNullable();
        table.jsonb('details').defaultTo('{}');
        table.timestamp('timestamp').defaultTo(knex.fn.now());
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
        table.index('server_id');
        table.index('user_id');
        table.index('action');
        table.index('timestamp');
    });
}
async function down(knex) {
    await knex.schema.dropTableIfExists('mcp_audit_logs');
    await knex.schema.dropTableIfExists('mcp_notifications');
    await knex.schema.dropTableIfExists('mcp_quotas');
    await knex.schema.dropTableIfExists('mcp_rate_limits');
    await knex.schema.dropTableIfExists('mcp_access_controls');
    await knex.schema.dropTableIfExists('mcp_configurations');
    await knex.schema.dropTableIfExists('mcp_metrics');
    await knex.schema.dropTableIfExists('mcp_request_logs');
    await knex.schema.dropTableIfExists('mcp_capabilities');
    await knex.schema.dropTableIfExists('mcp_servers');
}
