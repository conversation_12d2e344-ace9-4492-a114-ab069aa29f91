openapi: 3.0.0
info:
  title: The AIgency & Workflow and Collaboration API
  version: 1.0.0
  description: API endpoints for workflow definition, execution, real-time collaboration, and task management.

servers:
  - url: https://api.theaigency.com/v1
    description: Production server
  - url: https://dev.theaigency.com/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Workflows
    description: Defining and managing automated workflows
  - name: Tasks
    description: Managing individual tasks within workflows or independently
  - name: Collaboration
    description: Real-time features for user and agent collaboration
  - name: Cross-Tenant Communication
    description: Facilitating communication between different tenants/PAIM instances

paths:
  /workflows:
    get:
      summary: Get all defined workflows
      operationId: getAllWorkflows
      tags:
        - Workflows
      security:
        - bearerAuth: []
      parameters:
        - $ref: '../paim_management.yaml#/components/parameters/PageParam'
        - $ref: '../paim_management.yaml#/components/parameters/SizeParam'
        - $ref: '../paim_management.yaml#/components/parameters/SortParam'
        - in: query
          name: status
          schema:
            type: string
            enum: [active, inactive, draft]
          description: Filter workflows by status.
      responses:
        '200':
          description: List of workflows
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Workflow'
                  pagination:
                    $ref: '../paim_management.yaml#/components/schemas/PaginationMetadata'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Create a new workflow definition
      operationId: createWorkflow
      tags:
        - Workflows
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWorkflowRequest'
      responses:
        '201':
          description: Workflow created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Workflow'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /workflows/{workflowId}:
    get:
      summary: Get a specific workflow definition by ID
      operationId: getWorkflowById
      tags:
        - Workflows
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: workflowId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the workflow.
      responses:
        '200':
          description: Workflow details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Workflow'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    put:
      summary: Update an existing workflow definition
      operationId: updateWorkflow
      tags:
        - Workflows
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: workflowId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the workflow to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWorkflowRequest'
      responses:
        '200':
          description: Workflow updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Workflow'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    delete:
      summary: Delete a workflow definition
      operationId: deleteWorkflow
      tags:
        - Workflows
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: workflowId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the workflow to delete.
      responses:
        '204':
          description: Workflow deleted successfully
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /tasks:
    get:
      summary: Get all tasks
      operationId: getAllTasks
      tags:
        - Tasks
      security:
        - bearerAuth: []
      parameters:
        - $ref: '../paim_management.yaml#/components/parameters/PageParam'
        - $ref: '../paim_management.yaml#/components/parameters/SizeParam'
        - $ref: '../paim_management.yaml#/components/parameters/SortParam'
        - in: query
          name: status
          schema:
            type: string
            enum: [pending, in_progress, completed, cancelled]
          description: Filter tasks by status.
        - in: query
          name: assignedTo
          schema:
            type: string
            format: uuid
          description: Filter tasks by assigned user or agent ID.
      responses:
        '200':
          description: List of tasks
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Task'
                  pagination:
                    $ref: '../paim_management.yaml#/components/schemas/PaginationMetadata'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    post:
      summary: Create a new task
      operationId: createTask
      tags:
        - Tasks
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTaskRequest'
      responses:
        '201':
          description: Task created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /tasks/{taskId}:
    get:
      summary: Get a specific task by ID
      operationId: getTaskById
      tags:
        - Tasks
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: taskId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the task.
      responses:
        '200':
          description: Task details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    put:
      summary: Update an existing task
      operationId: updateTask
      tags:
        - Tasks
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: taskId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the task to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTaskRequest'
      responses:
        '200':
          description: Task updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'
    delete:
      summary: Delete a task
      operationId: deleteTask
      tags:
        - Tasks
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: taskId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the task to delete.
      responses:
        '204':
          description: Task deleted successfully
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /collaboration/sessions:
    post:
      summary: Start a new collaboration session
      operationId: startCollaborationSession
      tags:
        - Collaboration
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartCollaborationSessionRequest'
      responses:
        '201':
          description: Collaboration session started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollaborationSession'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /collaboration/sessions/{sessionId}/join:
    post:
      summary: Join an existing collaboration session
      operationId: joinCollaborationSession
      tags:
        - Collaboration
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: sessionId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the collaboration session to join.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: string
                  format: uuid
                  description: The ID of the user joining the session.
      responses:
        '200':
          description: Successfully joined session
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollaborationSession'
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /collaboration/sessions/{sessionId}/leave:
    post:
      summary: Leave a collaboration session
      operationId: leaveCollaborationSession
      tags:
        - Collaboration
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: sessionId
          schema:
            type: string
            format: uuid
          required: true
          description: The ID of the collaboration session to leave.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: string
                  format: uuid
                  description: The ID of the user leaving the session.
      responses:
        '204':
          description: Successfully left session
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

  /cross-tenant-communication/messages:
    post:
      summary: Send a message to another tenant/PAIM instance
      operationId: sendCrossTenantMessage
      tags:
        - Cross-Tenant Communication
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CrossTenantMessageRequest'
      responses:
        '200':
          description: Message sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  messageId:
                    type: string
                    format: uuid
                    example: a1b2c3d4-e5f6-7890-1234-567890abcdef
                  status:
                    type: string
                    example: sent
        '400':
          $ref: '../auth.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../auth.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../auth.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../auth.yaml#/components/responses/NotFound'
        '500':
          $ref: '../auth.yaml#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Workflow:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: w1o2r3k4-f5l6-7890-1234-567890abcdef
        name:
          type: string
          example: Customer Onboarding Workflow
        description:
          type: string
          nullable: true
          example: Automated steps for onboarding new customers.
        ownerId:
          type: string
          format: uuid
          description: User ID of the workflow owner.
        paimInstanceId:
          type: string
          format: uuid
          description: PAIM instance this workflow belongs to.
        status:
          type: string
          enum: [active, inactive, draft]
          example: active
        definition:
          type: object
          description: JSON representation of the workflow steps and logic.
          example:
            steps:
              - name: "Initial Contact"
                type: "agent_task"
                agentPersona: "Sales"
              - name: "Data Collection"
                type: "user_input"
                formId: "customer_data_form"
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    CreateWorkflowRequest:
      type: object
      required:
        - name
        - ownerId
        - paimInstanceId
        - definition
      properties:
        name:
          type: string
          example: New Marketing Campaign Workflow
        description:
          type: string
          nullable: true
          example: Workflow for launching a new marketing campaign.
        ownerId:
          type: string
          format: uuid
        paimInstanceId:
          type: string
          format: uuid
        definition:
          type: object
          description: JSON representation of the workflow steps and logic.

    UpdateWorkflowRequest:
      type: object
      properties:
        name:
          type: string
          example: Updated Customer Onboarding Workflow
        description:
          type: string
          nullable: true
          example: Revised automated steps for onboarding new customers.
        status:
          type: string
          enum: [active, inactive, draft]
          example: inactive
        definition:
          type: object
          description: Updated JSON representation of the workflow steps and logic.

    Task:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: t1a2s3k4-i5d6-7890-1234-567890abcdef
        title:
          type: string
          example: Review Q4 Sales Report
        description:
          type: string
          nullable: true
          example: Review the sales performance for the fourth quarter.
        status:
          type: string
          enum: [pending, in_progress, completed, cancelled]
          example: pending
        priority:
          type: string
          enum: [low, medium, high, critical]
          example: high
        assignedToId:
          type: string
          format: uuid
          nullable: true
          description: ID of the user or agent assigned to the task.
        assignedToType:
          type: string
          enum: [user, agent]
          nullable: true
        dueDate:
          type: string
          format: date-time
          nullable: true
          example: '2023-11-15T17:00:00Z'
        workflowId:
          type: string
          format: uuid
          nullable: true
          description: ID of the workflow this task belongs to.
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'

    CreateTaskRequest:
      type: object
      required:
        - title
        - status
        - priority
      properties:
        title:
          type: string
          example: Prepare Monthly Budget Report
        description:
          type: string
          nullable: true
          example: Compile and analyze financial data for the monthly budget.
        status:
          type: string
          enum: [pending, in_progress, completed, cancelled]
          example: pending
        priority:
          type: string
          enum: [low, medium, high, critical]
          example: medium
        assignedToId:
          type: string
          format: uuid
          nullable: true
        assignedToType:
          type: string
          enum: [user, agent]
          nullable: true
        dueDate:
          type: string
          format: date-time
          nullable: true
        workflowId:
          type: string
          format: uuid
          nullable: true

    UpdateTaskRequest:
      type: object
      properties:
        title:
          type: string
          example: Finalize Q4 Sales Report
        description:
          type: string
          nullable: true
          example: Add executive summary and recommendations.
        status:
          type: string
          enum: [pending, in_progress, completed, cancelled]
          example: completed
        priority:
          type: string
          enum: [low, medium, high, critical]
          example: critical
        assignedToId:
          type: string
          format: uuid
          nullable: true
        assignedToType:
          type: string
          enum: [user, agent]
          nullable: true
        dueDate:
          type: string
          format: date-time
          nullable: true

    CollaborationSession:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: c1o2l3l4-a5b6-7890-1234-567890abcdef
        name:
          type: string
          example: Project Alpha Brainstorm
        description:
          type: string
          nullable: true
          example: Real-time brainstorming session for Project Alpha.
        ownerId:
          type: string
          format: uuid
          description: User ID of the session owner.
        paimInstanceId:
          type: string
          format: uuid
          description: PAIM instance this session belongs to.
        participants:
          type: array
          items:
            type: object
            properties:
              userId:
                type: string
                format: uuid
              joinedAt:
                type: string
                format: date-time
          description: List of users currently in the session.
        status:
          type: string
          enum: [active, ended]
          example: active
        createdAt:
          type: string
          format: date-time
          example: '2023-10-27T10:00:00Z'
        endedAt:
          type: string
          format: date-time
          nullable: true
          example: '2023-10-27T11:00:00Z'

    StartCollaborationSessionRequest:
      type: object
      required:
        - name
        - ownerId
        - paimInstanceId
      properties:
        name:
          type: string
          example: New Feature Design Review
        description:
          type: string
          nullable: true
          example: Review and discuss design for upcoming feature.
        ownerId:
          type: string
          format: uuid
        paimInstanceId:
          type: string
          format: uuid
        initialParticipants:
          type: array
          items:
            type: string
            format: uuid
          description: Optional list of user IDs to invite initially.

    CrossTenantMessageRequest:
      type: object
      required:
        - senderPaimInstanceId
        - recipientPaimInstanceId
        - messageContent
      properties:
        senderPaimInstanceId:
          type: string
          format: uuid
          description: The ID of the sending PAIM instance.
        recipientPaimInstanceId:
          type: string
          format: uuid
          description: The ID of the receiving PAIM instance.
        messageContent:
          type: string
          description: The content of the message.
          example: "Urgent: Please review the shared document."
        messageType:
          type: string
          enum: [text, notification, data_request]
          default: text
          description: Type of message being sent.
        relatedTaskId:
          type: string
          format: uuid
          nullable: true
          description: Optional ID of a related task.