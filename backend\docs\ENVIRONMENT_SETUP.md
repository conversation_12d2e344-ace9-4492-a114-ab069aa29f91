# Environment Setup Guide

## Appwrite Configuration

1. Copy `.env.example` to `.env.development`
2. Update the Appwrite configuration values:
   - `APPWRITE_ENDPOINT`: Your Appwrite server endpoint
   - `APPWRITE_PROJECT_ID`: Your Appwrite project ID
   - `APPWRITE_DATABASE_ID`: Your Appwrite database ID
   - `APPWRITE_API_KEY`: Your Appwrite API key (optional)

## Development Setup

For development, you can use placeholder values or set up a real Appwrite project.

### Option 1: Placeholder Values (for compilation testing)
Use the values in `.env.development` as-is for basic compilation testing.

### Option 2: Real Appwrite Project
1. Create an account at https://cloud.appwrite.io
2. Create a new project
3. Create a database
4. Generate an API key
5. Update your `.env.development` file with real values