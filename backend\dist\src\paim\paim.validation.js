"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllPaimInstancesQuerySchema = exports.paimHierarchyUpdateSchema = exports.crossPaimCommunicationRequestSchema = exports.paimTierChangeRequestSchema = exports.updatePaimInstanceSchema = exports.createPaimInstanceSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const paim_types_1 = require("./paim.types");
const db_1 = require("../types/db");
// Joi schema for CreatePaimInstanceRequest
exports.createPaimInstanceSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).required(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    ownerId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    tier: joi_1.default.string().valid(...Object.values(db_1.PaimTierEnum)).required(),
});
// Joi schema for UpdatePaimInstanceRequest
exports.updatePaimInstanceSchema = joi_1.default.object({
    name: joi_1.default.string().trim().min(3).max(100).optional(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    status: joi_1.default.string().valid(...Object.values(paim_types_1.PaimInstanceStatus)).optional(),
    tier: joi_1.default.string().valid(...Object.values(db_1.PaimTierEnum)).optional(),
});
// Joi schema for PaimTierChangeRequest
exports.paimTierChangeRequestSchema = joi_1.default.object({
    requestedTier: joi_1.default.string().valid(...Object.values(db_1.PaimTierEnum)).required(),
    reason: joi_1.default.string().trim().min(10).max(500).required(),
});
// Joi schema for CrossPaimCommunicationRequest
exports.crossPaimCommunicationRequestSchema = joi_1.default.object({
    targetPaimInstanceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    message: joi_1.default.string().trim().min(1).max(1000).required(),
    messageType: joi_1.default.string().valid(...Object.values(paim_types_1.CrossPaimCommunicationMessageType)).optional(),
});
// Joi schema for PaimHierarchyUpdate
exports.paimHierarchyUpdateSchema = joi_1.default.object({
    hierarchyTree: joi_1.default.object().required(), // This can be more detailed if PaimHierarchyNode schema is defined
});
// Joi schema for query parameters for getAllPaimInstances
exports.getAllPaimInstancesQuerySchema = joi_1.default.object({
    status: joi_1.default.string().valid(...Object.values(paim_types_1.PaimInstanceStatus)).optional(),
    page: joi_1.default.number().integer().min(1).default(1),
    size: joi_1.default.number().integer().min(1).max(100).default(10),
    sort: joi_1.default.string().optional(), // e.g., 'createdAt,desc'
});
