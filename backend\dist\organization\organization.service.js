"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationService = void 0;
const audit_service_1 = require("../audit/audit.service");
const audit_types_1 = require("../audit/audit.types");
class OrganizationService {
    organizationRepository;
    auditTrailService;
    emailService; // Declare EmailService
    constructor(organizationRepository, emailService) {
        this.organizationRepository = organizationRepository;
        this.auditTrailService = new audit_service_1.AuditTrailService();
        this.emailService = emailService; // Initialize EmailService
    }
    async updateAllowedSeats(orgId, seatsAllowed) {
        if (seatsAllowed < 0) {
            throw new Error('Seats allowed cannot be negative.');
        }
        const currentOrg = await this.organizationRepository.getOrganizationById(orgId);
        if (!currentOrg) {
            throw new Error('Organization not found.');
        }
        if (seatsAllowed < currentOrg.org_seats_used) {
            throw new Error('Cannot set allowed seats less than currently used seats.');
        }
        const updatedOrg = await this.organizationRepository.updateOrganizationSeats(orgId, seatsAllowed);
        await this.auditTrailService.logEvent({
            tenantId: orgId,
            category: audit_types_1.AuditEventCategory.SYSTEM_OPERATION, // Or a new ORGANIZATION category
            operationType: 'ORG_SEATS_UPDATED',
            description: `Organization ${orgId} allowed seats updated from ${currentOrg.org_seats_allowed} to ${seatsAllowed}.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { orgId, oldSeats: currentOrg.org_seats_allowed, newSeats: seatsAllowed },
        });
        return updatedOrg;
    }
    async getOrganizationSeats(orgId) {
        return this.organizationRepository.getOrganizationSeats(orgId);
    }
    async assignSeat(orgId, userId) {
        const currentOrg = await this.organizationRepository.getOrganizationById(orgId);
        if (!currentOrg) {
            throw new Error('Organization not found.');
        }
        if (currentOrg.org_seats_used >= currentOrg.org_seats_allowed) {
            await this.auditTrailService.logEvent({
                tenantId: orgId,
                userId: userId,
                category: audit_types_1.AuditEventCategory.SYSTEM_OPERATION,
                operationType: 'SEAT_ASSIGNMENT_FAILED',
                description: `Seat assignment failed for user ${userId} in organization ${orgId}: Seat limit reached.`,
                severity: audit_types_1.AuditEventSeverity.MEDIUM,
                timestamp: new Date(),
                metadata: { orgId, userId, seatsUsed: currentOrg.org_seats_used, seatsAllowed: currentOrg.org_seats_allowed, reason: 'Seat limit reached' },
            });
            throw new Error('Seat limit reached for this organization.');
        }
        const updatedOrg = await this.organizationRepository.updateOrganizationSeatsUsed(orgId, currentOrg.org_seats_used + 1);
        await this.auditTrailService.logEvent({
            tenantId: orgId,
            userId: userId,
            category: audit_types_1.AuditEventCategory.SYSTEM_OPERATION,
            operationType: 'SEAT_ASSIGNED',
            description: `Seat assigned to user ${userId} in organization ${orgId}. Current seats used: ${updatedOrg?.org_seats_used}/${updatedOrg?.org_seats_allowed}.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { orgId, userId, seatsUsed: updatedOrg?.org_seats_used, seatsAllowed: updatedOrg?.org_seats_allowed },
        });
        return updatedOrg;
    }
    async revokeSeat(orgId, userId) {
        const currentOrg = await this.organizationRepository.getOrganizationById(orgId);
        if (!currentOrg) {
            throw new Error('Organization not found.');
        }
        if (currentOrg.org_seats_used <= 0) {
            await this.auditTrailService.logEvent({
                tenantId: orgId,
                userId: userId,
                category: audit_types_1.AuditEventCategory.SYSTEM_OPERATION,
                operationType: 'SEAT_REVOCATION_FAILED',
                description: `Seat revocation failed for user ${userId} in organization ${orgId}: No seats currently used.`,
                severity: audit_types_1.AuditEventSeverity.MEDIUM,
                timestamp: new Date(),
                metadata: { orgId, userId, seatsUsed: currentOrg.org_seats_used, seatsAllowed: currentOrg.org_seats_allowed, reason: 'No seats to revoke' },
            });
            throw new Error('No seats currently used to revoke.');
        }
        const updatedOrg = await this.organizationRepository.updateOrganizationSeatsUsed(orgId, currentOrg.org_seats_used - 1);
        await this.auditTrailService.logEvent({
            tenantId: orgId,
            userId: userId,
            category: audit_types_1.AuditEventCategory.SYSTEM_OPERATION,
            operationType: 'SEAT_REVOKED',
            description: `Seat revoked from user ${userId} in organization ${orgId}. Current seats used: ${updatedOrg?.org_seats_used}/${updatedOrg?.org_seats_allowed}.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { orgId, userId, seatsUsed: updatedOrg?.org_seats_used, seatsAllowed: updatedOrg?.org_seats_allowed },
        });
        return updatedOrg;
    }
    async applyFounderClubDiscount(orgId) {
        const organization = await this.organizationRepository.getOrganizationById(orgId);
        if (!organization) {
            throw new Error('Organization not found.');
        }
        if (organization.founder_club_flag && organization.promo_start_date) {
            // Discount already applied
            return organization;
        }
        // Assuming eligibility check (e.g., first 25 organizations) is done elsewhere or during org creation
        // For now, we'll just apply if founder_club_flag is true but promo_start_date is not set.
        if (organization.founder_club_flag && !organization.promo_start_date) {
            const promoStartDate = new Date();
            const promoEndDate = new Date();
            promoEndDate.setMonth(promoEndDate.getMonth() + 12); // 12 months from now
            const updatedOrganization = await this.organizationRepository.updateFounderClubStatus(orgId, true, promoStartDate, promoEndDate);
            if (updatedOrganization) {
                await this.emailService.sendEmail('<EMAIL>', // Placeholder for admin email or organization contact
                `Founder's Club Discount Applied for ${updatedOrganization.tenant_name}`, `Dear Admin,

The Founder's Club discount has been successfully applied to organization: ${updatedOrganization.tenant_name}.

Discount Start Date: ${promoStartDate.toDateString()}
Discount End Date: ${promoEndDate.toDateString()}

Please ensure the Stripe coupon 'FND75' is applied to their billing.

Best regards,
The AIgency Team`);
                await this.auditTrailService.logEvent({
                    tenantId: orgId,
                    category: audit_types_1.AuditEventCategory.BILLING,
                    operationType: 'FOUNDER_CLUB_DISCOUNT_APPLIED',
                    description: `Founder's Club discount applied to organization ${orgId}.`,
                    severity: audit_types_1.AuditEventSeverity.INFO,
                    timestamp: new Date(),
                    metadata: { orgId, promoStartDate, promoEndDate },
                });
            }
            return updatedOrganization;
        }
        return organization; // No changes if not eligible or already applied
    }
}
exports.OrganizationService = OrganizationService;
