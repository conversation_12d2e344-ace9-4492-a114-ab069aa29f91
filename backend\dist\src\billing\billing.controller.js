"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingController = void 0;
const express_1 = require("express");
const asyncHandler_1 = require("../utils/asyncHandler");
const validation_1 = require("../utils/validation");
const joi_1 = __importDefault(require("joi"));
const authentication_1 = require("../middleware/authentication"); // Import new authenticate middleware
const authorization_1 = require("../middleware/authorization"); // Import authorization middleware
const permissions_1 = require("../auth/permissions"); // Import permissions
const errors_1 = require("../utils/errors"); // Import CustomError
// Define custom interfaces to explicitly include 'body' and 'status'/'json'
// Define schema for incoming webhook data
const PowerOpsUsageSchema = joi_1.default.object({
    org_id: joi_1.default.string().guid().required(),
    powerops_used: joi_1.default.number().positive().required(),
});
class BillingController {
    router;
    billingService;
    constructor(billingService) {
        this.billingService = billingService;
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.post('/usage', authentication_1.authenticate, // Apply JWT validation
        (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), // Add authorization check
        (0, validation_1.validate)(PowerOpsUsageSchema), // Validate incoming data
        (0, asyncHandler_1.asyncHandler)(this.trackUsageWebhook.bind(this)));
    }
    async trackUsageWebhook(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { org_id, powerops_used } = req.body;
        // Assuming org_id from webhook body is the tenantId for tracking usage
        const record = await this.billingService.trackPowerOpsUsage(req.user, org_id, powerops_used);
        res.status(200).json({ message: 'PowerOps usage tracked successfully', record });
    }
}
exports.BillingController = BillingController;
