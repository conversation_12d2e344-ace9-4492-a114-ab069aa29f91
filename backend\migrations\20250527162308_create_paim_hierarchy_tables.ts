import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`
    -- Table: PaimTiers
    -- Defines the four-tier PAIM system (System Admin, Company Admin, Power User, Personal).
    -- This table serves as a lookup for the 'paim_tier' column in the Users table.
    CREATE TABLE PaimTiers (
        paim_tier_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tier_name VARCHAR(50) NOT NULL UNIQUE, -- e.g., 'System Admin', 'Company Admin', 'Power User', 'Personal'
        description TEXT,
        hierarchy_level INT NOT NULL UNIQUE, -- e.g., 1 for System Admin, 2 for Company Admin, etc.
        parent_tier_id UUID REFERENCES PaimTiers(paim_tier_id), -- Self-referencing for hierarchical relationships
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Indexes for performance optimization
    CREATE INDEX idx_paimtiers_tier_name ON PaimTiers(tier_name);
    CREATE INDEX idx_paimtiers_hierarchy_level ON PaimTiers(hierarchy_level);
    CREATE INDEX idx_paimtiers_parent_tier_id ON PaimTiers(parent_tier_id);

    -- Insert initial PAIM tiers
    INSERT INTO PaimTiers (tier_name, description, hierarchy_level) VALUES
    ('System Admin', 'Highest level, full system access across all tenants.', 1),
    ('Company Admin', 'Manages users and settings within a specific company/tenant.', 2),
    ('Power User', 'Advanced user with extended capabilities within their tenant.', 3),
    ('Personal', 'Standard user with basic access within their tenant.', 4);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw(`
    DROP TABLE IF EXISTS PaimTiers;
  `);
}