#!/usr/bin/env node

/**
 * Quality Control Test Script for AI Agent Framework
 * 
 * This script performs comprehensive testing of the agent framework implementation
 * to ensure all documented features are working correctly.
 */

const axios = require('axios');
const chalk = require('chalk');

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const FRAMEWORK_BASE_URL = `${BASE_URL}/agents/framework`;

// Test configuration
const TEST_CONFIG = {
  timeout: 30000,
  retries: 3,
  delay: 1000
};

class QCTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      skipped: 0,
      tests: []
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const colors = {
      info: chalk.blue,
      success: chalk.green,
      error: chalk.red,
      warning: chalk.yellow
    };
    
    console.log(`[${timestamp}] ${colors[type](message)}`);
  }

  async test(name, testFn, options = {}) {
    const startTime = Date.now();
    this.log(`Running test: ${name}`, 'info');

    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED', duration });
      this.log(`✓ ${name} (${duration}ms)`, 'success');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', duration, error: error.message });
      this.log(`✗ ${name} (${duration}ms): ${error.message}`, 'error');
      
      if (options.critical) {
        throw error;
      }
    }
  }

  async skip(name, reason) {
    this.results.skipped++;
    this.results.tests.push({ name, status: 'SKIPPED', reason });
    this.log(`⊘ ${name} - ${reason}`, 'warning');
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async makeRequest(method, url, data = null, headers = {}) {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      timeout: TEST_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    return axios(config);
  }

  async runAllTests() {
    this.log('Starting AI Agent Framework QC Tests', 'info');
    this.log(`Base URL: ${BASE_URL}`, 'info');
    this.log('=' * 60, 'info');

    try {
      // Phase 1: Framework Status Tests
      await this.testFrameworkStatus();
      
      // Phase 2: Agent Tests
      await this.testAgents();
      
      // Phase 3: Task Execution Tests
      await this.testTaskExecution();
      
      // Phase 4: Integration Tests
      await this.testIntegration();
      
      // Phase 5: Performance Tests
      await this.testPerformance();

    } catch (error) {
      this.log(`Critical test failure: ${error.message}`, 'error');
    }

    this.printSummary();
  }

  async testFrameworkStatus() {
    this.log('\n=== Phase 1: Framework Status Tests ===', 'info');

    await this.test('Framework Status Endpoint', async () => {
      const response = await this.makeRequest('GET', '/agents/framework/status');
      
      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      const { data } = response.data;
      if (!data.initialized) {
        throw new Error('Framework is not initialized');
      }

      if (!Array.isArray(data.agents)) {
        throw new Error('Agents list is not an array');
      }

      this.log(`Framework initialized with ${data.agents.length} agents`, 'info');
    }, { critical: true });

    await this.test('Framework Agents List', async () => {
      const response = await this.makeRequest('GET', '/agents/framework/agents');
      
      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      const { data } = response.data;
      if (!Array.isArray(data)) {
        throw new Error('Agents data is not an array');
      }

      const expectedAgents = ['CL-API', 'R1-Logic', 'DevOps'];
      const agentNames = data.map(agent => agent.name);
      
      for (const expectedAgent of expectedAgents) {
        if (!agentNames.includes(expectedAgent)) {
          throw new Error(`Expected agent '${expectedAgent}' not found`);
        }
      }

      this.log(`Found all expected agents: ${agentNames.join(', ')}`, 'info');
    });
  }

  async testAgents() {
    this.log('\n=== Phase 2: Individual Agent Tests ===', 'info');

    const agents = ['CL-API', 'R1-Logic', 'DevOps'];

    for (const agentName of agents) {
      await this.test(`${agentName} Agent Details`, async () => {
        const response = await this.makeRequest('GET', `/agents/framework/agents/${agentName}`);
        
        if (response.status !== 200) {
          throw new Error(`Expected status 200, got ${response.status}`);
        }

        const { data } = response.data;
        if (data.name !== agentName) {
          throw new Error(`Expected agent name '${agentName}', got '${data.name}'`);
        }

        if (!data.active) {
          throw new Error(`Agent '${agentName}' is not active`);
        }

        if (!Array.isArray(data.capabilities)) {
          throw new Error(`Agent '${agentName}' capabilities is not an array`);
        }

        this.log(`${agentName} has ${data.capabilities.length} capabilities`, 'info');
      });
    }
  }

  async testTaskExecution() {
    this.log('\n=== Phase 3: Task Execution Tests ===', 'info');

    await this.test('CL-API Agent - API Request Task', async () => {
      const taskData = {
        taskType: 'api_request',
        payload: {
          method: 'GET',
          endpoint: '/test',
          headers: { 'Content-Type': 'application/json' }
        }
      };

      const response = await this.makeRequest('POST', '/agents/framework/agents/CL-API/execute', taskData);
      
      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      const { data } = response.data;
      if (!data.success) {
        throw new Error(`Task execution failed: ${data.error}`);
      }

      this.log('CL-API agent successfully processed API request', 'info');
    });

    await this.test('R1-Logic Agent - Business Logic Task', async () => {
      const taskData = {
        taskType: 'process_business_logic',
        payload: {
          type: 'decision_making',
          context: {
            userId: 'test-user-123',
            tenantId: 'test-tenant-456',
            paimTier: 'enterprise'
          },
          data: { action: 'create_resource' }
        }
      };

      const response = await this.makeRequest('POST', '/agents/framework/agents/R1-Logic/execute', taskData);
      
      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      const { data } = response.data;
      if (!data.success) {
        throw new Error(`Task execution failed: ${data.error}`);
      }

      this.log('R1-Logic agent successfully processed business logic', 'info');
    });

    await this.test('DevOps Agent - Health Check Task', async () => {
      const taskData = {
        taskType: 'health_check',
        payload: {}
      };

      const response = await this.makeRequest('POST', '/agents/framework/agents/DevOps/execute', taskData);
      
      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      const { data } = response.data;
      if (!data.success) {
        throw new Error(`Task execution failed: ${data.error}`);
      }

      this.log('DevOps agent successfully performed health check', 'info');
    });
  }

  async testIntegration() {
    this.log('\n=== Phase 4: Integration Tests ===', 'info');

    await this.test('Framework Task Execution', async () => {
      const taskData = {
        taskType: 'api_request',
        payload: {
          method: 'POST',
          endpoint: '/integration-test',
          body: { test: 'data' }
        }
      };

      const response = await this.makeRequest('POST', '/agents/framework/execute', taskData);
      
      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      const { data } = response.data;
      if (!data.success) {
        throw new Error(`Framework task execution failed: ${data.error}`);
      }

      this.log('Framework successfully routed and executed task', 'info');
    });

    await this.test('Agent Metrics Collection', async () => {
      const response = await this.makeRequest('GET', '/agents/framework/agents/CL-API');
      
      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      const { data } = response.data;
      if (!data.metrics) {
        throw new Error('Agent metrics not found');
      }

      const metrics = data.metrics;
      if (typeof metrics.tasksExecuted !== 'number') {
        throw new Error('Invalid tasksExecuted metric');
      }

      if (typeof metrics.successRate !== 'number') {
        throw new Error('Invalid successRate metric');
      }

      this.log(`Agent metrics: ${metrics.tasksExecuted} tasks, ${metrics.successRate}% success rate`, 'info');
    });
  }

  async testPerformance() {
    this.log('\n=== Phase 5: Performance Tests ===', 'info');

    await this.test('Concurrent Task Execution', async () => {
      const concurrentTasks = 5;
      const taskPromises = [];

      for (let i = 0; i < concurrentTasks; i++) {
        const taskData = {
          taskType: 'api_request',
          payload: {
            method: 'GET',
            endpoint: `/performance-test-${i}`,
            headers: { 'X-Test-Id': `perf-${i}` }
          }
        };

        taskPromises.push(
          this.makeRequest('POST', '/agents/framework/agents/CL-API/execute', taskData)
        );
      }

      const startTime = Date.now();
      const responses = await Promise.all(taskPromises);
      const duration = Date.now() - startTime;

      for (const response of responses) {
        if (response.status !== 200) {
          throw new Error(`Concurrent task failed with status ${response.status}`);
        }
      }

      this.log(`Executed ${concurrentTasks} concurrent tasks in ${duration}ms`, 'info');
    });

    await this.test('Response Time Check', async () => {
      const startTime = Date.now();
      const response = await this.makeRequest('GET', '/agents/framework/status');
      const responseTime = Date.now() - startTime;

      if (response.status !== 200) {
        throw new Error(`Expected status 200, got ${response.status}`);
      }

      if (responseTime > 5000) {
        throw new Error(`Response time too slow: ${responseTime}ms`);
      }

      this.log(`Framework status response time: ${responseTime}ms`, 'info');
    });
  }

  printSummary() {
    this.log('\n' + '=' * 60, 'info');
    this.log('QC TEST SUMMARY', 'info');
    this.log('=' * 60, 'info');
    
    this.log(`Total Tests: ${this.results.tests.length}`, 'info');
    this.log(`Passed: ${this.results.passed}`, 'success');
    this.log(`Failed: ${this.results.failed}`, this.results.failed > 0 ? 'error' : 'info');
    this.log(`Skipped: ${this.results.skipped}`, this.results.skipped > 0 ? 'warning' : 'info');
    
    const successRate = ((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1);
    this.log(`Success Rate: ${successRate}%`, successRate >= 90 ? 'success' : 'error');

    if (this.results.failed > 0) {
      this.log('\nFailed Tests:', 'error');
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          this.log(`  - ${test.name}: ${test.error}`, 'error');
        });
    }

    this.log('\n' + '=' * 60, 'info');
    
    if (this.results.failed === 0) {
      this.log('🎉 ALL TESTS PASSED! Agent Framework is ready for production.', 'success');
      process.exit(0);
    } else {
      this.log('❌ Some tests failed. Please review and fix issues before deployment.', 'error');
      process.exit(1);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new QCTester();
  tester.runAllTests().catch(error => {
    console.error('QC Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = QCTester;
