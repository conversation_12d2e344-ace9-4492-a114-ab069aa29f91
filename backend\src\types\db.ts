export interface Tenant {
  tenant_id: string; // UUID
  tenant_name: string;
  schema_name: string;
  status: 'active' | 'inactive' | 'suspended';
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export interface User {
  user_id: string; // UUID
  tenant_id: string; // UUID
  email: string;
  password_hash: string;
  first_name: string | null;
  last_name: string | null;
  paim_tier: PaimTierEnum;
  cultural_profile: object | null; // JSONB
  status: 'active' | 'inactive' | 'suspended';
  last_login: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  roles?: Role[]; // Add roles property
}

export interface Role {
  role_id: string; // UUID
  tenant_id: string; // UUID
  role_name: string;
  description: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface UserRole {
  user_id: string; // UUID
  role_id: string; // UUID
  assigned_at: Date;
}

export interface Permission {
  permission_id: string; // UUID
  permission_name: string;
  description: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface RolePermission {
  role_id: string; // UUID
  permission_id: string; // UUID
  granted_at: Date;
}

export enum PaimTierEnum {
  Basic = 'Basic',
  Professional = 'Professional',
  Enterprise = 'Enterprise',
  Custom = 'Custom',
  CompanyAdmin = 'CompanyAdmin',
}

export interface PaimTier {
  paim_tier_id: string; // UUID
  tier_name: PaimTierEnum;
  description: string | null;
  hierarchy_level: number;
  parent_tier_id: string | null; // UUID
  created_at: Date;
  updated_at: Date;
}
export interface AgentDefinition {
  agent_id: string; // UUID
  tenant_id: string; // UUID
  agent_name: string;
  description: string | null;
  persona_details: object | null; // JSONB
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export interface AgentVersion {
  agent_version_id: string; // UUID
  agent_id: string; // UUID
  version_number: string;
  configuration: object | null; // JSONB
  embedding: number[] | null; // VECTOR(1536)
  is_active: boolean;
  created_at: Date;
}

export interface Workflow {
  workflow_id: string; // UUID
  tenant_id: string; // UUID
  workflow_name: string;
  description: string | null;
  definition: object; // JSONB
  cultural_requirements: object | null; // JSONB
  created_by: string | null; // UUID
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export interface WorkflowExecution {
  execution_id: string; // UUID
  workflow_id: string; // UUID
  tenant_id: string; // UUID
  initiated_by: string | null; // UUID
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  start_time: Date;
  end_time: Date | null;
  duration_ms: number | null;
  output_data: object | null; // JSONB
  performance_metrics: object | null; // JSONB
  error_details: object | null; // JSONB
}

export interface AgentTask {
  task_id: string; // UUID
  execution_id: string; // UUID
  agent_version_id: string; // UUID
  task_name: string | null;
  input_data: object | null; // JSONB
  output_data: object | null; // JSONB
  status: 'pending' | 'running' | 'completed' | 'failed';
  start_time: Date;
  end_time: Date | null;
  duration_ms: number | null;
  error_details: object | null; // JSONB
}

export interface CrossPaimCommunicationLog {
  log_id: string; // UUID
  tenant_id: string; // UUID
  sender_paim_id: string | null; // UUID
  receiver_paim_id: string | null; // UUID
  message_type: string;
  message_payload: object | null; // JSONB
  communication_status: string | null;
  timestamp: Date;
}
export interface CulturalProfile {
  profile_id: string; // UUID
  tenant_id: string; // UUID
  profile_name: string;
  language_code: string;
  dialect_code: string | null;
  cultural_norms: object | null; // JSONB
  timezone: string | null;
  currency: string | null;
  date_format: string | null;
  time_format: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface UserCulturalProfile {
  user_id: string; // UUID
  profile_id: string; // UUID
  assigned_at: Date;
}

export interface LanguagePattern {
  pattern_id: string; // UUID
  language_code: string;
  dialect_code: string | null;
  pattern_type: string;
  pattern_regex: string | null;
  cultural_context_id: string | null; // UUID
  description: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface CulturalContext {
  context_id: string; // UUID
  context_name: string;
  description: string | null;
  cultural_values: object | null; // JSONB
  geographic_region: string | null;
  demographic_info: object | null; // JSONB
  created_at: Date;
  updated_at: Date;
}

export interface ArabicBrainLearningAggregation {
  aggregation_id: string; // UUID
  tenant_id: string; // UUID
  source_data_id: string | null; // UUID
  content_hash: string | null;
  processed_text: string | null;
  language_code: string;
  dialect_detected: string | null;
  sentiment_score: number | null;
  cultural_relevance_score: number | null;
  embeddings: number[] | null; // VECTOR(1536)
  metadata: object | null; // JSONB
  aggregated_at: Date;
}

export interface LocalizationTracking {
  localization_id: string; // UUID
  tenant_id: string; // UUID
  content_type: string;
  content_id: string; // UUID
  source_language: string;
  target_language: string;
  target_dialect: string | null;
  adaptation_details: object | null; // JSONB
  status: 'pending' | 'in_progress' | 'completed' | 'reviewed';
  localized_by: string | null; // UUID
  localized_at: Date;
}
export interface PowerOpsUsage {
  usage_id: string; // UUID
  tenant_id: string; // UUID
  user_id: string; // UUID
  feature_name: string;
  usage_amount: number;
  unit_of_measure: string;
  cost_per_unit: number | null;
  total_cost: number | null;
  usage_timestamp: Date;
  metadata: object | null; // JSONB
}

export interface BillingRecord {
  billing_id: string; // UUID
  tenant_id: string; // UUID
  billing_period_start: Date;
  billing_period_end: Date;
  total_amount: number;
  currency: string;
  status: 'pending' | 'billed' | 'paid' | 'failed';
  invoice_url: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface UserXP {
  user_id: string; // UUID
  tenant_id: string; // UUID
  current_xp: number;
  level: number;
  last_xp_gain_at: Date;
}

export interface Badge {
  badge_id: string; // UUID
  badge_name: string;
  description: string | null;
  image_url: string | null;
  xp_reward: number | null;
  criteria: object | null; // JSONB
  created_at: Date;
}

export interface UserBadge {
  user_badge_id: string; // UUID
  user_id: string; // UUID
  badge_id: string; // UUID
  earned_at: Date;
}

export interface Achievement {
  achievement_id: string; // UUID
  achievement_name: string;
  description: string | null;
  xp_reward: number | null;
  badge_reward_id: string | null; // UUID
  criteria: object | null; // JSONB
  created_at: Date;
}

export interface UserAchievement {
  user_achievement_id: string; // UUID
  user_id: string; // UUID
  achievement_id: string; // UUID
  unlocked_at: Date;
}

export interface Streak {
  streak_id: string; // UUID
  user_id: string; // UUID
  streak_type: string;
  current_length: number;
  longest_length: number;
  last_activity_at: Date | null;
  started_at: Date;
}

export interface BudgetControl {
  budget_id: string; // UUID
  tenant_id: string; // UUID
  user_id: string | null; // UUID
  budget_limit: number;
  currency: string;
  period_type: 'monthly' | 'quarterly' | 'yearly';
  period_start_date: Date;
  period_end_date: Date;
  current_spend: number;
  alert_threshold: number | null;
  created_at: Date;
  updated_at: Date;
}
export interface ActivityLog {
  log_id: string; // UUID
  tenant_id: string; // UUID
  user_id: string | null; // UUID
  activity_type: string;
  activity_details: object | null; // JSONB
  cultural_context: object | null; // JSONB
  ip_address: string | null; // INET
  user_agent: string | null;
  timestamp: Date;
}

export interface SecurityEvent {
  event_id: string; // UUID
  tenant_id: string; // UUID
  user_id: string | null; // UUID
  event_type: string;
  event_details: object | null; // JSONB
  severity: 'low' | 'medium' | 'high' | 'critical';
  ip_address: string | null; // INET
  timestamp: Date;
}

export interface ComplianceRecord {
  record_id: string; // UUID
  tenant_id: string; // UUID
  compliance_standard: string;
  record_type: string;
  record_details: object | null; // JSONB
  document_url: string | null;
  created_at: Date;
  updated_at: Date;
  effective_date: Date | null;
  review_date: Date | null;
}

export interface DataAccessLog {
  access_id: string; // UUID
  tenant_id: string; // UUID
  user_id: string; // UUID
  data_entity_type: string;
  data_entity_id: string; // UUID
  access_type: 'read' | 'write' | 'delete';
  accessed_fields: object | null; // JSONB
  ip_address: string | null; // INET
  user_agent: string | null;
  timestamp: Date;
}
export interface SystemMetric {
  metric_id: string; // UUID
  tenant_id: string | null; // UUID
  metric_name: string;
  metric_value: number;
  unit: string | null;
  timestamp: Date;
  metadata: object | null; // JSONB
}

export interface ServiceHealth {
  health_id: string; // UUID
  service_name: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  last_check_time: Date;
  details: object | null; // JSONB
}

export interface Error {
  error_id: string; // UUID
  tenant_id: string | null; // UUID
  service_name: string | null;
  error_code: string | null;
  error_message: string;
  stack_trace: string | null;
  error_severity: 'info' | 'warning' | 'error' | 'critical';
  occurred_at: Date;
  resolved_at: Date | null;
  resolved_by: string | null; // UUID
  metadata: object | null; // JSONB
}

export interface AutoHealingAction {
  action_id: string; // UUID
  tenant_id: string | null; // UUID
  triggered_by_error_id: string | null; // UUID
  action_type: string;
  action_details: object | null; // JSONB
  status: 'initiated' | 'in_progress' | 'completed' | 'failed';
  action_time: Date;
}

export interface Escalation {
  escalation_id: string; // UUID
  tenant_id: string | null; // UUID
  source_metric_id: string | null; // UUID
  source_error_id: string | null; // UUID
  escalation_level: number;
  status: 'open' | 'acknowledged' | 'resolved';
  triggered_at: Date;
  resolved_at: Date | null;
  resolved_by: string | null; // UUID
  notes: string | null;
}