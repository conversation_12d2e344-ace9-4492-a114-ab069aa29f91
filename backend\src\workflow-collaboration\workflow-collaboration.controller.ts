import * as express from 'express';
import { WorkflowCollaborationService } from './workflow-collaboration.service';
import {
  CreateWorkflowRequest,
  UpdateWorkflowRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  StartCollaborationSessionRequest,
  CrossTenantMessageRequest,
  CreateNotificationRequest,
  ShareWorkflowRequest,
  DelegateTaskRequest,
  CreateWorkspaceRequest,
  UpdateWorkspaceRequest,
  CreateTeamRequest,
  UpdateTeamRequest,
} from './workflow-collaboration.types';
import { asyncHandler } from '../utils/asyncHandler';
import { validate } from '../utils/validation'; // Import validate middleware
import { requireParam, getParam } from '../utils/type-guards';
import {
  createWorkflowSchema,
  updateWorkflowSchema,
  createTaskSchema,
  updateTaskSchema,
  startCollaborationSessionSchema,
  userIdBodySchema,
  crossTenantMessageSchema,
  createNotificationSchema,
  shareWorkflowSchema,
  delegateTaskSchema,
  createWorkspaceSchema,
  updateWorkspaceSchema,
  createTeamSchema,
  updateTeamSchema,
  getAllWorkflowsQuerySchema,
  getAllTasksQuerySchema,
  getAllNotificationsQuerySchema,
  getAllWorkspacesQuerySchema,
  getAllTeamsQuerySchema,
} from './workflow-collaboration.validation'; // Import Joi schemas

export class WorkflowCollaborationController {
  public router: express.Router; // Add public router property

  constructor(private workflowCollaborationService: WorkflowCollaborationService) {
    this.router = express.Router(); // Initialize router
    this.initializeRoutes(); // Initialize routes
  }

  private initializeRoutes() {
    // Workflows
    this.router.get('/', validate(getAllWorkflowsQuerySchema), this.getAllWorkflows);
    this.router.post('/', validate(createWorkflowSchema), this.createWorkflow);
    this.router.get('/:workflowId', this.getWorkflowById);
    this.router.put('/:workflowId', validate(updateWorkflowSchema), this.updateWorkflow);
    this.router.delete('/:workflowId', this.deleteWorkflow);

    // Tasks
    this.router.get('/tasks', validate(getAllTasksQuerySchema), this.getAllTasks);
    this.router.post('/tasks', validate(createTaskSchema), this.createTask);
    this.router.get('/tasks/:taskId', this.getTaskById);
    this.router.put('/tasks/:taskId', validate(updateTaskSchema), this.updateTask);
    this.router.delete('/tasks/:taskId', this.deleteTask);

    // Collaboration Sessions
    this.router.post('/collaboration/sessions', validate(startCollaborationSessionSchema), this.startCollaborationSession);
    this.router.post('/collaboration/sessions/:sessionId/join', validate(userIdBodySchema), this.joinCollaborationSession);
    this.router.post('/collaboration/sessions/:sessionId/leave', validate(userIdBodySchema), this.leaveCollaborationSession);

    // Cross-Tenant Communication
    this.router.post('/cross-tenant-communication/messages', validate(crossTenantMessageSchema), this.sendCrossTenantMessage);

    // Notifications
    this.router.get('/notifications', validate(getAllNotificationsQuerySchema), this.getAllNotifications);
    this.router.post('/notifications/:notificationId/read', this.markNotificationAsRead); // No body validation needed for mark as read

    // Workflow Sharing
    this.router.post('/workflow-sharing/share', validate(shareWorkflowSchema), this.shareWorkflow);
    this.router.delete('/workflow-sharing/share/:workflowId/:permissionId', this.deleteWorkflowShare);

    // Task Delegation
    this.router.post('/task-delegation/delegate/:taskId', validate(delegateTaskSchema), this.delegateTask);

    // Collaborative Workspace
    this.router.get('/collaborative-workspaces', validate(getAllWorkspacesQuerySchema), this.getAllWorkspaces);
    this.router.post('/collaborative-workspaces', validate(createWorkspaceSchema), this.createWorkspace);
    this.router.get('/collaborative-workspaces/:workspaceId', this.getWorkspaceById);
    this.router.put('/collaborative-workspaces/:workspaceId', validate(updateWorkspaceSchema), this.updateWorkspace);
    this.router.delete('/collaborative-workspaces/:workspaceId', this.deleteWorkspace);

    // Team Coordination
    this.router.get('/team-coordination/teams', validate(getAllTeamsQuerySchema), this.getAllTeams);
    this.router.post('/team-coordination/teams', validate(createTeamSchema), this.createTeam);
    this.router.get('/team-coordination/teams/:teamId', this.getTeamById);
    this.router.put('/team-coordination/teams/:teamId', validate(updateTeamSchema), this.updateTeam);
    this.router.delete('/team-coordination/teams/:teamId', this.deleteTeam);
  }

  // Workflows
  getAllWorkflows = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { status, page, size, sort } = req.query;
    // Validation is now handled by Joi middleware
    const workflows = await this.workflowCollaborationService.getAllWorkflows(
      getParam(status as string | undefined),
      parseInt(getParam(page as string | undefined, '1')),
      parseInt(getParam(size as string | undefined, '10')),
      getParam(sort as string | undefined),
    );
    res.status(200).json(workflows);
  });

  createWorkflow = asyncHandler(async (req: express.Request, res: express.Response) => {
    const workflowData: CreateWorkflowRequest = req.body;
    const newWorkflow = await this.workflowCollaborationService.createWorkflow(workflowData);
    res.status(201).json(newWorkflow);
  });

  getWorkflowById = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { workflowId } = req.params;
    const workflow = await this.workflowCollaborationService.getWorkflowById(requireParam(workflowId, 'workflowId'));
    res.status(200).json(workflow);
  });

  updateWorkflow = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { workflowId } = req.params;
    const workflowData: UpdateWorkflowRequest = req.body;
    const updatedWorkflow = await this.workflowCollaborationService.updateWorkflow(requireParam(workflowId, 'workflowId'), workflowData);
    res.status(200).json(updatedWorkflow);
  });

  deleteWorkflow = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { workflowId } = req.params;
    await this.workflowCollaborationService.deleteWorkflow(requireParam(workflowId, 'workflowId'));
    res.status(204).send();
  });

  // Tasks
  getAllTasks = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { status, assignedTo, page, size, sort } = req.query;
    // Validation is now handled by Joi middleware
    const tasks = await this.workflowCollaborationService.getAllTasks(
      getParam(status as string | undefined),
      getParam(assignedTo as string | undefined),
      parseInt(getParam(page as string | undefined, '1')),
      parseInt(getParam(size as string | undefined, '10')),
      getParam(sort as string | undefined),
    );
    res.status(200).json(tasks);
  });

  createTask = asyncHandler(async (req: express.Request, res: express.Response) => {
    const taskData: CreateTaskRequest = req.body;
    const newTask = await this.workflowCollaborationService.createTask(taskData);
    res.status(201).json(newTask);
  });

  getTaskById = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { taskId } = req.params;
    const task = await this.workflowCollaborationService.getTaskById(requireParam(taskId, 'taskId'));
    res.status(200).json(task);
  });

  updateTask = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { taskId } = req.params;
    const taskData: UpdateTaskRequest = req.body;
    const updatedTask = await this.workflowCollaborationService.updateTask(requireParam(taskId, 'taskId'), taskData);
    res.status(200).json(updatedTask);
  });

  deleteTask = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { taskId } = req.params;
    await this.workflowCollaborationService.deleteTask(requireParam(taskId, 'taskId'));
    res.status(204).send();
  });

  // Collaboration Sessions
  startCollaborationSession = asyncHandler(async (req: express.Request, res: express.Response) => {
    const sessionData: StartCollaborationSessionRequest = req.body;
    const newSession = await this.workflowCollaborationService.startCollaborationSession(sessionData);
    res.status(201).json(newSession);
  });

  joinCollaborationSession = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { sessionId } = req.params;
    const { userId } = req.body;
    const updatedSession = await this.workflowCollaborationService.joinCollaborationSession(requireParam(sessionId, 'sessionId'), requireParam(userId, 'userId'));
    res.status(200).json(updatedSession);
  });

  leaveCollaborationSession = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { sessionId } = req.params;
    const { userId } = req.body;
    await this.workflowCollaborationService.leaveCollaborationSession(requireParam(sessionId, 'sessionId'), requireParam(userId, 'userId'));
    res.status(204).send();
  });

  // Cross-Tenant Communication
  sendCrossTenantMessage = asyncHandler(async (req: express.Request, res: express.Response) => {
    const messageData: CrossTenantMessageRequest = req.body;
    const result = await this.workflowCollaborationService.sendCrossTenantMessage(messageData);
    res.status(200).json(result);
  });

  // Notifications
  getAllNotifications = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { userId, read, page, size, sort } = req.query;
    // Validation is now handled by Joi middleware
    const notifications = await this.workflowCollaborationService.getAllNotifications(
      getParam(userId as string | undefined),
      getParam(read as string | undefined),
      parseInt(getParam(page as string | undefined, '1')),
      parseInt(getParam(size as string | undefined, '10')),
      getParam(sort as string | undefined),
    );
    res.status(200).json(notifications);
  });

  markNotificationAsRead = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { notificationId } = req.params;
    await this.workflowCollaborationService.markNotificationAsRead(requireParam(notificationId, 'notificationId'));
    res.status(204).send();
  });

  // Workflow Sharing
  shareWorkflow = asyncHandler(async (req: express.Request, res: express.Response) => {
    const shareData: ShareWorkflowRequest = req.body;
    const result = await this.workflowCollaborationService.shareWorkflow(shareData);
    res.status(200).json(result);
  });

  deleteWorkflowShare = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { workflowId, permissionId } = req.params;
    await this.workflowCollaborationService.deleteWorkflowShare(requireParam(workflowId, 'workflowId'), requireParam(permissionId, 'permissionId'));
    res.status(204).send();
  });

  // Task Delegation
  delegateTask = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { taskId } = req.params;
    const delegateData: DelegateTaskRequest = req.body;
    const result = await this.workflowCollaborationService.delegateTask(requireParam(taskId, 'taskId'), delegateData);
    res.status(200).json(result);
  });

  // Collaborative Workspace
  getAllWorkspaces = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { ownerId, paimInstanceId, page, size, sort } = req.query;
    // Validation is now handled by Joi middleware
    const workspaces = await this.workflowCollaborationService.getAllWorkspaces(
      getParam(ownerId as string | undefined),
      getParam(paimInstanceId as string | undefined),
      parseInt(getParam(page as string | undefined, '1')),
      parseInt(getParam(size as string | undefined, '10')),
      getParam(sort as string | undefined),
    );
    res.status(200).json(workspaces);
  });

  createWorkspace = asyncHandler(async (req: express.Request, res: express.Response) => {
    const workspaceData: CreateWorkspaceRequest = req.body;
    const newWorkspace = await this.workflowCollaborationService.createWorkspace(workspaceData);
    res.status(201).json(newWorkspace);
  });

  getWorkspaceById = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { workspaceId } = req.params;
    const workspace = await this.workflowCollaborationService.getWorkspaceById(requireParam(workspaceId, 'workspaceId'));
    res.status(200).json(workspace);
  });

  updateWorkspace = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { workspaceId } = req.params;
    const workspaceData: UpdateWorkspaceRequest = req.body;
    const updatedWorkspace = await this.workflowCollaborationService.updateWorkspace(requireParam(workspaceId, 'workspaceId'), workspaceData);
    res.status(200).json(updatedWorkspace);
  });

  deleteWorkspace = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { workspaceId } = req.params;
    await this.workflowCollaborationService.deleteWorkspace(requireParam(workspaceId, 'workspaceId'));
    res.status(204).send();
  });

  // Team Coordination
  getAllTeams = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { paimInstanceId, page, size, sort } = req.query;
    // Validation is now handled by Joi middleware
    const teams = await this.workflowCollaborationService.getAllTeams(
      getParam(paimInstanceId as string | undefined),
      parseInt(getParam(page as string | undefined, '1')),
      parseInt(getParam(size as string | undefined, '10')),
      getParam(sort as string | undefined),
    );
    res.status(200).json(teams);
  });

  createTeam = asyncHandler(async (req: express.Request, res: express.Response) => {
    const teamData: CreateTeamRequest = req.body;
    const newTeam = await this.workflowCollaborationService.createTeam(teamData);
    res.status(201).json(newTeam);
  });

  getTeamById = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { teamId } = req.params;
    const team = await this.workflowCollaborationService.getTeamById(requireParam(teamId, 'teamId'));
    res.status(200).json(team);
  });

  updateTeam = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { teamId } = req.params;
    const teamData: UpdateTeamRequest = req.body;
    const updatedTeam = await this.workflowCollaborationService.updateTeam(requireParam(teamId, 'teamId'), teamData);
    res.status(200).json(updatedTeam);
  });

  deleteTeam = asyncHandler(async (req: express.Request, res: express.Response) => {
    const { teamId } = req.params;
    await this.workflowCollaborationService.deleteTeam(requireParam(teamId, 'teamId'));
    res.status(204).send();
  });
}