import { Request, Response, NextFunction } from 'express';
import { JwtPayload } from './auth.types';
import { PaimTierEnum } from '../types/db'; // Import PaimTierEnum from db types
import { AppwriteException } from 'node-appwrite'; // Import AppwriteException
import { AppwriteAdapter } from '../database/adapters/appwrite-adapter'; // Import AppwriteAdapter
import logger from '../config/logger'; // Import logger
import { AuthenticationError, AuthorizationError } from '../utils/errors'; // Import custom error classes

/**
 * Middleware to authenticate requests using JWT.
 * Attaches decoded user payload and tenantId to the request object.
 */
const appwriteAdapter = new AppwriteAdapter();

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = (req as any).headers.authorization;
  const ipAddress = req.ip;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    logger.warn('Authentication failed: No token provided or invalid format.', { ipAddress });
    return next(new AuthenticationError('No token provided or invalid token format.'));
  }

  const token = authHeader.split(' ')[1];

  try {
    const user = await appwriteAdapter.getCurrentUser();

    if (!user) {
      logger.warn('Authentication failed: Invalid or expired token.', { ipAddress, token });
      return next(new AuthenticationError('Invalid or expired token.'));
    }

    // Map Appwrite user to JwtPayload
    (req as any).user = {
      userId: user.$id,
      email: user.email || '',
      tenantId: user.prefs?.tenant_id || '', // Assuming tenant_id is in user.prefs
      paimTier: user.prefs?.paim_tier || '', // Assuming paim_tier is in user.prefs
      roles: user.prefs?.roles || [], // Assuming roles are in user.prefs
      iat: user.accessedAt ? Math.floor(new Date(user.accessedAt).getTime() / 1000) : 0, // Use accessedAt for iat
      exp: user.expire ? Math.floor(new Date(user.expire).getTime() / 1000) : 0, // Use expire for exp
    };
    (req as any).tenantId = (req as any).user.tenantId;

    logger.info(`User authenticated: ${user.email}`, { userId: user.$id, tenantId: (req as any).tenantId, ipAddress });
    next();
  } catch (error: unknown) {
    if (error instanceof AppwriteException && (error.code === 401 || error.code === 403)) {
      logger.warn('Appwrite authentication error: Invalid or expired token.', { ipAddress, token, error: error.message });
      return next(new AuthenticationError('Invalid or expired token.'));
    }
    logger.error('Authentication failed due to server error.', { ipAddress, token, error });
    return next(new AuthenticationError('Authentication failed due to server error.'));
  }
};

/**
 * Middleware to enforce PAIM tier-based authorization.
 * Requires the authenticate middleware to be run first.
 */
export const authorizePaimTier = (requiredTier: PaimTierEnum) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const ipAddress = req.ip;
    const userId = (req as any).user?.userId;

    if (!(req as any).user) {
      logger.warn('Authorization failed (PAIM Tier): Authentication required.', { ipAddress });
      return next(new AuthenticationError('Authentication required for authorization.'));
    }

    const userTier = (req as any).user.paimTier;
    const paimTierHierarchy: Record<PaimTierEnum, number> = {
      [PaimTierEnum.Basic]: 4,
      [PaimTierEnum.Professional]: 3,
      [PaimTierEnum.Enterprise]: 2,
      [PaimTierEnum.Custom]: 1, // Custom is typically the highest or most flexible
      [PaimTierEnum.CompanyAdmin]: 0, // CompanyAdmin is the highest tier
    };

    // Check if user's tier level is numerically less than or equal to the required tier level
    // (lower number means higher tier in this hierarchy)
    if (paimTierHierarchy[userTier as PaimTierEnum] > paimTierHierarchy[requiredTier]) {
      logger.warn(`Authorization failed (PAIM Tier): Insufficient tier. User: ${userId}, Tier: ${userTier}, Required: ${requiredTier}`, { ipAddress });
      return next(new AuthorizationError('Forbidden: Insufficient PAIM tier.'));
    }

    logger.info(`PAIM Tier authorized: User ${userId} with tier ${userTier} for required tier ${requiredTier}`, { ipAddress });
    next();
  };
};

/**
 * Middleware to enforce role-based authorization.
 * Requires the authenticate middleware to be run first.
 */
export const authorizeRoles = (requiredRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const ipAddress = req.ip;
    const userId = (req as any).user?.userId;

    if (!(req as any).user) {
      logger.warn('Authorization failed (Roles): Authentication required.', { ipAddress });
      return next(new AuthenticationError('Authentication required for authorization.'));
    }

    const userRoles = (req as any).user.roles;
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      logger.warn(`Authorization failed (Roles): Insufficient roles. User: ${userId}, Roles: ${userRoles}, Required: ${requiredRoles}`, { ipAddress });
      return next(new AuthorizationError('Forbidden: Insufficient roles.'));
    }

    logger.info(`Roles authorized: User ${userId} with roles ${userRoles} for required roles ${requiredRoles}`, { ipAddress });
    next();
  };
};

/**
 * Middleware to enforce tenant context for multi-tenant isolation.
 * Ensures that a user can only access resources within their assigned tenant.
 * Requires the authenticate middleware to be run first.
 */
export const enforceTenantContext = (req: Request, res: Response, next: NextFunction) => {
  const ipAddress = req.ip;
  const userId = (req as any).user?.userId;
  const userTenantId = (req as any).user?.tenantId;

  if (!(req as any).user || !userTenantId) {
    logger.warn('Tenant context enforcement failed: Authentication and tenant context required.', { ipAddress });
    return next(new AuthenticationError('Authentication and tenant context required.'));
  }

  const requestedTenantId = (req as any).params.tenantId || (req as any).query.tenantId;

  // Custom tier can bypass tenant context for cross-tenant operations (assuming Custom is highest/admin-like)
  if ((req as any).user.paimTier === PaimTierEnum.Custom) {
    logger.info(`Tenant context bypassed for Custom tier user: ${userId}`, { ipAddress });
    return next();
  }

  if (requestedTenantId && requestedTenantId !== userTenantId) {
    logger.warn(`Tenant context enforcement failed: Access to other tenant data not allowed. User: ${userId}, User Tenant: ${userTenantId}, Requested Tenant: ${requestedTenantId}`, { ipAddress });
    return next(new AuthorizationError('Forbidden: Access to other tenant data is not allowed.'));
  }

  logger.info(`Tenant context enforced: User ${userId} in tenant ${userTenantId}`, { ipAddress });
  next();
};