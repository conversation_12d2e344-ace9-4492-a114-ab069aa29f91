export enum EntityType {
  User = 'user',
  PaimInstance = 'paim_instance',
  Organization = 'organization',
  Team = 'team',
}

export enum CostCategory {
  Compute = 'compute',
  Storage = 'storage',
  Bandwidth = 'bandwidth',
  AIModel = 'ai_model',
  Other = 'other',
}

export interface PowerOpsUsage {
  entityId: string;
  entityType: EntityType;
  totalUsageUnits: number;
  estimatedCost: number;
}

export interface LogPowerOpsUsageRequest {
  entityId: string;
  entityType: EntityType;
  usageUnits: number;
  costCategory: CostCategory;
  description?: string;
  timestamp: string; // ISO 8601 date string
}

export interface PowerOpsUsageEntry {
  id: string;
  entityId: string;
  entityType: EntityType;
  usageUnits: number;
  costCategory: CostCategory;
  estimatedCost: number;
  description?: string;
  timestamp: string; // ISO 8601 date string
}

export interface Xp {
  entityId: string;
  entityType: EntityType;
  currentXp: number;
  level: number;
  lastAwardedAt?: string; // ISO 8601 date string
}

export interface AwardXpRequest {
  entityId: string; // User ID or Paim Instance ID
  entityType: EntityType; // Type of entity (User or PaimInstance)
  org_id: string;
  agent_id?: string; // Optional, as XP can be for org or agent
  powerops: number;
  reason: string;
  metadata?: Record<string, any>;
}

export interface XpEvent {
  id?: string;
  org_id: string;
  agent_id?: string;
  powerops: number;
  xp_gained: number;
  reason: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  awardedAt: string; // ISO 8601 date string
}

export interface AwardBadgeRequest {
  entityId: string;
  entityType: EntityType;
  badgeId: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  unlockedAt: string; // ISO 8601 date string
  progress?: number;
  target?: number;
}

export interface GrantAchievementRequest {
  entityId: string;
  entityType: EntityType;
  achievementId: string;
}

export interface Streak {
  id: string;
  entityId: string;
  entityType: EntityType;
  name: string;
  currentStreak: number;
  longestStreak: number;
  lastAchievedAt: string; // ISO 8601 date string
}

export interface Budget {
  id: string;
  entityId: string;
  entityType: EntityType;
  monthlyLimit: number;
  currentSpend: number;
  currency: string;
  alertThreshold?: number; // Percentage
  createdAt: string; // ISO 8601 date string
  updatedAt: string; // ISO 8601 date string
}

export interface CreateBudgetRequest {
  entityId: string;
  entityType: EntityType;
  monthlyLimit: number;
  currency: string;
  alertThreshold?: number;
}

export interface UpdateBudgetRequest {
  monthlyLimit?: number;
  alertThreshold?: number;
}

export interface Invoice {
  id: string;
  entityId: string;
  entityType: EntityType;
  billingPeriodStart: string; // ISO 8601 date string
  billingPeriodEnd: string; // ISO 8601 date string
  totalAmount: number;
  currency: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  issueDate: string; // ISO 8601 date string
  dueDate: string; // ISO 8601 date string
  paymentMethodId?: string;
  lineItems: InvoiceLineItem[];
}

export interface InvoiceLineItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface CreateInvoiceRequest {
  entityId: string;
  entityType: EntityType;
  billingPeriodStart: string;
  billingPeriodEnd: string;
  lineItems: InvoiceLineItem[];
  paymentMethodId?: string;
}

export interface Payment {
  id: string;
  invoiceId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  transactionId: string;
  status: 'success' | 'failed' | 'pending';
  timestamp: string; // ISO 8601 date string
}

export interface ProcessPaymentRequest {
  invoiceId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  transactionDetails: any; // Specific details depend on payment gateway
  entityId: string; // Add entityId
  entityType: EntityType; // Add entityType
}

export interface LeaderboardEntry {
  entityId: string;
  entityType: EntityType;
  rank: number;
  score: number; // XP or other metric
  displayName: string;
}

export interface CostOptimizationRecommendation {
  id: string;
  entityId: string;
  entityType: EntityType;
  recommendation: string;
  potentialSavings: number;
  currency: string;
  status: 'pending' | 'applied' | 'dismissed';
  createdAt: string; // ISO 8601 date string
}

export interface ResourceUsageLimit {
  id: string;
  entityId: string;
  entityType: EntityType;
  resourceType: string;
  limit: number;
  unit: string;
  currentUsage: number;
  createdAt: string; // ISO 8601 date string
  updatedAt: string; // ISO 8601 date string
}

export interface SetResourceUsageLimitRequest {
  entityId: string;
  entityType: EntityType;
  resourceType: string;
  limit: number;
  unit: string;
}

export interface Notification {
  id: string;
  entityId: string;
  entityType: EntityType;
  type: 'budget_alert' | 'achievement_unlocked' | 'invoice_due' | 'payment_confirmation' | 'system_message';
  message: string;
  read: boolean;
  timestamp: string; // ISO 8601 date string
}

export interface CreateNotificationRequest {
  entityId: string;
  entityType: EntityType;
  type: 'budget_alert' | 'achievement_unlocked' | 'invoice_due' | 'payment_confirmation' | 'system_message';
  message: string;
}