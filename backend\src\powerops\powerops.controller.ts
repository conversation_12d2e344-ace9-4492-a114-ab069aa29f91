import { Request, Response, Router, NextFunction } from 'express';
import { PowerOpsService } from './powerops.service';
import { asyncHandler, AsyncRequestHandler } from '../utils/asyncHandler';
import { NotificationService } from '../notifications/notification.service'; // Import NotificationService
import { validate } from '../utils/validation'; // Import validate middleware
import { CustomError } from '../utils/errors';
import rateLimit from 'express-rate-limit'; // Import rateLimit
import { requireParam, getParam } from '../utils/type-guards';
import { authorize } from '../middleware/authorization'; // Import authorization middleware
import { PERMISSIONS } from '../auth/permissions'; // Import permissions
import { JwtPayload } from '../auth/auth.types'; // Import JwtPayload
import {
  LogPowerOpsUsageRequest,
  AwardXpRequest,
  AwardBadgeRequest,
  GrantAchievementRequest,
  CreateBudgetRequest,
  UpdateBudgetRequest,
  CreateInvoiceRequest,
  ProcessPaymentRequest,
  SetResourceUsageLimitRequest,
  CreateNotificationRequest,
  EntityType,
} from './powerops.types';
import {
  logPowerOpsUsageSchema,
  awardXpSchema,
  awardBadgeSchema,
  grantAchievementSchema,
  createBudgetSchema,
  updateBudgetSchema,
  createInvoiceSchema,
  processPaymentSchema,
  setResourceUsageLimitSchema,
  createNotificationSchema,
  entityIdEntityTypeQuerySchema,
  getPowerOpsUsageQuerySchema,
  getLeaderboardQuerySchema,
} from './powerops.validation'; // Import Joi schemas


// Rate limiting for XP endpoints to prevent farming
const xpLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // Limit each IP to 5 requests per minute
  message: 'Too many XP requests from this IP, please try again after a minute.',
  keyGenerator: (req: Request) => {
    // Use a combination of IP and IP and user ID (if authenticated) for more granular limiting
    const requestWithUser = req as Request & { user?: JwtPayload; ip?: string };
    return (requestWithUser.ip || 'unknown') + (requestWithUser.user ? `-${requestWithUser.user.userId}` : '');
  },
});

export class PowerOpsController {
  public router: Router;
  private powerOpsService: PowerOpsService;

  constructor(notificationService: NotificationService) { // Accept NotificationService
    this.powerOpsService = new PowerOpsService(undefined, undefined, notificationService); // Pass notificationService
    this.router = Router();
    this.initializeRoutes();
  }

  // PowerOps Usage & Cost Management
  private initializeRoutes() { // Add this method
    this.router.post('/usage', authorize([PERMISSIONS.POWER_OPS_CREATE]), validate(logPowerOpsUsageSchema), asyncHandler(this.logPowerOpsUsage.bind(this) as AsyncRequestHandler));
    this.router.get('/usage/:entityType/:entityId', authorize([PERMISSIONS.POWER_OPS_READ]), validate(getPowerOpsUsageQuerySchema), asyncHandler(this.getPowerOpsUsage.bind(this) as AsyncRequestHandler));
    this.router.post('/xp/add', authorize([PERMISSIONS.POWER_OPS_MANAGE_ALL]), xpLimiter, validate(awardXpSchema), asyncHandler(this.awardXp.bind(this) as AsyncRequestHandler)); // Apply XP rate limit
    this.router.get('/xp/user/:id', authorize([PERMISSIONS.POWER_OPS_READ]), asyncHandler(this.getXpByUser.bind(this) as AsyncRequestHandler)); // New endpoint for user XP
    this.router.get('/xp/org/:id', authorize([PERMISSIONS.POWER_OPS_READ]), asyncHandler(this.getXpByOrg.bind(this) as AsyncRequestHandler)); // New endpoint for organization XP
    this.router.post('/badges/award', authorize([PERMISSIONS.POWER_OPS_MANAGE_ALL]), validate(awardBadgeSchema), asyncHandler(this.awardBadge.bind(this) as AsyncRequestHandler)); // Changed to /badges/award
    this.router.get('/badges', authorize([PERMISSIONS.POWER_OPS_READ]), asyncHandler(this.getAllBadges.bind(this) as AsyncRequestHandler)); // New endpoint for all badges
    this.router.get('/badges/user/:id', authorize([PERMISSIONS.POWER_OPS_READ]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getBadgesByUser.bind(this) as AsyncRequestHandler)); // New endpoint for user badges
    this.router.post('/achievements', authorize([PERMISSIONS.POWER_OPS_MANAGE_ALL]), validate(grantAchievementSchema), asyncHandler(this.grantAchievement.bind(this) as AsyncRequestHandler));
    this.router.get('/achievements/:entityType/:entityId', authorize([PERMISSIONS.POWER_OPS_READ]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getAchievements.bind(this) as AsyncRequestHandler));
    this.router.get('/streaks/:entityType/:entityId', authorize([PERMISSIONS.POWER_OPS_READ]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getStreaks.bind(this) as AsyncRequestHandler));
    this.router.get('/leaderboard/:metric', authorize([PERMISSIONS.POWER_OPS_READ]), validate(getLeaderboardQuerySchema), asyncHandler(this.getLeaderboard.bind(this) as AsyncRequestHandler));
    this.router.get('/budgets/:entityType/:entityId', authorize([PERMISSIONS.BILLING_VIEW]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getBudgets.bind(this) as AsyncRequestHandler));
    this.router.post('/budgets', authorize([PERMISSIONS.BILLING_MANAGE]), validate(createBudgetSchema), asyncHandler(this.createBudget.bind(this) as AsyncRequestHandler));
    this.router.put('/budgets/:budgetId', authorize([PERMISSIONS.BILLING_MANAGE]), validate(updateBudgetSchema), asyncHandler(this.updateBudget.bind(this) as AsyncRequestHandler));
    this.router.delete('/budgets/:budgetId', authorize([PERMISSIONS.BILLING_MANAGE]), asyncHandler(this.deleteBudget.bind(this) as AsyncRequestHandler));
    this.router.get('/invoices/:entityType/:entityId', authorize([PERMISSIONS.BILLING_VIEW]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getInvoices.bind(this) as AsyncRequestHandler));
    this.router.post('/invoices', authorize([PERMISSIONS.BILLING_MANAGE]), validate(createInvoiceSchema), asyncHandler(this.createInvoice.bind(this) as AsyncRequestHandler));
    this.router.post('/payments', authorize([PERMISSIONS.BILLING_MANAGE]), validate(processPaymentSchema), asyncHandler(this.processPayment.bind(this) as AsyncRequestHandler));
    this.router.get('/recommendations/:entityType/:entityId', authorize([PERMISSIONS.BILLING_VIEW]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getCostOptimizationRecommendations.bind(this) as AsyncRequestHandler));
    this.router.get('/resource-limits/:entityType/:entityId', authorize([PERMISSIONS.BILLING_VIEW]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getResourceUsageLimits.bind(this) as AsyncRequestHandler));
    this.router.post('/resource-limits', authorize([PERMISSIONS.BILLING_MANAGE]), validate(setResourceUsageLimitSchema), asyncHandler(this.setResourceUsageLimit.bind(this) as AsyncRequestHandler));
    this.router.get('/notifications/:entityType/:entityId', authorize([PERMISSIONS.POWER_OPS_READ]), validate(entityIdEntityTypeQuerySchema), asyncHandler(this.getNotifications.bind(this) as AsyncRequestHandler));
    this.router.post('/notifications', authorize([PERMISSIONS.POWER_OPS_CREATE]), validate(createNotificationSchema), asyncHandler(this.createNotification.bind(this) as AsyncRequestHandler));
  }

  logPowerOpsUsage = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: LogPowerOpsUsageRequest = req.body as LogPowerOpsUsageRequest;
    const usage = await this.powerOpsService.logUsage(req.user, data);
    res.status(201).json(usage);
  });

  getPowerOpsUsage = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType, startDate, endDate } = req.query as { entityId?: string; entityType?: string; startDate?: string; endDate?: string; };
    // Validation is now handled by Joi middleware

    const usage = await this.powerOpsService.getUsage(
      req.user,
      requireParam(entityId as string, 'entityId'),
      entityType as EntityType, // Keep as EntityType, validation above
      getParam(startDate as string | undefined),
      getParam(endDate as string | undefined)
    );
    res.status(200).json(usage);
  });

  // Gamification (XP)
  getXpByUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { id } = req.params as { id: string };
    const xp = await this.powerOpsService.getXp(req.user, requireParam(id, 'id'), EntityType.User);
    res.status(200).json(xp);
  });

  getXpByOrg = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { id } = req.params as { id: string };
    const xp = await this.powerOpsService.getXp(req.user, requireParam(id, 'id'), EntityType.PaimInstance); // Assuming org is PaimInstance
    res.status(200).json(xp);
  });

  awardXp = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: AwardXpRequest = req.body as AwardXpRequest;
    const xp = await this.powerOpsService.awardXp(req.user, data);
    res.status(200).json(xp);
  });

  // Gamification (Badges)
  getAllBadges = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const badges = await this.powerOpsService.getAllBadges(req.user);
    res.status(200).json(badges);
  });

  getBadgesByUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { id } = req.params as { id: string };
    const badges = await this.powerOpsService.getBadges(req.user, requireParam(id, 'id'), EntityType.User);
    res.status(200).json(badges);
  });

  awardBadge = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: AwardBadgeRequest = req.body as AwardBadgeRequest;
    const badge = await this.powerOpsService.awardBadge(req.user, data);
    res.status(200).json(badge);
  });

  // Gamification (Achievements)
  getAchievements = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType } = req.query as { entityId?: string; entityType?: string; };
    // Validation is now handled by Joi middleware

    const achievements = await this.powerOpsService.getAchievements(req.user, requireParam(entityId as string, 'entityId'), requireParam(entityType as string, 'entityType') as EntityType);
    res.status(200).json(achievements);
  });

  grantAchievement = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: GrantAchievementRequest = req.body as GrantAchievementRequest;
    const achievement = await this.powerOpsService.grantAchievement(req.user, data);
    res.status(200).json(achievement);
  });

  // Gamification (Streaks)
  getStreaks = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType } = req.query as { entityId?: string; entityType?: string; };
    // Validation is now handled by Joi middleware

    const streaks = await this.powerOpsService.getStreaks(req.user, requireParam(entityId as string, 'entityId'), requireParam(entityType as string, 'entityType') as EntityType);
    res.status(200).json(streaks);
  });

  // Cost Management (Budgets)
  getBudgets = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType } = req.query as { entityId?: string; entityType?: string; };
    // Validation is now handled by Joi middleware

    const budgets = await this.powerOpsService.getBudgets(req.user, requireParam(entityId as string, 'entityId'), requireParam(entityType as string, 'entityType') as EntityType);
    res.status(200).json(budgets);
  });

  createBudget = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: CreateBudgetRequest = req.body as CreateBudgetRequest;
    const budget = await this.powerOpsService.createBudget(req.user, data);
    res.status(201).json(budget);
  });

  updateBudget = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { budgetId } = req.params as { budgetId: string };
    const data: UpdateBudgetRequest = req.body as UpdateBudgetRequest;
    const updatedBudget = await this.powerOpsService.updateBudget(req.user, requireParam(budgetId, 'budgetId'), data);
    res.status(200).json(updatedBudget);
  });

  deleteBudget = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { budgetId } = req.params as { budgetId: string };
    await this.powerOpsService.deleteBudget(req.user, requireParam(budgetId, 'budgetId'));
    res.status(204).send();
  });

  // Billing (Invoices)
  getInvoices = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType } = req.query as { entityId?: string; entityType?: string; };
    // Validation is now handled by Joi middleware

    const invoices = await this.powerOpsService.getInvoices(req.user, requireParam(entityId as string, 'entityId'), requireParam(entityType as string, 'entityType') as EntityType);
    res.status(200).json(invoices);
  });

  createInvoice = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: CreateInvoiceRequest = req.body as CreateInvoiceRequest;
    const invoice = await this.powerOpsService.createInvoice(req.user, data);
    res.status(201).json(invoice);
  });

  // Billing (Payments)
  processPayment = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: ProcessPaymentRequest = req.body as ProcessPaymentRequest;
    const payment = await this.powerOpsService.processPayment(req.user, data);
    res.status(200).json(payment);
  });

  // Gamification (Leaderboard)
  getLeaderboard = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { metric, limit } = req.query as { metric?: string; limit?: string; };
    // Validation is now handled by Joi middleware
    const leaderboard = await this.powerOpsService.getLeaderboard(req.user, requireParam(metric as string, 'metric'), limit ? parseInt(getParam(limit as string | undefined)) : undefined);
    res.status(200).json(leaderboard);
  });

  // Cost Management (Recommendations)
  getCostOptimizationRecommendations = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType } = req.query as { entityId?: string; entityType?: string; };
    // Validation is now handled by Joi middleware

    const recommendations = await this.powerOpsService.getCostOptimizationRecommendations(req.user, requireParam(entityId as string, 'entityId'), requireParam(entityType as string, 'entityType') as EntityType);
    res.status(200).json(recommendations);
  });

  // Resource Management (Limits)
  getResourceUsageLimits = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType } = req.query as { entityId?: string; entityType?: string; };
    // Validation is now handled by Joi middleware

    const limits = await this.powerOpsService.getResourceUsageLimits(req.user, requireParam(entityId as string, 'entityId'), requireParam(entityType as string, 'entityType') as EntityType);
    res.status(200).json(limits);
  });

  setResourceUsageLimit = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: SetResourceUsageLimitRequest = req.body as SetResourceUsageLimitRequest;
    const limit = await this.powerOpsService.setResourceUsageLimit(req.user, data);
    res.status(201).json(limit);
  });

  // Notifications
  getNotifications = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const { entityId, entityType } = req.query as { entityId?: string; entityType?: string; };
    // Validation is now handled by Joi middleware

    const notifications = await this.powerOpsService.getNotifications(req.user, requireParam(entityId as string, 'entityId'), requireParam(entityType as string, 'entityType') as EntityType);
    res.status(200).json(notifications);
  });

  createNotification = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError('User not authenticated', { originalStatusCode: 401 });
    }
    const data: CreateNotificationRequest = req.body as CreateNotificationRequest;
    const notification = await this.powerOpsService.createNotification(req.user, data);
    res.status(201).json(notification);
  });
}