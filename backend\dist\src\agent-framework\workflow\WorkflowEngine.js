"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowEngine = void 0;
const Agent_1 = require("../core/Agent");
const EventEmitter_1 = require("../core/EventEmitter");
const ToolRegistry_1 = require("../tools/ToolRegistry");
const logger_1 = __importDefault(require("../../config/logger"));
/**
 * Workflow Engine for orchestrating agent and tool execution
 */
class WorkflowEngine extends EventEmitter_1.EventEmitter {
    static instance;
    workflows = new Map();
    executions = new Map();
    toolRegistry;
    constructor() {
        super();
        this.toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
    }
    static getInstance() {
        if (!WorkflowEngine.instance) {
            WorkflowEngine.instance = new WorkflowEngine();
        }
        return WorkflowEngine.instance;
    }
    /**
     * Register a workflow definition
     */
    registerWorkflow(workflow) {
        if (this.workflows.has(workflow.id)) {
            throw new Error(`Workflow '${workflow.id}' is already registered`);
        }
        // Validate workflow definition
        this.validateWorkflow(workflow);
        this.workflows.set(workflow.id, workflow);
        logger_1.default.info(`Workflow '${workflow.name}' (${workflow.id}) registered`);
    }
    /**
     * Unregister a workflow
     */
    unregisterWorkflow(workflowId) {
        const removed = this.workflows.delete(workflowId);
        if (removed) {
            logger_1.default.info(`Workflow '${workflowId}' unregistered`);
        }
        return removed;
    }
    /**
     * Get workflow definition
     */
    getWorkflow(workflowId) {
        return this.workflows.get(workflowId);
    }
    /**
     * Get all workflow definitions
     */
    getAllWorkflows() {
        return Array.from(this.workflows.values());
    }
    /**
     * Execute a workflow
     */
    async executeWorkflow(workflowId, input, context) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) {
            throw new Error(`Workflow '${workflowId}' not found`);
        }
        const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const execution = {
            id: executionId,
            workflowId,
            status: 'pending',
            startTime: new Date().toISOString(),
            input,
            stepResults: new Map(),
            metadata: context
        };
        this.executions.set(executionId, execution);
        logger_1.default.info(`Starting workflow execution ${executionId} for workflow ${workflowId}`);
        try {
            execution.status = 'running';
            this.emit('workflow:started', { execution, workflow });
            // Execute workflow steps
            const result = await this.executeSteps(workflow, execution);
            execution.status = 'completed';
            execution.endTime = new Date().toISOString();
            execution.output = result;
            this.emit('workflow:completed', { execution, workflow });
            logger_1.default.info(`Workflow execution ${executionId} completed successfully`);
        }
        catch (error) {
            execution.status = 'failed';
            execution.endTime = new Date().toISOString();
            execution.error = error instanceof Error ? error.message : 'Unknown error';
            this.emit('workflow:failed', { execution, workflow, error });
            logger_1.default.error(`Workflow execution ${executionId} failed: ${error}`);
        }
        return execution;
    }
    /**
     * Cancel a workflow execution
     */
    async cancelExecution(executionId) {
        const execution = this.executions.get(executionId);
        if (!execution) {
            throw new Error(`Execution '${executionId}' not found`);
        }
        if (execution.status !== 'running') {
            throw new Error(`Execution '${executionId}' is not running`);
        }
        execution.status = 'cancelled';
        execution.endTime = new Date().toISOString();
        this.emit('workflow:cancelled', { execution });
        logger_1.default.info(`Workflow execution ${executionId} cancelled`);
    }
    /**
     * Get workflow execution
     */
    getExecution(executionId) {
        return this.executions.get(executionId);
    }
    /**
     * Get all executions for a workflow
     */
    getExecutions(workflowId) {
        const executions = Array.from(this.executions.values());
        if (workflowId) {
            return executions.filter(exec => exec.workflowId === workflowId);
        }
        return executions;
    }
    /**
     * Execute workflow steps
     */
    async executeSteps(workflow, execution) {
        const stepResults = {};
        const completedSteps = new Set();
        // Build dependency graph
        const dependencyGraph = this.buildDependencyGraph(workflow.steps);
        // Execute steps in dependency order
        while (completedSteps.size < workflow.steps.length) {
            const readySteps = workflow.steps.filter(step => !completedSteps.has(step.id) &&
                step.dependencies.every(dep => completedSteps.has(dep)));
            if (readySteps.length === 0) {
                throw new Error('Circular dependency detected in workflow steps');
            }
            // Execute ready steps (can be parallel)
            const stepPromises = readySteps.map(step => this.executeStep(step, execution, stepResults));
            const results = await Promise.allSettled(stepPromises);
            // Process results
            for (let i = 0; i < results.length; i++) {
                const step = readySteps[i];
                const result = results[i];
                if (result.status === 'fulfilled') {
                    stepResults[step.id] = result.value;
                    completedSteps.add(step.id);
                }
                else {
                    // Handle step failure
                    const stepResult = {
                        stepId: step.id,
                        status: 'failed',
                        startTime: new Date().toISOString(),
                        endTime: new Date().toISOString(),
                        input: {},
                        error: result.reason?.message || 'Step execution failed',
                        attempts: 1
                    };
                    execution.stepResults.set(step.id, stepResult);
                    throw new Error(`Step '${step.id}' failed: ${stepResult.error}`);
                }
            }
        }
        return stepResults;
    }
    /**
     * Execute a single workflow step
     */
    async executeStep(step, execution, context) {
        const stepResult = {
            stepId: step.id,
            status: 'running',
            startTime: new Date().toISOString(),
            input: { ...step.parameters, ...context },
            attempts: 0
        };
        execution.stepResults.set(step.id, stepResult);
        execution.currentStep = step.id;
        logger_1.default.info(`Executing step '${step.name}' (${step.id})`);
        try {
            let result;
            switch (step.type) {
                case 'agent':
                    result = await this.executeAgentStep(step, stepResult.input);
                    break;
                case 'tool':
                    result = await this.executeToolStep(step, stepResult.input, execution);
                    break;
                case 'condition':
                    result = await this.executeConditionStep(step, stepResult.input);
                    break;
                default:
                    throw new Error(`Unsupported step type: ${step.type}`);
            }
            stepResult.status = 'completed';
            stepResult.endTime = new Date().toISOString();
            stepResult.output = result;
            logger_1.default.info(`Step '${step.id}' completed successfully`);
            return result;
        }
        catch (error) {
            stepResult.status = 'failed';
            stepResult.endTime = new Date().toISOString();
            stepResult.error = error instanceof Error ? error.message : 'Unknown error';
            logger_1.default.error(`Step '${step.id}' failed: ${stepResult.error}`);
            throw error;
        }
    }
    /**
     * Execute an agent step
     */
    async executeAgentStep(step, input) {
        if (!step.agentId) {
            throw new Error(`Agent ID is required for agent step '${step.id}'`);
        }
        const agent = Agent_1.Agent.getAgent(step.agentId);
        if (!agent) {
            throw new Error(`Agent '${step.agentId}' not found`);
        }
        const task = {
            id: `task_${step.id}_${Date.now()}`,
            type: 'workflow_step',
            payload: input,
            timestamp: new Date()
        };
        const result = await agent.execute(task);
        if (!result.success) {
            throw new Error(result.error || 'Agent execution failed');
        }
        return result.data;
    }
    /**
     * Execute a tool step
     */
    async executeToolStep(step, input, execution) {
        if (!step.toolId) {
            throw new Error(`Tool ID is required for tool step '${step.id}'`);
        }
        const context = {
            agentId: 'workflow-engine',
            taskId: `task_${step.id}_${Date.now()}`,
            timestamp: new Date().toISOString(),
            metadata: execution.metadata
        };
        const result = await this.toolRegistry.execute(step.toolId, input, context);
        if (!result.success) {
            throw new Error(result.error || 'Tool execution failed');
        }
        return result.data;
    }
    /**
     * Execute a condition step
     */
    async executeConditionStep(step, input) {
        if (!step.condition) {
            throw new Error(`Condition is required for condition step '${step.id}'`);
        }
        // Simple condition evaluation (in production, use a proper expression engine)
        const conditionResult = this.evaluateCondition(step.condition, input);
        return {
            condition: step.condition,
            result: conditionResult,
            input
        };
    }
    /**
     * Evaluate a condition (simplified implementation)
     */
    evaluateCondition(condition, context) {
        try {
            // This is a simplified implementation
            // In production, use a proper expression engine like JSONata or similar
            // Replace variables in condition
            let evaluatedCondition = condition;
            for (const [key, value] of Object.entries(context)) {
                const regex = new RegExp(`\\b${key}\\b`, 'g');
                evaluatedCondition = evaluatedCondition.replace(regex, JSON.stringify(value));
            }
            // Simple evaluation (UNSAFE - use proper expression engine in production)
            return eval(evaluatedCondition);
        }
        catch (error) {
            logger_1.default.error(`Condition evaluation failed: ${error}`);
            return false;
        }
    }
    /**
     * Build dependency graph for steps
     */
    buildDependencyGraph(steps) {
        const graph = new Map();
        for (const step of steps) {
            graph.set(step.id, step.dependencies);
        }
        return graph;
    }
    /**
     * Validate workflow definition
     */
    validateWorkflow(workflow) {
        if (!workflow.id || !workflow.name || !workflow.steps) {
            throw new Error('Workflow must have id, name, and steps');
        }
        if (workflow.steps.length === 0) {
            throw new Error('Workflow must have at least one step');
        }
        // Validate step IDs are unique
        const stepIds = new Set();
        for (const step of workflow.steps) {
            if (stepIds.has(step.id)) {
                throw new Error(`Duplicate step ID: ${step.id}`);
            }
            stepIds.add(step.id);
        }
        // Validate dependencies exist
        for (const step of workflow.steps) {
            for (const dep of step.dependencies) {
                if (!stepIds.has(dep)) {
                    throw new Error(`Step '${step.id}' depends on non-existent step '${dep}'`);
                }
            }
        }
        // Check for circular dependencies (simplified check)
        this.checkCircularDependencies(workflow.steps);
    }
    /**
     * Check for circular dependencies
     */
    checkCircularDependencies(steps) {
        const visited = new Set();
        const recursionStack = new Set();
        const hasCycle = (stepId) => {
            if (recursionStack.has(stepId)) {
                return true;
            }
            if (visited.has(stepId)) {
                return false;
            }
            visited.add(stepId);
            recursionStack.add(stepId);
            const step = steps.find(s => s.id === stepId);
            if (step) {
                for (const dep of step.dependencies) {
                    if (hasCycle(dep)) {
                        return true;
                    }
                }
            }
            recursionStack.delete(stepId);
            return false;
        };
        for (const step of steps) {
            if (hasCycle(step.id)) {
                throw new Error('Circular dependency detected in workflow');
            }
        }
    }
}
exports.WorkflowEngine = WorkflowEngine;
