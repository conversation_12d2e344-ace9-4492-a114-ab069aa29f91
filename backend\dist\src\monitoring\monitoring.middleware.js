"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringMiddleware = void 0;
const monitoring_service_1 = require("./monitoring.service");
const monitoring_repository_1 = require("./monitoring.repository");
const audit_service_1 = require("../audit/audit.service");
const paim_service_1 = require("../paim/paim.service");
const powerops_service_1 = require("../powerops/powerops.service");
const cultural_sensitivity_service_1 = require("../cultural-sensitivity/cultural-sensitivity.service");
const db_1 = __importDefault(require("../database/db")); // Default import for knex instance
const logger_1 = __importDefault(require("../config/logger"));
const monitoring_types_1 = require("./monitoring.types");
const uuid_1 = require("uuid");
class MonitoringMiddleware {
    monitoringService;
    constructor() {
        const monitoringRepository = new monitoring_repository_1.MonitoringRepository();
        const auditTrailService = new audit_service_1.AuditTrailService();
        const paimService = new paim_service_1.PaimService(null, auditTrailService, {}, // NotificationService placeholder
        {} // WebSocketService placeholder
        ); // PaimRepository and AuditTrailService needed
        const powerOpsService = new powerops_service_1.PowerOpsService();
        const culturalSensitivityService = new cultural_sensitivity_service_1.CulturalSensitivityService(db_1.default); // Knex instance needed
        this.monitoringService = new monitoring_service_1.MonitoringService(monitoringRepository, auditTrailService, paimService, powerOpsService, culturalSensitivityService);
    }
    // Middleware for request/response monitoring and performance metric collection
    requestMonitor = async (req, res, next) => {
        const start = process.hrtime.bigint();
        res.on('finish', async () => {
            const end = process.hrtime.bigint();
            const duration = Number(end - start) / 1_000_000; // duration in milliseconds
            const serviceName = req.baseUrl.split('/')[2] || 'unknown-service'; // Extract service name from URL
            const endpoint = `${req.method} ${req.originalUrl}`;
            const statusCode = res.statusCode;
            logger_1.default.info(`Request: ${endpoint}, Status: ${statusCode}, Duration: ${duration}ms`);
            // Collect performance metrics
            const metrics = {
                cpuUsage: 0, // Placeholder - actual CPU usage per request is complex
                memoryUsage: 0, // Placeholder
                diskUsage: 0, // Placeholder
                networkLatency: 0, // Placeholder
                requestPerSecond: 1, // This metric is usually aggregated over time
                errorRate: statusCode >= 400 ? 1 : 0, // Simple error rate for this request
                responseTime: duration,
            };
            try {
                await this.monitoringService.recordPerformanceMetrics(serviceName, metrics);
            }
            catch (error) {
                logger_1.default.error(`Failed to record performance metrics: ${error}`);
            }
        });
        next();
    };
    // Middleware for error tracking and classification
    errorTracker = async (err, req, res, next) => {
        logger_1.default.error(`Unhandled error: ${err.message}`, { stack: err.stack, path: req.path });
        const serviceName = req.baseUrl.split('/')[2] || 'unknown-service';
        const errorId = (0, uuid_1.v4)();
        const systemError = {
            id: errorId,
            serviceName: serviceName,
            code: err.code || 'UNKNOWN_ERROR',
            message: err.message,
            severity: monitoring_types_1.ErrorSeverity.CRITICAL, // Default to critical for unhandled errors
            timestamp: new Date().toISOString(),
            details: {
                stack: err.stack,
                path: req.path,
                method: req.method,
                body: req.body,
                query: req.query,
                params: req.params,
            },
            isResolved: false,
        };
        try {
            await this.monitoringService['repository'].addSystemError(systemError);
            // Trigger alert for the error
            await this.monitoringService.triggerAlert({
                id: (0, uuid_1.v4)(),
                type: 'error',
                serviceName: systemError.serviceName, // Corrected from serviceError
                message: `System Error: ${systemError.message} (Code: ${systemError.code})`,
                timestamp: systemError.timestamp,
                severity: systemError.severity,
                isAcknowledged: false,
            });
        }
        catch (monitoringError) {
            logger_1.default.error(`Failed to record or alert system error: ${monitoringError}`);
        }
        // Pass the error to the next error handling middleware or send a response
        next(err);
    };
    // Health check endpoint middleware (can be used for specific routes)
    healthCheck = (serviceName) => async (req, res, next) => {
        try {
            // In a real scenario, this would perform actual checks (DB connection, external services)
            const healthStatus = {
                serviceName: serviceName,
                status: monitoring_types_1.HealthStatus.OK, // Placeholder
                timestamp: new Date().toISOString(),
                details: 'Service is operational.',
            };
            await this.monitoringService.recordSystemHealth(healthStatus);
            res.status(200).json(healthStatus);
        }
        catch (error) {
            logger_1.default.error(`Health check failed for ${serviceName}: ${error}`);
            res.status(500).json({ serviceName, status: monitoring_types_1.HealthStatus.CRITICAL, timestamp: new Date().toISOString(), details: 'Health check failed.' });
        }
    };
}
exports.MonitoringMiddleware = MonitoringMiddleware;
