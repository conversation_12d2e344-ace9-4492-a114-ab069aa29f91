// backend/src/paim/paim.permissions.ts
import { Permission, PERMISSIONS } from '../auth/permissions';
import { PaimTierEnum } from '../types/db'; // Import the canonical PaimTierEnum

export { PaimTierEnum as PAIMTier }; // Re-export for backward compatibility and clarity

const basicPermissions: Permission[] = [
  PERMISSIONS.PAIM_READ,
  PERMISSIONS.POWER_OPS_READ,
  PERMISSIONS.WORKFLOW_READ,
  PERMISSIONS.AGENT_READ,
];

const professionalPermissions: Permission[] = [
  ...basicPermissions,
  PERMISSIONS.PAIM_CREATE,
  PERMISSIONS.PAIM_UPDATE,
  PERMISSIONS.POWER_OPS_CREATE,
  PERMISSIONS.POWER_OPS_UPDATE,
  PERMISSIONS.WORKFLOW_CREATE,
  PERMISSIONS.WORKFLOW_UPDATE,
];

const enterprisePermissions: Permission[] = [
  ...professionalPermissions,
  PERMISSIONS.PAIM_DELETE,
  PERMISSIONS.PAIM_MANAGE_TIERS, // Enterprise can manage their own tiers (e.g., downgrade/upgrade)
  PERMISSIONS.POWER_OPS_DELETE,
  PERMISSIONS.POWER_OPS_MANAGE_ALL,
  PERMISSIONS.WORKFLOW_DELETE,
  PERMISSIONS.WORKFLOW_MANAGE_ALL,
  PERMISSIONS.AGENT_CREATE,
  PERMISSIONS.AGENT_UPDATE,
  PERMISSIONS.AGENT_DELETE,
  PERMISSIONS.AGENT_MANAGE_ALL,
  PERMISSIONS.ORG_MANAGE_MEMBERS,
  PERMISSIONS.ORG_MANAGE_ROLES,
  PERMISSIONS.BILLING_VIEW,
];

export const PAIM_TIER_PERMISSIONS: Record<PaimTierEnum, Permission[]> = {
  [PaimTierEnum.Basic]: basicPermissions,
  [PaimTierEnum.Professional]: professionalPermissions,
  [PaimTierEnum.Enterprise]: enterprisePermissions,
  [PaimTierEnum.Custom]: [
    // Custom tiers will have permissions defined dynamically,
    // but for a baseline, they might inherit Enterprise or have specific ones.
    // For now, we'll give them all permissions for demonstration.
    ...Object.values(PERMISSIONS),
  ],
  [PaimTierEnum.CompanyAdmin]: [
    // CompanyAdmin has all permissions
    ...Object.values(PERMISSIONS),
  ],
};

// Helper function to check if a tier includes another tier's permissions
export function tierIncludes(tier: PaimTierEnum, requiredTier: PaimTierEnum): boolean {
  const tierOrder = [PaimTierEnum.Basic, PaimTierEnum.Professional, PaimTierEnum.Enterprise, PaimTierEnum.Custom, PaimTierEnum.CompanyAdmin];
  const tierIndex = tierOrder.indexOf(tier);
  const requiredTierIndex = tierOrder.indexOf(requiredTier);
  return tierIndex >= requiredTierIndex;
}

// TODO: Implement cross-PAIM permission validation for multi-tenant scenarios.
// This would involve checking if a user from one PAIM can access resources of another PAIM.
// This typically requires checking the organizationId or tenantId associated with the resource and the user.
// Example:
// public canAccessCrossPAIMResource(
//   userPaimTier: PAIMTier,
//   userOrganizationId: string,
//   resourceOrganizationId: string,
//   requiredPermission: Permission
// ): boolean {
//   if (userOrganizationId === resourceOrganizationId) {
//     return authorizationService.hasPermission([], userPaimTier, requiredPermission); // Check within same PAIM
//   }
//   // Logic for cross-PAIM access, e.g., SuperAdmin can access all, or specific agreements
//   return false;
// }