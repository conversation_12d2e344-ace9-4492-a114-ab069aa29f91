"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentController = void 0;
const express_1 = require("express");
const agent_service_1 = require("./agent.service");
const asyncHandler_1 = require("../utils/asyncHandler"); // Assuming asyncHandler utility exists
const errors_1 = require("../utils/errors"); // Assuming CustomError class exists
const type_guards_1 = require("../utils/type-guards");
class AgentController {
    router;
    agentService;
    constructor() {
        this.router = (0, express_1.Router)();
        this.agentService = new agent_service_1.AgentService();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/', (0, asyncHandler_1.asyncHandler)(this.getAllAgents));
        this.router.post('/', (0, asyncHandler_1.asyncHandler)(this.createAgent));
        this.router.get('/:agentId', (0, asyncHandler_1.asyncHandler)(this.getAgentById));
        this.router.put('/:agentId', (0, asyncHandler_1.asyncHandler)(this.updateAgent));
        this.router.delete('/:agentId', (0, asyncHandler_1.asyncHandler)(this.deleteAgent));
        this.router.post('/:agentId/assign', (0, asyncHandler_1.asyncHandler)(this.assignAgent));
        this.router.post('/workflows/:workflowId/execute', (0, asyncHandler_1.asyncHandler)(this.executeWorkflow));
        this.router.get('/:agentId/performance', (0, asyncHandler_1.asyncHandler)(this.getAgentPerformance));
    }
    // Helper to extract tenantId and userId from request (assuming authentication middleware populates req.user)
    getAuthContext(req) {
        // In a real application, req.user would be populated by authentication middleware
        // For now, using mock values or assuming they are passed in headers/body for testing
        const tenantId = req.headers['x-tenant-id'] || 'mock-tenant-id';
        const userId = req.user?.id || 'mock-user-id';
        return { tenantId, userId };
    }
    // GET /agents
    async getAllAgents(req, res) {
        const { tenantId } = this.getAuthContext(req);
        const options = {
            page: parseInt((0, type_guards_1.getParam)(req.query.page, '1')),
            size: parseInt((0, type_guards_1.getParam)(req.query.size, '10')),
            sort: (0, type_guards_1.getParam)(req.query.sort),
            persona: (0, type_guards_1.getParam)(req.query.persona),
            status: (0, type_guards_1.getParam)(req.query.status),
        };
        const { agents, pagination } = await this.agentService.getAllAgents(tenantId, options);
        res.status(200).json({ data: agents, pagination });
    }
    // POST /agents
    async createAgent(req, res) {
        const { tenantId } = this.getAuthContext(req);
        const createRequest = req.body;
        const newAgent = await this.agentService.createAgent(tenantId, createRequest);
        res.status(201).json(newAgent);
    }
    // GET /agents/:agentId
    async getAgentById(req, res) {
        const { tenantId } = this.getAuthContext(req);
        const { agentId } = req.params;
        const agent = await this.agentService.getAgentById(tenantId, (0, type_guards_1.requireParam)(agentId, 'agentId'));
        if (!agent) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(200).json(agent);
    }
    // PUT /agents/:agentId
    async updateAgent(req, res) {
        const { tenantId, userId } = this.getAuthContext(req);
        const { agentId } = req.params;
        const updateRequest = req.body;
        const updatedAgent = await this.agentService.updateAgent(tenantId, (0, type_guards_1.requireParam)(agentId, 'agentId'), updateRequest, userId);
        if (!updatedAgent) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(200).json(updatedAgent);
    }
    // DELETE /agents/:agentId
    async deleteAgent(req, res) {
        const { tenantId, userId } = this.getAuthContext(req);
        const { agentId } = req.params;
        const deleted = await this.agentService.deleteAgent(tenantId, (0, type_guards_1.requireParam)(agentId, 'agentId'), userId);
        if (!deleted) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(204).send(); // No content for successful deletion
    }
    // POST /agents/:agentId/assign
    async assignAgent(req, res) {
        const { tenantId, userId } = this.getAuthContext(req);
        const { agentId } = req.params;
        const assignmentRequest = req.body;
        const assignment = await this.agentService.assignAgent(tenantId, { ...assignmentRequest, agentId: (0, type_guards_1.requireParam)(agentId, 'agentId') }, userId);
        res.status(200).json(assignment);
    }
    // POST /workflows/:workflowId/execute
    async executeWorkflow(req, res) {
        const { tenantId, userId } = this.getAuthContext(req);
        const { workflowId } = req.params;
        const executionRequest = req.body;
        const executionStatus = await this.agentService.executeWorkflow(tenantId, (0, type_guards_1.requireParam)(workflowId, 'workflowId'), executionRequest, userId);
        res.status(200).json(executionStatus);
    }
    // GET /agents/:agentId/performance
    async getAgentPerformance(req, res) {
        const { tenantId } = this.getAuthContext(req);
        const { agentId } = req.params;
        const { startDate, endDate } = req.query;
        const performanceMetrics = await this.agentService.getAgentPerformance(tenantId, (0, type_guards_1.requireParam)(agentId, 'agentId'), (0, type_guards_1.getParam)(startDate), (0, type_guards_1.getParam)(endDate));
        if (!performanceMetrics) {
            throw new errors_1.CustomError(`Performance metrics for agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(200).json(performanceMetrics);
    }
}
exports.AgentController = AgentController;
