"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = void 0;
const EventEmitter_1 = require("./EventEmitter");
const Config_1 = require("./Config");
const logger_1 = __importDefault(require("../../config/logger"));
class Agent extends EventEmitter_1.EventEmitter {
    static registry = new Map();
    id;
    name;
    type;
    config;
    plugins = new Map();
    capabilities = new Map();
    metrics;
    isActive = false;
    constructor(id, name, type, config) {
        super();
        this.id = id;
        this.name = name;
        this.type = type;
        this.config = config || new Config_1.Config();
        this.metrics = {
            tasksExecuted: 0,
            successRate: 100,
            averageExecutionTime: 0,
            lastActivity: new Date()
        };
        logger_1.default.info(`Agent ${this.name} (${this.id}) initialized`);
    }
    /**
     * Execute a task with the agent
     */
    async execute(task) {
        const startTime = Date.now();
        const taskObj = typeof task === 'string'
            ? {
                id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type: 'generic',
                payload: task,
                timestamp: new Date()
            }
            : task;
        logger_1.default.info(`Agent ${this.name} executing task: ${taskObj.id}`);
        try {
            // Emit task start event
            this.emit('task:start', { agent: this, task: taskObj });
            // Check if agent is active
            if (!this.isActive) {
                throw new Error(`Agent ${this.name} is not active`);
            }
            // Execute the task (this will be overridden by specialized agents)
            const result = await this.executeTask(taskObj);
            const executionTime = Date.now() - startTime;
            // Update metrics
            this.updateMetrics(true, executionTime);
            const executionResult = {
                success: true,
                data: result,
                executionTime,
                agentId: this.id,
                taskId: taskObj.id
            };
            // Emit task completion event
            this.emit('task:complete', { agent: this, task: taskObj, result: executionResult });
            logger_1.default.info(`Agent ${this.name} completed task ${taskObj.id} in ${executionTime}ms`);
            return executionResult;
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            this.updateMetrics(false, executionTime);
            const executionResult = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime,
                agentId: this.id,
                taskId: taskObj.id
            };
            // Emit task error event
            this.emit('task:error', { agent: this, task: taskObj, error, result: executionResult });
            logger_1.default.error(`Agent ${this.name} failed to execute task ${taskObj.id}: ${error}`);
            return executionResult;
        }
    }
    /**
     * Override this method in specialized agents
     */
    async executeTask(task) {
        // Default implementation - just return the payload
        return {
            message: `Task ${task.id} executed by ${this.name}`,
            payload: task.payload,
            timestamp: new Date().toISOString()
        };
    }
    /**
     * Get the agent's configuration
     */
    getConfig() {
        return this.config;
    }
    /**
     * Register an agent in the global registry
     */
    static registerAgent(name, agent) {
        if (Agent.registry.has(name)) {
            throw new Error(`Agent with name '${name}' is already registered`);
        }
        Agent.registry.set(name, agent);
        logger_1.default.info(`Agent '${name}' registered in global registry`);
    }
    /**
     * Get an agent from the global registry
     */
    static getAgent(name) {
        return Agent.registry.get(name);
    }
    /**
     * Get all registered agents
     */
    static getAllAgents() {
        return new Map(Agent.registry);
    }
    /**
     * Unregister an agent
     */
    static unregisterAgent(name) {
        const removed = Agent.registry.delete(name);
        if (removed) {
            logger_1.default.info(`Agent '${name}' unregistered from global registry`);
        }
        return removed;
    }
    /**
     * Start the agent
     */
    async start() {
        if (this.isActive) {
            logger_1.default.warn(`Agent ${this.name} is already active`);
            return;
        }
        this.isActive = true;
        this.emit('agent:start', { agent: this });
        logger_1.default.info(`Agent ${this.name} started`);
    }
    /**
     * Stop the agent
     */
    async stop() {
        if (!this.isActive) {
            logger_1.default.warn(`Agent ${this.name} is already inactive`);
            return;
        }
        this.isActive = false;
        this.emit('agent:stop', { agent: this });
        logger_1.default.info(`Agent ${this.name} stopped`);
    }
    /**
     * Install a plugin
     */
    async installPlugin(plugin) {
        if (this.plugins.has(plugin.name)) {
            throw new Error(`Plugin '${plugin.name}' is already installed`);
        }
        await plugin.install(this);
        this.plugins.set(plugin.name, plugin);
        this.emit('plugin:installed', { agent: this, plugin });
        logger_1.default.info(`Plugin '${plugin.name}' installed on agent ${this.name}`);
    }
    /**
     * Uninstall a plugin
     */
    async uninstallPlugin(pluginName) {
        const plugin = this.plugins.get(pluginName);
        if (!plugin) {
            throw new Error(`Plugin '${pluginName}' is not installed`);
        }
        await plugin.uninstall(this);
        this.plugins.delete(pluginName);
        this.emit('plugin:uninstalled', { agent: this, plugin });
        logger_1.default.info(`Plugin '${pluginName}' uninstalled from agent ${this.name}`);
    }
    /**
     * Add a capability to the agent
     */
    addCapability(capability) {
        this.capabilities.set(capability.name, capability);
        this.emit('capability:added', { agent: this, capability });
        logger_1.default.info(`Capability '${capability.name}' added to agent ${this.name}`);
    }
    /**
     * Remove a capability from the agent
     */
    removeCapability(capabilityName) {
        const removed = this.capabilities.delete(capabilityName);
        if (removed) {
            this.emit('capability:removed', { agent: this, capabilityName });
            logger_1.default.info(`Capability '${capabilityName}' removed from agent ${this.name}`);
        }
        return removed;
    }
    /**
     * Get agent capabilities
     */
    getCapabilities() {
        return Array.from(this.capabilities.values());
    }
    /**
     * Get agent metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Check if agent is active
     */
    getIsActive() {
        return this.isActive;
    }
    /**
     * Update agent metrics
     */
    updateMetrics(success, executionTime) {
        this.metrics.tasksExecuted++;
        this.metrics.lastActivity = new Date();
        // Update average execution time
        const totalTime = this.metrics.averageExecutionTime * (this.metrics.tasksExecuted - 1) + executionTime;
        this.metrics.averageExecutionTime = totalTime / this.metrics.tasksExecuted;
        // Update success rate
        const successfulTasks = Math.round(this.metrics.successRate * (this.metrics.tasksExecuted - 1) / 100);
        const newSuccessfulTasks = success ? successfulTasks + 1 : successfulTasks;
        this.metrics.successRate = (newSuccessfulTasks / this.metrics.tasksExecuted) * 100;
    }
}
exports.Agent = Agent;
