import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('tenants', (table) => {
    table.boolean('founder_club_flag').defaultTo(false);
    table.timestamp('promo_start_date', { useTz: true }).nullable();
    table.timestamp('promo_end_date', { useTz: true }).nullable();
  });
}


export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('tenants', (table) => {
    table.dropColumn('founder_club_flag');
    table.dropColumn('promo_start_date');
    table.dropColumn('promo_end_date');
  });
}

