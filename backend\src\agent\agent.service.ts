import { AgentRepository } from './agent.repository';
import {
  Agent,
  CreateAgentRequest,
  UpdateAgentRequest,
  AgentAssignment,
  AgentAssignmentRequest,
  WorkflowExecutionRequest,
  WorkflowExecutionStatus,
  AgentPerformanceMetrics,
  AgentQueryOptions,
} from './agent.types';
import { PaginationMetadata } from '../utils/pagination';
import { PaimService } from '../paim/paim.service';
import { PaimRepository } from '../paim/paim.repository';
import { AuditTrailService } from '../audit/audit.service';
import logger from '../config/logger';
import { JwtPayload } from '../auth/auth.types';
import { authorizationService } from '../auth/authorization.service';
import { PERMISSIONS } from '../auth/permissions';
import { AuthorizationError, CustomError } from '../utils/errors';

export class AgentService {
  private agentRepository: AgentRepository;
  private paimService: PaimService;
  private auditTrailService: AuditTrailService;

  constructor() {
    this.auditTrailService = new AuditTrailService();
    this.agentRepository = new AgentRepository(this.auditTrailService);
    // PaimService constructor now expects a PaimRepository and AuditTrailService
    this.paimService = new PaimService(
      new PaimRepository(this.auditTrailService),
      this.auditTrailService,
      {} as any, // NotificationService placeholder
      {} as any  // WebSocketService placeholder
    );
  }

  // Agent Lifecycle Management
  public async getAllAgents(
    user: JwtPayload,
    options: AgentQueryOptions,
  ): Promise<{ agents: Agent[]; pagination: PaginationMetadata }> {
    logger.info(`Fetching all agents for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.AGENT_READ)) {
      throw new AuthorizationError('Forbidden: Insufficient permissions to view agents.', 403);
    }

    return this.agentRepository.getAllAgents(user.tenantId, options);
  }

  public async getAgentById(user: JwtPayload, agentId: string): Promise<Agent | undefined> {
    logger.info(`Fetching agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.AGENT_READ)) {
      throw new AuthorizationError('Forbidden: Insufficient permissions to view agent.', 403);
    }

    const agent = await this.agentRepository.getAgentById(user.tenantId, agentId);

    if (agent && !authorizationService.canAccessResource(user, { ownerId: agent.ownerId, organizationId: agent.tenantId, teamId: agent.teamId }, 'read')) {
      throw new AuthorizationError('Forbidden: You do not have access to this specific agent.', 403);
    }

    return agent;
  }

  public async createAgent(user: JwtPayload, agentData: CreateAgentRequest): Promise<Agent> {
    logger.info(`Creating new agent for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.AGENT_CREATE)) {
      throw new AuthorizationError('Forbidden: Insufficient permissions to create agent.', 403);
    }

    // Validate PAIM instance ID and ensure user has access to it
    const paimInstance = await this.paimService.getPaimInstanceById(agentData.paimInstanceId, user);
    if (!paimInstance) {
      throw new CustomError(`PAIM instance with ID ${agentData.paimInstanceId} not found.`, { originalStatusCode: 404 });
    }
    // Further check if the user has permission to create agents within this PAIM instance
    // This could be a more granular check if needed, e.g., based on PAIM hierarchy.
    // For now, the AGENT_CREATE permission combined with PAIM_READ on the instance is sufficient.

    const agent = await this.agentRepository.createAgent(user.tenantId, agentData);
    await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_created', agent.id, agent, user.userId);
    return agent;
  }

  public async updateAgent(user: JwtPayload, agentId: string, agentData: UpdateAgentRequest): Promise<Agent | undefined> {
    logger.info(`Updating agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.AGENT_UPDATE)) {
      throw new AuthorizationError('Forbidden: Insufficient permissions to update agent.', 403);
    }

    const existingAgent = await this.agentRepository.getAgentById(user.tenantId, agentId);
    if (!existingAgent) {
      throw new CustomError(`Agent with ID ${agentId} not found.`, { originalStatusCode: 404 });
    }

    if (!authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'update')) {
      throw new AuthorizationError('Forbidden: You do not have access to update this specific agent.', 403);
    }

    const updatedAgent = await this.agentRepository.updateAgent(user.tenantId, agentId, agentData);
    if (updatedAgent) {
      await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_updated', agentId, updatedAgent, user.userId);
    }
    return updatedAgent;
  }

  public async deleteAgent(user: JwtPayload, agentId: string): Promise<boolean> {
    logger.info(`Deleting agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.AGENT_DELETE)) {
      throw new AuthorizationError('Forbidden: Insufficient permissions to delete agent.', 403);
    }

    const existingAgent = await this.agentRepository.getAgentById(user.tenantId, agentId);
    if (!existingAgent) {
      throw new CustomError(`Agent with ID ${agentId} not found.`, { originalStatusCode: 404 });
    }

    if (!authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'delete')) {
      throw new AuthorizationError('Forbidden: You do not have access to delete this specific agent.', 403);
    }

    const deleted = await this.agentRepository.deleteAgent(user.tenantId, agentId);
    if (deleted) {
      await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_deleted', agentId, { agentId }, user.userId);
    }
    return deleted;
  }

  // Agent Assignment
  public async assignAgent(user: JwtPayload, assignmentData: AgentAssignmentRequest & { agentId: string }): Promise<AgentAssignment> {
    logger.info(`Assigning agent ${assignmentData.agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.AGENT_UPDATE)) { // Assuming assign is an update operation
      throw new AuthorizationError('Forbidden: Insufficient permissions to assign agent.', 403);
    }

    const existingAgent = await this.agentRepository.getAgentById(user.tenantId, assignmentData.agentId);
    if (!existingAgent) {
      throw new CustomError(`Agent with ID ${assignmentData.agentId} not found.`, { originalStatusCode: 404 });
    }

    if (!authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'update')) {
      throw new AuthorizationError('Forbidden: You do not have access to assign this specific agent.', 403);
    }

    const assignment = await this.agentRepository.assignAgent(user.tenantId, assignmentData);
    await this.agentRepository.recordAuditTrail(user.tenantId, 'agent_assigned', assignment.id, assignment, user.userId);
    return assignment;
  }

  // Workflow Orchestration and Agent Execution
  public async executeWorkflow(user: JwtPayload, workflowId: string, executionRequest: WorkflowExecutionRequest): Promise<WorkflowExecutionStatus> {
    logger.info(`Executing workflow ${workflowId} for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.WORKFLOW_CREATE)) { // Assuming execute workflow requires workflow creation permission
      throw new AuthorizationError('Forbidden: Insufficient permissions to execute workflow.', 403);
    }

    // In a real scenario, you would fetch the workflow and check resource-level permissions on it.
    // For now, we'll assume the permission check above is sufficient.

    // Placeholder for actual workflow execution logic
    // This would involve:
    // 1. Fetching workflow definition
    // 2. Validating input data
    // 3. Scheduling tasks and assigning to agents
    // 4. Monitoring task execution
    // 5. Handling errors and retries
    // 6. Recording execution history

    const executionStatus: WorkflowExecutionStatus = {
      executionId: 'mock-execution-id-' + Math.random().toString(36).substring(7),
      workflowId: workflowId,
      status: 'running',
      startTime: new Date().toISOString(),
      outputData: {},
    };

    // Simulate async execution
    setTimeout(async () => {
      executionStatus.status = 'completed';
      executionStatus.endTime = new Date().toISOString();
      executionStatus.outputData = { message: 'Workflow completed successfully (mock)' };
      await this.agentRepository.recordWorkflowExecution(user.tenantId, executionStatus);
      logger.info(`Workflow ${workflowId} completed for tenant: ${user.tenantId}`);
    }, 5000); // Simulate 5 seconds execution

    await this.agentRepository.recordAuditTrail(user.tenantId, 'workflow_execution_initiated', workflowId, executionRequest, user.userId);
    return executionStatus;
  }

  // Agent Performance Monitoring
  public async getAgentPerformance(user: JwtPayload, agentId: string, startDate?: string, endDate?: string): Promise<AgentPerformanceMetrics | undefined> {
    logger.info(`Fetching performance metrics for agent ${agentId} for tenant: ${user.tenantId} by user: ${user.userId}`);

    if (!authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.AGENT_READ)) { // Assuming performance read is part of agent read
      throw new AuthorizationError('Forbidden: Insufficient permissions to view agent performance.', 403);
    }

    const existingAgent = await this.agentRepository.getAgentById(user.tenantId, agentId);
    if (!existingAgent) {
      throw new CustomError(`Agent with ID ${agentId} not found.`, { originalStatusCode: 404 });
    }

    if (!authorizationService.canAccessResource(user, { ownerId: existingAgent.ownerId, organizationId: existingAgent.tenantId, teamId: existingAgent.teamId }, 'read')) {
      throw new AuthorizationError('Forbidden: You do not have access to view performance for this specific agent.', 403);
    }

    return this.agentRepository.getAgentPerformanceMetrics(user.tenantId, agentId, startDate, endDate);
  }

  // Agent-PAIM integration and permission handling (further implementation needed)
  // Agent runtime engine with execution context (further implementation needed)
  // Workflow orchestration and task scheduling (further implementation needed)
  // Agent state management and persistence (handled by repository, but service might add logic)
  // Agent communication and coordination (further implementation needed)
  // Resource management and limits (further implementation needed)
  // Integration with external AI services (further implementation needed)
}